"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\");\n/* harmony import */ var _assets_images_icons_instagram_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/instagram.svg */ \"(app-pages-browser)/./src/assets/images/icons/instagram.svg\");\n/* harmony import */ var _assets_images_icons_linkedin_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/linkedin.svg */ \"(app-pages-browser)/./src/assets/images/icons/linkedin.svg\");\n/* harmony import */ var _assets_images_icons_facebook_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/facebook.svg */ \"(app-pages-browser)/./src/assets/images/icons/facebook.svg\");\n/* harmony import */ var _assets_images_icons_x_svg__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/assets/images/icons/x.svg */ \"(app-pages-browser)/./src/assets/images/icons/x.svg\");\n/* harmony import */ var _assets_images_website_PentabellOfficesIcon_svg__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/assets/images/website/PentabellOfficesIcon.svg */ \"(app-pages-browser)/./src/assets/images/website/PentabellOfficesIcon.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst GlossaryDetails = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function GlossaryDetails(param) {\n    let { article, language, isMobileSSR } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(theme.breakpoints.down(\"sm\")) || isMobileSSR;\n    const breadcrumbSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"BreadcrumbList\",\n            itemListElement: [\n                {\n                    \"@type\": \"ListItem\",\n                    position: 1,\n                    item: {\n                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                        name: \"Glossary\"\n                    }\n                }\n            ]\n        }), [\n        language\n    ]);\n    const glossaryPath = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>language === \"en\" ? \"/glossaries\" : \"/\".concat(language, \"/glossaries\"), [\n        language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page-details\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    type: \"application/ld+json\",\n                    dangerouslySetInnerHTML: {\n                        __html: JSON.stringify(breadcrumbSchema)\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"container\",\n                    container: true,\n                    columnSpacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            item: true,\n                            sm: 12,\n                            md: 12,\n                            lg: 9,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"glossary-header\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"custom-max-width\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glossary-path\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        locale: language === \"en\" ? \"en\" : \"fr\",\n                                                        href: \"\".concat(glossaryPath, \"/\"),\n                                                        className: \"link\",\n                                                        children: \"Glossary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (article === null || article === void 0 ? void 0 : article.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                                lineNumber: 67,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"word\",\n                                                                children: article.word\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                                lineNumber: 68,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"letter\",\n                                                children: article === null || article === void 0 ? void 0 : article.letter\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"custom-max-width\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            htmlContent: article === null || article === void 0 ? void 0 : article.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"pentabell-company\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"title\",\n                                                            children: \"Pentabell Company\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                            lineNumber: 79,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"description\",\n                                                            children: [\n                                                                \"Absenteeism is the persistent absence of individuals from work, usually without a valid or authorized reason. \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                                    lineNumber: 82,\n                                                                    columnNumber: 73\n                                                                }, this),\n                                                                \"In most instances, employee absenteeism is characterized by repeated and intentional failure to fulfill obligations which can lead to decreased.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                            lineNumber: 80,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"btn btn-filled-yellow\",\n                                                            children: \"Contact us\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pentabell-offices-icon\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_website_PentabellOfficesIcon_svg__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            item: true,\n                            sm: 12,\n                            md: 12,\n                            lg: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"custom-max-width\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glossary-social-media-icons\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_facebook_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_linkedin_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_x_svg__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_instagram_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}, \"PjPdiaLrIseGsZR0cSMWC88IBLw=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        _barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    ];\n})), \"PjPdiaLrIseGsZR0cSMWC88IBLw=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        _barrel_optimize_names_Button_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    ];\n});\n_c1 = GlossaryDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryDetails);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlossaryDetails$memo\");\n$RefreshReg$(_c1, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});