import { memo } from "react";

const GlossaryLoading = memo(function GlossaryLoading() {
  return (
    <div className="glossary-loading">
      <div className="loading-header">
        <div className="loading-breadcrumb">
          <div className="loading-skeleton loading-text-sm"></div>
          <div className="loading-skeleton loading-text-sm"></div>
        </div>
      </div>
      <div className="loading-content">
        <div className="loading-skeleton loading-title"></div>
        <div className="loading-skeleton loading-text"></div>
        <div className="loading-skeleton loading-text"></div>
        <div className="loading-skeleton loading-text-short"></div>
      </div>
      <style jsx>{`
        .glossary-loading {
          padding: 20px;
          max-width: 1200px;
          margin: 0 auto;
        }
        
        .loading-header {
          margin-bottom: 30px;
        }
        
        .loading-breadcrumb {
          display: flex;
          gap: 10px;
          align-items: center;
          margin-bottom: 20px;
        }
        
        .loading-content {
          max-width: 800px;
        }
        
        .loading-skeleton {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: loading 1.5s infinite;
          border-radius: 4px;
          margin-bottom: 12px;
        }
        
        .loading-text-sm {
          height: 16px;
          width: 80px;
        }
        
        .loading-title {
          height: 32px;
          width: 60%;
          margin-bottom: 20px;
        }
        
        .loading-text {
          height: 16px;
          width: 100%;
        }
        
        .loading-text-short {
          height: 16px;
          width: 70%;
        }
        
        @keyframes loading {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
      `}</style>
    </div>
  );
});

export default GlossaryLoading;
