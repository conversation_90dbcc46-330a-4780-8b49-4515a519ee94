"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createClientSchema = exports.createManagerSchema = void 0;
const constants_1 = require("@/utils/helpers/constants");
const joi_1 = __importDefault(require("joi"));
const createManagerSchema = joi_1.default.object({
    firstName: joi_1.default.string().required(),
    lastName: joi_1.default.string().required(),
    email: joi_1.default.string().email(),
    phone: joi_1.default.string(),
    country: joi_1.default.valid(...Object.values(constants_1.Countries)).required(),
    jobPosition: joi_1.default.string(),
    client: joi_1.default.string().required(),
});
exports.createManagerSchema = createManagerSchema;
const createClientSchema = joi_1.default.object({
    name: joi_1.default.string().required(),
    hidden: joi_1.default.boolean(),
    industry: joi_1.default.valid(...Object.values(constants_1.Industry)).required(),
    logo: joi_1.default.string()
});
exports.createClientSchema = createClientSchema;
//# sourceMappingURL=client.validations.js.map