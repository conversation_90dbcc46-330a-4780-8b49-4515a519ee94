"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.robotsMeta = exports.Visibility = exports.Language = void 0;
const constants_1 = require("@/utils/helpers/constants");
Object.defineProperty(exports, "Language", { enumerable: true, get: function () { return constants_1.Language; } });
Object.defineProperty(exports, "Visibility", { enumerable: true, get: function () { return constants_1.Visibility; } });
Object.defineProperty(exports, "robotsMeta", { enumerable: true, get: function () { return constants_1.robotsMeta; } });
//# sourceMappingURL=article.interface.js.map