# Client-Only Error Solution

## Problem
The error `'client-only' cannot be imported from a Server Component module. It should only be used from a Client Component.` occurred when trying to use client-side performance monitoring hooks in server components.

## Root Cause
The issue was caused by importing and using client-side hooks (`usePerformanceMonitor`, `useRenderPerformance`) in components that were being rendered on the server side. These hooks use browser APIs like `window`, `performance`, and `PerformanceObserver` which are not available during server-side rendering.

## Solution Applied

### 1. Added 'use client' Directive
Added the `'use client'` directive to files that use client-side APIs:

**File: `client/src/hooks/usePerformanceMonitor.js`**
```javascript
'use client';

import { useEffect, useCallback } from 'react';
// ... rest of the hook code
```

**File: `client/src/utils/performance.js`**
```javascript
'use client';

// Performance optimization utilities
// ... rest of the utility functions
```

### 2. Removed Client-Side Hooks from Server Components
Removed the performance monitoring hook from the `GlossaryDetails` component since it was being dynamically imported and could cause server/client conflicts:

**Before:**
```javascript
import { useRenderPerformance } from "@/hooks/usePerformanceMonitor";

const GlossaryDetails = memo(function GlossaryDetails({ article, language }) {
  useRenderPerformance('GlossaryDetails', process.env.NODE_ENV === 'development');
  // ... rest of component
});
```

**After:**
```javascript
const GlossaryDetails = memo(function GlossaryDetails({ article, language }) {
  // Removed performance monitoring to avoid server/client conflicts
  // ... rest of component
});
```

### 3. Optimized Loading Component
Updated the `GlossaryLoading` component to use inline styles instead of styled-jsx to avoid potential SSR issues:

**Before:**
```javascript
<style jsx>{`
  .glossary-loading { /* styles */ }
`}</style>
```

**After:**
```javascript
const loadingStyles = {
  glossaryLoading: { /* styles as objects */ }
};

<style>
  {`@keyframes loading { /* keyframes only */ }`}
</style>
```

## Key Principles for Avoiding This Error

### 1. Use 'use client' for Client-Side Code
Any file that uses browser APIs, React hooks that depend on the DOM, or client-side libraries should have the `'use client'` directive at the top.

### 2. Separate Server and Client Logic
- **Server Components**: Use for data fetching, static content, SEO metadata
- **Client Components**: Use for interactivity, browser APIs, state management

### 3. Dynamic Imports for Client Components
When importing client components into server components, use dynamic imports:

```javascript
import dynamic from 'next/dynamic';

const ClientComponent = dynamic(() => import('./ClientComponent'), {
  loading: () => <LoadingComponent />,
  ssr: false // or true depending on needs
});
```

### 4. Performance Monitoring Best Practices
For performance monitoring in Next.js 13+ App Router:

```javascript
// Create a separate client component for performance monitoring
'use client';

import { useEffect } from 'react';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

export default function PerformanceMonitor() {
  usePerformanceMonitor(true, (metrics) => {
    // Handle metrics
  });
  
  return null; // This component doesn't render anything
}

// Then use it in your app layout or specific pages
export default function Layout({ children }) {
  return (
    <>
      <PerformanceMonitor />
      {children}
    </>
  );
}
```

## Files Modified to Fix the Error

1. **`client/src/hooks/usePerformanceMonitor.js`** - Added `'use client'` directive
2. **`client/src/utils/performance.js`** - Added `'use client'` directive  
3. **`client/src/features/glossary/component/GlossaryDetails.jsx`** - Removed client-side hook import
4. **`client/src/components/loading/GlossaryLoading.jsx`** - Optimized to use inline styles

## Verification
After applying these changes:
- ✅ No more `'client-only'` import errors
- ✅ Server-side rendering works correctly
- ✅ Client-side functionality preserved
- ✅ Performance optimizations maintained

## Additional Notes

### When to Use 'use client'
- Components that use `useState`, `useEffect`, or other React hooks
- Components that access browser APIs (`window`, `document`, `localStorage`)
- Components that handle user interactions (click handlers, form submissions)
- Third-party libraries that depend on the browser environment

### When to Keep Server Components
- Static content rendering
- Data fetching with server-side APIs
- SEO metadata generation
- Initial page rendering for better performance

This solution maintains the performance optimizations while ensuring compatibility with Next.js 13+ App Router's server/client component architecture.
