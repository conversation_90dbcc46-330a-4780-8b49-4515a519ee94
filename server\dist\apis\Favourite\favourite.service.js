"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const opportunity_model_1 = __importDefault(require("../opportunity/model/opportunity.model"));
const user_model_1 = __importDefault(require("../user/user.model"));
const article_model_1 = __importDefault(require("@/apis/article/article.model"));
const favourite_model_1 = __importDefault(require("./favourite.model"));
const constants_1 = require("@/utils/helpers/constants");
class FavouriteService {
    constructor() {
        this.Opportunity = opportunity_model_1.default;
        this.Article = article_model_1.default;
        this.User = user_model_1.default;
        this.Favourite = favourite_model_1.default;
    }
    async addToFavourite(favouriteId, favouriteType, currentUser) {
        let item;
        if (favouriteType === 'opportunity') {
            item = await this.Opportunity.findById(favouriteId);
        }
        else if (favouriteType === 'article') {
            item = await this.Article.findById(favouriteId);
        }
        if (!item) {
            throw new http_exception_1.default(404, `${favouriteType} not found`);
        }
        const existingFavourite = await this.Favourite.findOne({
            user: currentUser._id,
            favouriteType: favouriteType,
            favouriteId: favouriteId,
        });
        if (existingFavourite) {
            throw new http_exception_1.default(409, `This ${favouriteType} is already in your favourites`);
        }
        const newFavourite = await this.Favourite.create({
            user: currentUser._id,
            favouriteType: favouriteType,
            favouriteId: favouriteId,
        });
        return newFavourite;
    }
    async getAllFavourites(queries, currentUserId) {
        const { paginated, keyWord, industry, genre, createdAt, country, sortOrder, isPublished, pageNumber: queryPageNumber, pageSize: queryPageSize, typeOfFavourite, } = queries;
        const pageNumber = Number(queryPageNumber) || 1;
        const pageSize = Number(queryPageSize) || 8;
        const userFavourites = await this.Favourite.find({ user: currentUserId }).lean();
        if (!userFavourites || userFavourites.length === 0) {
            return { totalFavourites: 0, favourites: [], pageNumber, totalPages: 0, totalFavouriteOpportunities: 0, totalFavouriteArticles: 0 };
        }
        let totalItems = 0;
        let totalPages = 0;
        let favourites = [];
        const favouriteType = typeOfFavourite === constants_1.FavouriteType.ARTICLE ? constants_1.FavouriteType.ARTICLE : constants_1.FavouriteType.OPPORTUNITY;
        if (favouriteType === 'opportunity') {
            const opportunityIds = userFavourites.filter(fav => fav.favouriteType === 'opportunity').map(fav => fav.favouriteId);
            const queryConditions = {};
            if (isPublished !== undefined)
                queryConditions.$or = [{ 'versions.fr.isPublished': true }, { 'versions.en.isPublished': true }];
            if (createdAt) {
                const date = new Date(createdAt);
                queryConditions['createdAt'] = { $gte: date.toISOString(), $lte: new Date().toISOString() };
            }
            if (genre)
                queryConditions['genre'] = genre;
            if (industry)
                queryConditions['industry'] = industry;
            if (country)
                queryConditions['country'] = country;
            const shortlistCondition = { _id: { $in: opportunityIds } };
            const opportunitySearchRequest = keyWord
                ? { $text: { $search: keyWord }, ...shortlistCondition, ...queryConditions }
                : { ...shortlistCondition, ...queryConditions };
            const sortCriteria = {};
            if (sortOrder) {
                sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
            }
            totalItems = await this.Opportunity.countDocuments(opportunitySearchRequest);
            totalPages = Math.ceil(totalItems / pageSize);
            favourites = await this.Opportunity.find(opportunitySearchRequest)
                .sort(sortCriteria)
                .select('versions industry country status reference createdAt dateOfExpiration')
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize);
        }
        else if (favouriteType === 'article') {
            // Use the article's main _id, not the version's _id
            const articleIds = userFavourites.filter(fav => fav.favouriteType === 'article').map(fav => fav.favouriteId);
            totalItems = await this.Article.countDocuments({
                _id: { $in: articleIds }, // Filter by the article's main _id
            });
            totalPages = Math.ceil(totalItems / pageSize);
            favourites = await this.Article.find({
                _id: { $in: articleIds }, // Same here, query using the article's _id
            })
                .select('versions') // Retrieve the versions array of each article
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize)
                .lean();
        }
        const totalFavouriteOpportunities = userFavourites.filter(fav => fav.favouriteType === 'opportunity').length;
        const totalFavouriteArticles = userFavourites.filter(fav => fav.favouriteType === 'article').length;
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalFavourites: totalItems,
            favourites,
            totalFavouriteOpportunities,
            totalFavouriteArticles,
        };
    }
    async removeFavourite(currentUserId, favouriteId, favouriteType) {
        try {
            if (!currentUserId) {
                throw new http_exception_1.default(400, 'User ID is required.');
            }
            if (!favouriteId) {
                throw new http_exception_1.default(400, 'Favourite ID is required.');
            }
            if (!favouriteType) {
                throw new http_exception_1.default(400, 'Favourite Type is required.');
            }
            const existingFavourite = await this.Favourite.findOne({
                user: currentUserId,
                favouriteId,
                favouriteType,
            });
            if (!existingFavourite) {
                throw new http_exception_1.default(404, 'Favourite item not found or already deleted.');
            }
            const result = await this.Favourite.deleteOne({
                user: currentUserId,
                favouriteId,
                favouriteType,
            });
            if (result.deletedCount === 0) {
                throw new http_exception_1.default(500, 'Failed to delete favourite item.');
            }
            console.log(`Successfully removed favourite item with ID: ${favouriteId}`);
        }
        catch (error) {
            if (error instanceof http_exception_1.default) {
                console.error(`Error: ${error.message}`);
                throw error;
            }
            else {
                console.error('Unexpected error while removing favourite item:', error);
                throw new http_exception_1.default(500, 'An unexpected error occurred.');
            }
        }
    }
    async isItemSaved(itemId, currentUserId) {
        const isSaved = await this.Favourite.exists({
            user: currentUserId,
            favouriteId: itemId,
        });
        return isSaved === null ? false : true;
    }
}
exports.default = FavouriteService;
//# sourceMappingURL=favourite.service.js.map