"use client";

import { useTranslation } from "react-i18next";

import AuthLayout from "@/components/layouts/AuthLayout";
import Activationandconfirm from "../../../../features/application/component/Activationandconfirm";

const page = () => {
  const { t } = useTranslation();
  return (
    <AuthLayout id="auth-layout" >
      <Activationandconfirm t={t} />
    </AuthLayout>
  );
};

export default page;
