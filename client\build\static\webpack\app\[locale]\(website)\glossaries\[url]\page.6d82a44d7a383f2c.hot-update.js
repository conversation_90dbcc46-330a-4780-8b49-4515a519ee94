"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var moment_locale_fr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! moment/locale/fr */ \"(app-pages-browser)/./node_modules/moment/locale/fr.js\");\n/* harmony import */ var moment_locale_fr__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(moment_locale_fr__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction GlossaryDetails(param) {\n    let { id, article, language, url } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    moment__WEBPACK_IMPORTED_MODULE_2___default().locale(i18n.language || \"en\");\n    const [modifiedHtmlContent, setModifiedHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(article === null || article === void 0 ? void 0 : article.content);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page-details\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"glossary-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"custom-max-width\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"categories-path\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    locale: language === \"en\" ? \"en\" : \"fr\",\n                                    href: \"\".concat(language === \"en\" ? \"/glossary\" : \"/\".concat(language, \"/glossary\"), \"/\"),\n                                    className: \"link\",\n                                    children: \"Glossary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 15\n                                }, this),\n                                (article === null || article === void 0 ? void 0 : article.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"word\",\n                                            children: article === null || article === void 0 ? void 0 : article.word\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    type: \"application/ld+json\",\n                    dangerouslySetInnerHTML: {\n                        __html: JSON.stringify({\n                            \"@context\": \"https://schema.org\",\n                            \"@type\": \"BreadcrumbList\",\n                            itemListElement: [\n                                {\n                                    \"@type\": \"ListItem\",\n                                    position: 1,\n                                    item: {\n                                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                                        name: \"Glossary\"\n                                    }\n                                }\n                            ]\n                        })\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"custom-max-width\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"container\",\n                        container: true,\n                        columnSpacing: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glossary-content\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    htmlContent: modifiedHtmlContent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(GlossaryDetails, \"cN5jiYE7cGZdAPLstTP88kVffFw=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    ];\n});\n_c = GlossaryDetails;\nvar _c;\n$RefreshReg$(_c, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9nbG9zc2FyeS9jb21wb25lbnQvR2xvc3NhcnlEZXRhaWxzLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM0QztBQUNoQjtBQUNvQjtBQUNRO0FBQzlCO0FBQ3FCO0FBRW1CO0FBQ0Q7QUFFbEQsU0FBU1UsZ0JBQWdCLEtBQThCO1FBQTlCLEVBQUVDLEVBQUUsRUFBRUMsT0FBTyxFQUFFQyxRQUFRLEVBQUVDLEdBQUcsRUFBRSxHQUE5Qjs7SUFDdEMsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBR1IsNkRBQWNBO0lBRTVCLE1BQU1TLFFBQVFYLHNHQUFRQTtJQUN0QixNQUFNLEVBQUVZLElBQUksRUFBRSxHQUFHViw2REFBY0E7SUFDL0IsTUFBTVcsV0FBV1osc0dBQWFBLENBQUNVLE1BQU1HLFdBQVcsQ0FBQ0MsSUFBSSxDQUFDO0lBRXREbEIsb0RBQWEsQ0FBQ2UsS0FBS0osUUFBUSxJQUFJO0lBRS9CLE1BQU0sQ0FBQ1MscUJBQXFCQyx1QkFBdUIsR0FBR3RCLCtDQUFRQSxDQUM1RFcsb0JBQUFBLDhCQUFBQSxRQUFTWSxPQUFPO0lBR2xCLHFCQUNFO2tCQUNFLDRFQUFDQztZQUFJZCxJQUFHOzs4QkFDTiw4REFBQ2M7b0JBQUlkLElBQUc7OEJBQ04sNEVBQUNSLDBGQUFTQTt3QkFBQ3VCLFdBQVU7a0NBQ25CLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNDO29DQUNDTixRQUFRUixhQUFhLE9BQU8sT0FBTztvQ0FDbkNlLE1BQU0sR0FFTCxPQURDZixhQUFhLE9BQVEsY0FBYSxJQUFhLE9BQVRBLFVBQVMsY0FDaEQ7b0NBQ0RhLFdBQVU7OENBQ1g7Ozs7OztnQ0FHQWQsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTaUIsSUFBSSxtQkFDWjs7c0RBQ0UsOERBQUNwQiwyRUFBYUE7Ozs7O3NEQUNkLDhEQUFDcUI7NENBQUVKLFdBQVU7c0RBQVFkLG9CQUFBQSw4QkFBQUEsUUFBU2lCLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNNUMsOERBQUNFO29CQUNDQyxNQUFLO29CQUNMQyx5QkFBeUI7d0JBQ3ZCQyxRQUFRQyxLQUFLQyxTQUFTLENBQUM7NEJBQ3JCLFlBQVk7NEJBQ1osU0FBUzs0QkFDVEMsaUJBQWlCO2dDQUNmO29DQUNFLFNBQVM7b0NBQ1RDLFVBQVU7b0NBQ1ZDLE1BQU07d0NBQ0osT0FDRTFCLGFBQWEsT0FDUiwwQ0FDRCw2QkFBc0MsT0FBVEEsVUFBUzt3Q0FDNUMyQixNQUFNO29DQUNSO2dDQUNGOzZCQUNEO3dCQUNIO29CQUNGOzs7Ozs7OEJBRUYsOERBQUNyQywwRkFBU0E7b0JBQUN1QixXQUFVOzhCQUNuQiw0RUFBQ3RCLDJGQUFJQTt3QkFBQ3NCLFdBQVU7d0JBQVllLFNBQVM7d0JBQUNDLGVBQWU7a0NBQ25ELDRFQUFDdEMsMkZBQUlBOzRCQUFDbUMsSUFBSTs0QkFBQ0ksSUFBSTs0QkFBSUMsSUFBSTtzQ0FDckIsNEVBQUNuQjtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2xCLHVFQUFjQTtvQ0FBQ3FDLGFBQWF2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVTdDO0dBekV3Qlo7O1FBQ1JILHlEQUFjQTtRQUVkRixrR0FBUUE7UUFDTEUseURBQWNBO1FBQ2RELGtHQUFhQTs7O0tBTFJJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9nbG9zc2FyeS9jb21wb25lbnQvR2xvc3NhcnlEZXRhaWxzLmpzeD8xMzM2Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBtb21lbnQgZnJvbSBcIm1vbWVudFwiO1xyXG5pbXBvcnQgeyBDb250YWluZXIsIEdyaWQgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyB1c2VUaGVtZSwgdXNlTWVkaWFRdWVyeSB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCBcIm1vbWVudC9sb2NhbGUvZnJcIjtcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tIFwicmVhY3QtaTE4bmV4dFwiO1xyXG5cclxuaW1wb3J0IEFydGljbGVDb250ZW50IGZyb20gXCIuLi8uLi9ibG9nL2NvbXBvbmVudHMvQXJ0aWNsZUNvbnRlbnRcIjtcclxuaW1wb3J0IFN2Z0Fycm93UmlnaHQgZnJvbSBcIkAvYXNzZXRzL2ltYWdlcy9pY29ucy9hcnJvd1JpZ2h0LnN2Z1wiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gR2xvc3NhcnlEZXRhaWxzKHsgaWQsIGFydGljbGUsIGxhbmd1YWdlLCB1cmwgfSkge1xyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKTtcclxuXHJcbiAgY29uc3QgdGhlbWUgPSB1c2VUaGVtZSgpO1xyXG4gIGNvbnN0IHsgaTE4biB9ID0gdXNlVHJhbnNsYXRpb24oKTtcclxuICBjb25zdCBpc01vYmlsZSA9IHVzZU1lZGlhUXVlcnkodGhlbWUuYnJlYWtwb2ludHMuZG93bihcInNtXCIpKTtcclxuXHJcbiAgbW9tZW50LmxvY2FsZShpMThuLmxhbmd1YWdlIHx8IFwiZW5cIik7XHJcblxyXG4gIGNvbnN0IFttb2RpZmllZEh0bWxDb250ZW50LCBzZXRNb2RpZmllZEh0bWxDb250ZW50XSA9IHVzZVN0YXRlKFxyXG4gICAgYXJ0aWNsZT8uY29udGVudFxyXG4gICk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8ZGl2IGlkPVwiZ2xvc3NhcnktcGFnZS1kZXRhaWxzXCI+XHJcbiAgICAgICAgPGRpdiBpZD1cImdsb3NzYXJ5LWhlYWRlclwiPlxyXG4gICAgICAgICAgPENvbnRhaW5lciBjbGFzc05hbWU9XCJjdXN0b20tbWF4LXdpZHRoXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2F0ZWdvcmllcy1wYXRoXCI+XHJcbiAgICAgICAgICAgICAgPGFcclxuICAgICAgICAgICAgICAgIGxvY2FsZT17bGFuZ3VhZ2UgPT09IFwiZW5cIiA/IFwiZW5cIiA6IFwiZnJcIn1cclxuICAgICAgICAgICAgICAgIGhyZWY9e2Ake1xyXG4gICAgICAgICAgICAgICAgICBsYW5ndWFnZSA9PT0gXCJlblwiID8gYC9nbG9zc2FyeWAgOiBgLyR7bGFuZ3VhZ2V9L2dsb3NzYXJ5YFxyXG4gICAgICAgICAgICAgICAgfS9gfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibGlua1wiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgR2xvc3NhcnlcclxuICAgICAgICAgICAgICA8L2E+XHJcbiAgICAgICAgICAgICAge2FydGljbGU/LndvcmQgJiYgKFxyXG4gICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgPFN2Z0Fycm93UmlnaHQgLz5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwid29yZFwiPnthcnRpY2xlPy53b3JkfTwvcD5cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9Db250YWluZXI+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPHNjcmlwdFxyXG4gICAgICAgICAgdHlwZT1cImFwcGxpY2F0aW9uL2xkK2pzb25cIlxyXG4gICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcclxuICAgICAgICAgICAgX19odG1sOiBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgICAgICAgXCJAY29udGV4dFwiOiBcImh0dHBzOi8vc2NoZW1hLm9yZ1wiLFxyXG4gICAgICAgICAgICAgIFwiQHR5cGVcIjogXCJCcmVhZGNydW1iTGlzdFwiLFxyXG4gICAgICAgICAgICAgIGl0ZW1MaXN0RWxlbWVudDogW1xyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICBcIkB0eXBlXCI6IFwiTGlzdEl0ZW1cIixcclxuICAgICAgICAgICAgICAgICAgcG9zaXRpb246IDEsXHJcbiAgICAgICAgICAgICAgICAgIGl0ZW06IHtcclxuICAgICAgICAgICAgICAgICAgICBcIkBpZFwiOlxyXG4gICAgICAgICAgICAgICAgICAgICAgbGFuZ3VhZ2UgPT09IFwiZW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGBodHRwczovL3d3dy5wZW50YWJlbGwuY29tL2dsb3NzYXJpZXMvYFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGBodHRwczovL3d3dy5wZW50YWJlbGwuY29tLyR7bGFuZ3VhZ2V9L2dsb3NzYXJpZXMvYCxcclxuICAgICAgICAgICAgICAgICAgICBuYW1lOiBcIkdsb3NzYXJ5XCIsXHJcbiAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAvPlxyXG4gICAgICAgIDxDb250YWluZXIgY2xhc3NOYW1lPVwiY3VzdG9tLW1heC13aWR0aFwiPlxyXG4gICAgICAgICAgPEdyaWQgY2xhc3NOYW1lPVwiY29udGFpbmVyXCIgY29udGFpbmVyIGNvbHVtblNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezh9PlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ2xvc3NhcnktY29udGVudFwiPlxyXG4gICAgICAgICAgICAgICAgPEFydGljbGVDb250ZW50IGh0bWxDb250ZW50PXttb2RpZmllZEh0bWxDb250ZW50fSAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICB7LyogPEdyaWQgY2xhc3NOYW1lPVwiY29udGFpbmVyXCIgY29udGFpbmVyIGNvbHVtblNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgPC9HcmlkPiAqL31cclxuICAgICAgICA8L0NvbnRhaW5lcj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8Lz5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIm1vbWVudCIsIkNvbnRhaW5lciIsIkdyaWQiLCJ1c2VUaGVtZSIsInVzZU1lZGlhUXVlcnkiLCJ1c2VUcmFuc2xhdGlvbiIsIkFydGljbGVDb250ZW50IiwiU3ZnQXJyb3dSaWdodCIsIkdsb3NzYXJ5RGV0YWlscyIsImlkIiwiYXJ0aWNsZSIsImxhbmd1YWdlIiwidXJsIiwidCIsInRoZW1lIiwiaTE4biIsImlzTW9iaWxlIiwiYnJlYWtwb2ludHMiLCJkb3duIiwibG9jYWxlIiwibW9kaWZpZWRIdG1sQ29udGVudCIsInNldE1vZGlmaWVkSHRtbENvbnRlbnQiLCJjb250ZW50IiwiZGl2IiwiY2xhc3NOYW1lIiwiYSIsImhyZWYiLCJ3b3JkIiwicCIsInNjcmlwdCIsInR5cGUiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsIkpTT04iLCJzdHJpbmdpZnkiLCJpdGVtTGlzdEVsZW1lbnQiLCJwb3NpdGlvbiIsIml0ZW0iLCJuYW1lIiwiY29udGFpbmVyIiwiY29sdW1uU3BhY2luZyIsInhzIiwic20iLCJodG1sQ29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});