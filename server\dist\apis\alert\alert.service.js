"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlertService = void 0;
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const messages_1 = require("@/utils/helpers/messages");
const alert_model_1 = __importDefault(require("./alert.model"));
const user_model_1 = __importDefault(require("../user/user.model"));
const alert_model_2 = __importDefault(require("./alert.model"));
const notification_model_1 = __importDefault(require("../notifications/notification.model"));
const settings_model_1 = __importDefault(require("../settings/settings.model"));
class AlertService {
    constructor() {
        this.Alert = alert_model_1.default;
    }
    async create(alertData, currentUser) {
        const existingAlert = await this.Alert.findOne({
            createdBy: currentUser._id,
            isActive: true,
        }).lean();
        if (existingAlert) {
            throw new http_exception_1.default(400, 'You can only have one active alert.');
        }
        const createdAlert = await this.Alert.create({
            ...alertData,
            createdBy: currentUser._id,
            isActive: true,
        });
        return createdAlert.toObject();
    }
    async getAlerts(currentUser) {
        const alerts = await this.Alert.find({
            createdBy: currentUser._id,
            isActive: true
        });
        return alerts.map(alert => alert.toObject());
    }
    async get(alertId) {
        const alert = await this.Alert.findById(alertId).lean();
        if (!alert) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.ALERT.NOT_FOUND);
        }
        return alert;
    }
    async toggle(alertId, status) {
        await this.get(alertId);
        await alert_model_2.default.findByIdAndUpdate(alertId, { isActive: status });
    }
    async getById(id) {
        const alert = await alert_model_2.default.findById(id).lean();
        if (!alert)
            throw new http_exception_1.default(404, 'alert not found');
        return alert;
    }
    async update(currentUser, id, alertData) {
        const alert = await alert_model_2.default.findById(id).lean();
        if (!alert) {
            throw new http_exception_1.default(404, 'Alert not found');
        }
        if (alert.createdBy.toString() !== currentUser._id.toString()) {
            throw new http_exception_1.default(403, 'You are not authorized to update this alert');
        }
        const updatedAlert = await this.Alert.findByIdAndUpdate(id, { ...alertData }, { new: true }).lean();
        return updatedAlert;
    }
    async addAlertToAllUsers() {
        try {
            const usersWithoutAlerts = await user_model_1.default.find({
                $or: [
                    { alerts: { $exists: false } },
                    { alerts: null }
                ]
            });
            if (usersWithoutAlerts.length === 0) {
                return;
            }
            for (const user of usersWithoutAlerts) {
                if (!user.alerts || user.alerts === null) {
                    console.log(`Création d'une alerte pour l'utilisateur avec ID: ${user._id}`);
                    const alert = new alert_model_2.default({
                        createdBy: user._id,
                        industry: [],
                        title: '',
                        country: [],
                        isActive: true,
                        frequence: 'weekly',
                    });
                    const savedAlert = await alert.save();
                    await user_model_1.default.updateOne({ _id: user._id }, { $set: { alerts: savedAlert._id } });
                }
                else {
                    console.log(`L'utilisateur ${user._id} a déjà une alerte.`);
                }
            }
            console.log("Tous les utilisateurs ont été mis à jour.");
        }
        catch (error) {
            console.error('Erreur lors de la création et de l\'assignation de l\'alerte:', error);
        }
    }
    async addNotificationsto() {
        try {
            const usersWithoutAlerts = await user_model_1.default.find();
            console.log(`Total utilisateurs sans alerte : ${usersWithoutAlerts.length}`);
            if (usersWithoutAlerts.length === 0) {
                console.log("Aucun utilisateur sans alerte, rien à faire.");
                return;
            }
            for (const user of usersWithoutAlerts) {
                console.log(`Création d'une alerte pour l'utilisateur avec ID: ${user._id}`);
                const alert = new notification_model_1.default({
                    receiver: user._id,
                    sender: null,
                    message: "Welcome to Pentabell,we are happy to see you here!",
                    link: `${process.env.LOGIN_LINK}`,
                });
                const savedAlert = await alert.save();
                console.log(`L'utilisateur ${user._id} a déjà une alerte.`);
            }
            console.log("Tous les utilisateurs ont été mis à jour.");
        }
        catch (error) {
            console.error('Erreur lors de la création et de l\'assignation de l\'alerte:', error);
        }
    }
    async addsettings() {
        try {
            const usersWithoutAlerts = await user_model_1.default.find();
            console.log(`Total utilisateurs sans alerte : ${usersWithoutAlerts.length}`);
            if (usersWithoutAlerts.length === 0) {
                console.log("Aucun utilisateur sans alerte, rien à faire.");
                return;
            }
            for (const user of usersWithoutAlerts) {
                console.log(`Création d'une alerte pour l'utilisateur avec ID: ${user._id}`);
                const alert = new settings_model_1.default({
                    user: user._id,
                    notifications: {
                        newJobAlerts: {
                            email: true,
                            website: true,
                        },
                        appliedJobStatusUpdates: {
                            email: true,
                            website: true,
                        },
                        newsLetter: {
                            email: true,
                            website: true,
                        },
                    },
                });
                const savedAlert = await alert.save();
                console.log(`L'utilisateur ${user._id} a déjà une alerte.`);
            }
            console.log("Tous les utilisateurs ont été mis à jour.");
        }
        catch (error) {
            console.error('Erreur lors de la création et de l\'assignation de l\'alerte:', error);
        }
    }
    /*
        // public async updateUsersAlertsToNull(): Promise<void> {
        //     try {
               
        //         const updateResult = await userModel.updateMany(
                  
        //             {
        //                 $set: { alerts: null }
        //             }
        //         );
        
        //         console.log(`${updateResult.modifiedCount} utilisateurs ont été mis à jour avec alerts = null`);
        //     } catch (error) {
        //         console.error('Erreur lors de la mise à jour des utilisateurs avec alerts null:', error);
        //     }
        // } */
    async removeAlertFromAllUsers() {
        try {
            const updateResult = await user_model_1.default.updateMany({}, { $set: { alerts: null } });
            console.log(`Nombre d'utilisateurs mis à jour : ${updateResult.modifiedCount}`);
            if (updateResult.modifiedCount > 0) {
                console.log('Alertes supprimées de tous les utilisateurs.');
            }
            else {
                console.log('Aucune alerte n\'a été supprimée.');
            }
        }
        catch (error) {
            console.error('Erreur lors de la suppression des alertes des utilisateurs:', error);
        }
    }
    async getAll(queries) {
        const { paginated, industry, createdAt, sortOrder, createdBy, isActive } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 8;
        const queryConditions = {};
        if (createdAt) {
            const date = new Date(createdAt);
            const nextYear = date.getFullYear() + 1;
            const antDate = new Date(nextYear, 0, 0, 24, 59, 59, 999);
            queryConditions['createdAt'] = {
                $gte: date.toISOString(),
                $lte: antDate.toISOString(),
            };
        }
        if (industry) {
            queryConditions['industry'] = industry;
        }
        if (createdBy) {
            queryConditions['createdBy'] = createdBy;
        }
        if (isActive) {
            queryConditions['isActive'] = isActive;
        }
        if (paginated === 'false') {
            const alerts = await this.Alert.find(queryConditions)
                .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
                .select('industry country isActive createdAt')
                .lean();
            return { alerts: alerts };
        }
        const totalAlerts = await this.Alert.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalAlerts / pageSize);
        const alerts = await this.Alert.find(queryConditions)
            .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
            .select('industry country isActive createdAt')
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .lean();
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalAlerts,
            alerts: alerts,
        };
    }
}
exports.AlertService = AlertService;
//# sourceMappingURL=alert.service.js.map