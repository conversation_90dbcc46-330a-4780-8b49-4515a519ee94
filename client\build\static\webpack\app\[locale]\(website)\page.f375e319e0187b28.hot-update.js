"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/page",{

/***/ "(app-pages-browser)/./src/components/sections/LatestJobOffers.jsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/LatestJobOffers.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Container!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var embla_carousel_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! embla-carousel-react */ \"(app-pages-browser)/./node_modules/embla-carousel-react/esm/embla-carousel-react.esm.js\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var i18n_iso_countries__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! i18n-iso-countries */ \"(app-pages-browser)/./node_modules/i18n-iso-countries/index.js\");\n/* harmony import */ var i18n_iso_countries_langs_en_json__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! i18n-iso-countries/langs/en.json */ \"(app-pages-browser)/./node_modules/i18n-iso-countries/langs/en.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LatestJobOffers(param) {\n    let { language } = param;\n    _s();\n    const OPTIONS = {\n        loop: false,\n        align: \"start\"\n    };\n    const [emblaRef] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(OPTIONS);\n    const [jobOffers, setJobOffers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        urgent: undefined\n    });\n    i18n_iso_countries__WEBPACK_IMPORTED_MODULE_7__.registerLocale(i18n_iso_countries_langs_en_json__WEBPACK_IMPORTED_MODULE_8__);\n    const fetchJobOffers = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_3__.axiosGetJsonSSR.get(\"\".concat(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.baseURL).concat(_utils_urls__WEBPACK_IMPORTED_MODULE_4__.API_URLS.opportunity, \"/urgent\"), {\n                params: {\n                    urgent: query.urgent\n                }\n            });\n            if (response.data && response.data.length > 0) {\n                const fetchedOffers = response.data.map((offer)=>{\n                    var _offer_versions_language, _offer_versions_language1;\n                    return {\n                        id: offer._id,\n                        title: ((_offer_versions_language = offer.versions[language]) === null || _offer_versions_language === void 0 ? void 0 : _offer_versions_language.title) || \"Titre non disponible\",\n                        industry: offer.industry,\n                        country: offer.country,\n                        dateOfExpiration: offer.dateOfExpiration,\n                        minExperience: offer.minExperience,\n                        maxExperience: offer.maxExperience,\n                        existingLanguages: offer.existingLanguages,\n                        reference: offer.reference,\n                        urgent: offer.urgent,\n                        url: (_offer_versions_language1 = offer.versions[language]) === null || _offer_versions_language1 === void 0 ? void 0 : _offer_versions_language1.url\n                    };\n                });\n                setJobOffers(fetchedOffers);\n            } else {\n                setJobOffers([]);\n                setError(\"No job offers available at the moment.\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching job offers:\", err);\n            setError(\"Failed to fetch job offers\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchJobOffers();\n    }, [\n        query\n    ]);\n    const onClickFilter = (data)=>{\n        if (data.urgent !== undefined) {\n            setQuery((prev)=>({\n                    ...prev,\n                    urgent: data.urgent\n                }));\n        } else {\n            setQuery((prev)=>({\n                    ...prev,\n                    urgent: undefined\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        id: \"latest-offers\",\n        className: \"custom-max-width\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"heading-h1 text-center\",\n                children: t(\"homePage:s3:title\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"filter-btns\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        onClick: ()=>onClickFilter({\n                                urgent: !query.urgent\n                            }),\n                        text: t(\"homePage:s3:btu\"),\n                        className: \"\".concat(query.urgent ? \"btn btn-filter selected\" : \"btn btn-outlined\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        text: t(\"homePage:s3:btlast\"),\n                        onClick: ()=>onClickFilter({\n                                urgent: undefined\n                            }),\n                        className: \"\".concat(query.urgent === undefined ? \"btn btn-filter selected\" : \"btn btn-outlined\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"embla\",\n                id: \"jobs__slider\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"embla__viewport\",\n                    ref: emblaRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"embla__container\",\n                        children: jobOffers.map((offer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OpportunityItemByGrid, {\n                                opportunity: opportunity,\n                                language: language\n                            }, key, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                                lineNumber: 112,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"center-div\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    text: t(\"homePage:s3:all\"),\n                    link: \"/\".concat(_helpers_routesList__WEBPACK_IMPORTED_MODULE_6__.websiteRoutesList.opportunities.route),\n                    // onClick={() => onClickFilter({ urgent: undefined })}\n                    className: \"btn btn-filled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(LatestJobOffers, \"3pdFVtrTsKhzPDGwe+Ms3jSedJI=\", false, function() {\n    return [\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c = LatestJobOffers;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LatestJobOffers);\nvar _c;\n$RefreshReg$(_c, \"LatestJobOffers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/LatestJobOffers.jsx\n"));

/***/ })

});