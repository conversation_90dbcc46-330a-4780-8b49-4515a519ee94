"use client";

import { useEffect, useCallback } from "react";

/**
 * Custom hook for monitoring performance metrics
 * @param {boolean} enabled - Whether to enable performance monitoring
 * @param {Function} onMetrics - Callback function to handle metrics
 */
export function usePerformanceMonitor(enabled = true, onMetrics = null) {
  const measureWebVitals = useCallback(() => {
    if (!enabled || typeof window === "undefined") return;

    // Largest Contentful Paint (LCP)
    if ("PerformanceObserver" in window) {
      try {
        const lcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          const lastEntry = entries[entries.length - 1];
          const lcp = lastEntry.startTime;

          if (onMetrics) {
            onMetrics({
              metric: "LCP",
              value: lcp,
              rating:
                lcp < 2500 ? "good" : lcp < 4000 ? "needs-improvement" : "poor",
            });
          }

          if (process.env.NODE_ENV === "development") {
            console.log("LCP:", lcp, "ms");
          }
        });
        lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });

        // First Input Delay (FID)
        const fidObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          entries.forEach((entry) => {
            const fid = entry.processingStart - entry.startTime;

            if (onMetrics) {
              onMetrics({
                metric: "FID",
                value: fid,
                rating:
                  fid < 100 ? "good" : fid < 300 ? "needs-improvement" : "poor",
              });
            }

            if (process.env.NODE_ENV === "development") {
              console.log("FID:", fid, "ms");
            }
          });
        });
        fidObserver.observe({ entryTypes: ["first-input"] });

        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries();
          entries.forEach((entry) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });

          if (onMetrics) {
            onMetrics({
              metric: "CLS",
              value: clsValue,
              rating:
                clsValue < 0.1
                  ? "good"
                  : clsValue < 0.25
                  ? "needs-improvement"
                  : "poor",
            });
          }

          if (process.env.NODE_ENV === "development") {
            console.log("CLS:", clsValue);
          }
        });
        clsObserver.observe({ entryTypes: ["layout-shift"] });

        // Total Blocking Time (TBT) approximation
        const tbtObserver = new PerformanceObserver((entryList) => {
          let tbt = 0;
          const entries = entryList.getEntries();
          entries.forEach((entry) => {
            if (entry.duration > 50) {
              tbt += entry.duration - 50;
            }
          });

          if (onMetrics) {
            onMetrics({
              metric: "TBT",
              value: tbt,
              rating:
                tbt < 200 ? "good" : tbt < 600 ? "needs-improvement" : "poor",
            });
          }

          if (process.env.NODE_ENV === "development") {
            console.log("TBT (approx):", tbt, "ms");
          }
        });
        tbtObserver.observe({ entryTypes: ["longtask"] });
      } catch (error) {
        console.warn("Performance monitoring not supported:", error);
      }
    }
  }, [enabled, onMetrics]);

  const measurePageLoad = useCallback(() => {
    if (!enabled || typeof window === "undefined") return;

    window.addEventListener("load", () => {
      setTimeout(() => {
        if ("performance" in window) {
          const perfData = performance.getEntriesByType("navigation")[0];
          const metrics = {
            "DNS Lookup": perfData.domainLookupEnd - perfData.domainLookupStart,
            "TCP Connection": perfData.connectEnd - perfData.connectStart,
            Request: perfData.responseStart - perfData.requestStart,
            Response: perfData.responseEnd - perfData.responseStart,
            "DOM Processing": perfData.domComplete - perfData.domLoading,
            "Total Load Time": perfData.loadEventEnd - perfData.navigationStart,
            "Time to Interactive":
              perfData.domInteractive - perfData.navigationStart,
            "DOM Content Loaded":
              perfData.domContentLoadedEventEnd - perfData.navigationStart,
          };

          if (onMetrics) {
            Object.entries(metrics).forEach(([metric, value]) => {
              onMetrics({ metric, value, type: "navigation" });
            });
          }

          if (process.env.NODE_ENV === "development") {
            console.table(metrics);
          }
        }
      }, 0);
    });
  }, [enabled, onMetrics]);

  const measureResourceTiming = useCallback(() => {
    if (!enabled || typeof window === "undefined") return;

    window.addEventListener("load", () => {
      setTimeout(() => {
        const resources = performance.getEntriesByType("resource");
        const slowResources = resources.filter(
          (resource) => resource.duration > 1000
        );

        if (
          slowResources.length > 0 &&
          process.env.NODE_ENV === "development"
        ) {
          console.warn("Slow loading resources:", slowResources);
        }

        if (onMetrics) {
          onMetrics({
            metric: "Slow Resources",
            value: slowResources.length,
            details: slowResources.map((r) => ({
              name: r.name,
              duration: r.duration,
            })),
          });
        }
      }, 1000);
    });
  }, [enabled, onMetrics]);

  useEffect(() => {
    if (!enabled) return;

    measureWebVitals();
    measurePageLoad();
    measureResourceTiming();
  }, [enabled, measureWebVitals, measurePageLoad, measureResourceTiming]);

  return {
    measureWebVitals,
    measurePageLoad,
    measureResourceTiming,
  };
}

/**
 * Hook for monitoring component render performance
 * @param {string} componentName - Name of the component
 * @param {boolean} enabled - Whether to enable monitoring
 */
export function useRenderPerformance(componentName, enabled = true) {
  useEffect(() => {
    if (!enabled || process.env.NODE_ENV !== "development") return;

    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      if (renderTime > 16) {
        // More than one frame (16ms)
        console.warn(`${componentName} render took ${renderTime.toFixed(2)}ms`);
      }
    };
  });
}

/**
 * Hook for monitoring memory usage
 * @param {boolean} enabled - Whether to enable monitoring
 */
export function useMemoryMonitor(enabled = true) {
  useEffect(() => {
    if (!enabled || typeof window === "undefined" || !("memory" in performance))
      return;

    const checkMemory = () => {
      const memory = performance.memory;
      const memoryInfo = {
        usedJSHeapSize: (memory.usedJSHeapSize / 1048576).toFixed(2) + " MB",
        totalJSHeapSize: (memory.totalJSHeapSize / 1048576).toFixed(2) + " MB",
        jsHeapSizeLimit: (memory.jsHeapSizeLimit / 1048576).toFixed(2) + " MB",
      };

      if (process.env.NODE_ENV === "development") {
        console.log("Memory Usage:", memoryInfo);
      }

      // Warn if memory usage is high
      if (memory.usedJSHeapSize / memory.jsHeapSizeLimit > 0.8) {
        console.warn("High memory usage detected");
      }
    };

    const interval = setInterval(checkMemory, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [enabled]);
}
