"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const user_service_1 = __importDefault(require("../services/user.service"));
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const upload_middleware_1 = require("../../../middlewares/upload.middleware");
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const users_validations_1 = require("../users.validations");
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const user_model_1 = __importDefault(require("../user.model"));
const multer_1 = __importDefault(require("multer"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class UserController {
    constructor() {
        this.path = '/users';
        this.router = (0, express_1.Router)();
        this.userService = new user_service_1.default();
        this.upload = (0, multer_1.default)();
        this.desctiveraccountUser = async (request, response, next) => {
            try {
                const id = request.params.id;
                const result = await this.userService.desactiverAccount(id);
                response.status(200).send('desactivate successufly');
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteemail = async (request, response, next) => {
            try {
                const id = request.params.id;
                const { email } = request.body;
                if (!id || !email) {
                    return response.status(400).json({ message: 'User ID and email are required' });
                }
                await this.userService.deleteEmail(id, email);
                response.status(200).json({ message: 'Email added successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.addemailtouser = async (request, response, next) => {
            try {
                const id = request.params.id;
                const { email } = request.body;
                if (!id || !email) {
                    return response.status(400).json({ message: 'User ID and email are required' });
                }
                await this.userService.addEmailToUser(id, email);
                response.status(200).json({ message: 'Email added successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.editEmail = async (request, response, next) => {
            try {
                const id = request.params.id;
                const { oldEmail, newEmail } = request.body;
                if (!id || !oldEmail || !newEmail) {
                    return response.status(400).json({ message: 'User ID, old email, and new email are required' });
                }
                await this.userService.EditEmail(id, oldEmail, newEmail);
                response.status(200).json({ message: 'Email updated successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.editPhone = async (request, response, next) => {
            try {
                const id = request.params.id;
                const { oldPhone, newPhone } = request.body;
                if (!id || !oldPhone || !newPhone) {
                    return response.status(400).json({ message: 'User ID, old phone, and new phone are required' });
                }
                await this.userService.EditPhone(id, oldPhone, newPhone);
                response.status(200).json({ message: 'phone updated successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.addphonetouser = async (request, response, next) => {
            try {
                const id = request.params.id;
                const { phone } = request.body;
                if (!id || !phone) {
                    return response.status(400).json({ message: 'User ID and phone are required' });
                }
                await this.userService.addPhonetouser(id, phone);
                response.status(200).json({ message: 'phone added successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.getUsers = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.userService.getAll(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.deletephone = async (request, response, next) => {
            try {
                const id = request.params.id;
                const { phone } = request.body;
                if (!id || !phone) {
                    return response.status(400).json({ message: 'User ID and phone are required' });
                }
                await this.userService.deletePhone(id, phone);
                response.status(200).json({ message: 'phone deleted successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateUserDetails = async (request, response, next) => {
            try {
                const user = request.user;
                if (!user) {
                    response.status(401).send({ message: 'Unauthorized' });
                    return;
                }
                const id = request.params.id;
                const data = request.body;
                const result = await this.userService.updateUserDetails(id, data, user._id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.disarchiveUtilisateur = async (req, res, next) => {
            const id = req.params.id;
            try {
                const user = await user_model_1.default.findByIdAndUpdate(id, { isArchived: false });
                if (!user) {
                    res.status(404).send({ message: 'user not found' });
                    return;
                }
                res.send({ message: `user ${id} disarchiver successfully` });
            }
            catch (error) {
                next(error);
            }
        };
        this.getCurrent = async (request, response, next) => {
            try {
                const user = request.user;
                const result = await this.userService.get(user._id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.get = async (request, response, next) => {
            try {
                const id = request.params.id;
                const result = await this.userService.get(id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.createUser = async (request, response, next) => {
            try {
                const user = request.body;
                const result = await this.userService.createUsers(user);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.archivedanddisarchiveusers = async (req, res, next) => {
            try {
                const userId = req.params.userId;
                const isArchived = req.body.archive;
                res.send({ message: await this.userService.archivedAndDisarchiveUsers(userId, isArchived) });
            }
            catch (error) {
                next(error);
            }
        };
        this.importUser = async (request, response, next) => {
            try {
                if (request.query.action === 'update')
                    await this.userService.updateUsers();
                else
                    await this.userService.importUsers(request.file);
                response.send({ message: 'Imported!' });
            }
            catch (error) {
                next(error);
            }
        };
        this.attachResumeToCandidate = async (request, response, next) => {
            try {
                await this.userService.attachResumeToCandidate(request.file);
                response.send({ message: 'Attached!' });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateUser = async (request, response, next) => {
            try {
                const user = request.user;
                const id = request.params.id;
                const data = request.body;
                const result = await this.userService.updateUser(id, data, user);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteUser = async (request, response, next) => {
            try {
                const id = request.params.id;
                await this.userService.deleteUser(id);
                response.send({
                    message: messages_1.MESSAGES.USER.USER_DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), queries_validation_middleware_1.default, this.getUsers);
        this.router.get(`${this.path}/current`, validateApiKey_middleware_1.default, authentication_middleware_1.default, this.getCurrent);
        this.router.get(`${this.path}/:id`, validateApiKey_middleware_1.default, authentication_middleware_1.default, this.get);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), upload_middleware_1.uploadMiddleware, (0, validation_middleware_1.validationMiddleware)(users_validations_1.createNewUserSchema), this.createUser);
        this.router.put(`${this.path}/phone/:id`, this.editPhone);
        this.router.put(`${this.path}/editemail/:id`, this.editEmail);
        this.router.delete(`${this.path}/editemail/:id`, this.deleteemail);
        this.router.delete(`${this.path}/phone/:id`, this.deletephone);
        this.router.post(`${this.path}/import`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.upload.single('file'), this.importUser);
        this.router.post(`${this.path}/attach`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.upload.single('file'), this.attachResumeToCandidate);
        this.router.post(`${this.path}/email/:id`, this.addemailtouser);
        this.router.post(`${this.path}/phone/:id`, this.addphonetouser);
        this.router.put(`${this.path}/modify/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.updateUserDetails);
        this.router.put(`${this.path}/disarchive/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.disarchiveUtilisateur);
        this.router.delete(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), mongoId_validation_middleware_1.default, this.deleteUser);
        this.router.put(`${this.path}/archive/:userId`, this.archivedanddisarchiveusers);
        this.router.delete(`${this.path}/desactiver/:id`, mongoId_validation_middleware_1.default, this.desctiveraccountUser);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, mongoId_validation_middleware_1.default, (0, validation_middleware_1.validationMiddleware)(users_validations_1.updateUserSchema), this.updateUser);
    }
}
exports.default = UserController;
//# sourceMappingURL=user.controller.js.map