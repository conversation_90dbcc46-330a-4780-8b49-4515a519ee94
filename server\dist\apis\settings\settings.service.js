"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSettingsService = void 0;
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const settings_model_1 = __importDefault(require("../settings/settings.model"));
class UserSettingsService {
    async updateSettingsData(currentUser, id, settingsData) {
        const existingSettings = await settings_model_1.default.findById(id).lean();
        if (!existingSettings) {
            throw new http_exception_1.default(404, 'settings not found');
        }
        if (existingSettings.user.toString() !== currentUser._id.toString()) {
            throw new http_exception_1.default(404, 'User settings not found');
        }
        const updatedSettings = await settings_model_1.default.findByIdAndUpdate(id, { ...settingsData }, { new: true }).lean();
        return updatedSettings;
    }
    async getSettings(currentUser) {
        const settings = await settings_model_1.default.find({
            user: currentUser._id,
        });
        return settings.map(setting => setting.toObject());
    }
}
exports.UserSettingsService = UserSettingsService;
//# sourceMappingURL=settings.service.js.map