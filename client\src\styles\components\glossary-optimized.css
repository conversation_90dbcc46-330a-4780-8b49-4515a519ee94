/* Optimized CSS for Glossary Components */

/* Use CSS containment for better performance */
.glossary-container {
  contain: layout style paint;
}

/* Optimize font loading */
.glossary-content {
  font-display: swap;
}

/* Use transform instead of changing layout properties */
.glossary-item {
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
}

.glossary-item:hover {
  transform: translateY(-2px) translateZ(0);
  transition: transform 0.2s ease-out;
}

/* Optimize images */
.glossary-image {
  object-fit: cover;
  object-position: center;
  loading: lazy;
  decoding: async;
}

/* Critical above-the-fold styles */
.glossary-header {
  contain: layout;
  min-height: 60px;
}

.glossary-breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.glossary-breadcrumb a {
  color: #0066cc;
  text-decoration: none;
  font-size: 14px;
}

.glossary-breadcrumb a:hover {
  text-decoration: underline;
}

/* Content area optimization */
.glossary-content-area {
  contain: layout style;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

/* Loading states */
.glossary-loading {
  contain: layout style paint;
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive optimizations */
@media (max-width: 768px) {
  .glossary-content-area {
    padding: 16px;
  }
  
  .glossary-breadcrumb {
    font-size: 12px;
  }
}

/* Print optimizations */
@media print {
  .glossary-header,
  .glossary-breadcrumb {
    display: none;
  }
  
  .glossary-content-area {
    max-width: none;
    padding: 0;
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .glossary-item {
    transition: none;
  }
  
  .loading-skeleton {
    animation: none;
    background: #f0f0f0;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glossary-breadcrumb a {
    color: #000;
    font-weight: bold;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .glossary-content-area {
    background-color: #1a1a1a;
    color: #ffffff;
  }
  
  .glossary-breadcrumb a {
    color: #66b3ff;
  }
  
  .loading-skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
  }
}

/* Focus management for accessibility */
.glossary-content-area:focus-within {
  outline: 2px solid #0066cc;
  outline-offset: 2px;
}

/* Optimize text rendering */
.glossary-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Grid optimizations for layout */
.glossary-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  contain: layout;
}

@media (min-width: 768px) {
  .glossary-grid {
    grid-template-columns: 2fr 1fr;
  }
}

/* Intersection observer optimization */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s;
}

.lazy-load.loaded {
  opacity: 1;
}

/* Critical resource hints */
.glossary-page {
  /* Preload critical fonts */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Performance-optimized animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Optimize for Core Web Vitals */
.glossary-main {
  /* Improve LCP */
  contain: layout style paint;
  
  /* Reduce CLS */
  min-height: 400px;
}

/* Optimize scrolling performance */
.glossary-scroll-container {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
