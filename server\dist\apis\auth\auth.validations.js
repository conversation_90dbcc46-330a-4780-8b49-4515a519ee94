"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSchema = exports.tokenResetPasswordSchema = exports.confirmAccountSchema = exports.resetPasswordSchema = exports.forgotPasswordSchema = exports.signInSchema = exports.signUpSchema = void 0;
const constants_1 = require("@/utils/helpers/constants");
const joi_1 = __importDefault(require("joi"));
const signUpSchema = joi_1.default.object({
    firstName: joi_1.default.string().min(3).required(),
    lastName: joi_1.default.string().min(3).required(),
    email: joi_1.default.string().required().email(),
    password: joi_1.default.string().required(),
    industry: joi_1.default.string(),
    industries: joi_1.default.array().items(joi_1.default.string()),
    country: joi_1.default.string(),
    phone: joi_1.default.string(),
});
exports.signUpSchema = signUpSchema;
const UpdateSchema = joi_1.default.object({
    firstName: joi_1.default.string().min(3).required(),
    lastName: joi_1.default.string().min(3).required(),
    phone: joi_1.default.string().min(3).required(),
    email: joi_1.default.string().required().email(),
    country: joi_1.default.valid(...Object.values(constants_1.Countries)).required(),
    password: joi_1.default.string().min(8).required(),
    jobTitle: joi_1.default.string(),
    roles: joi_1.default.array()
        .items(joi_1.default.valid(...Object.values(constants_1.Role)))
        .optional(),
    certifications: joi_1.default.when('roles', {
        is: joi_1.default.array().items(joi_1.default.valid(constants_1.Role.CANDIDATE)).required(),
        then: joi_1.default.array().items(joi_1.default.object({
            academy: joi_1.default.string().required(),
            endDate: joi_1.default.string().required(),
            startDate: joi_1.default.string().required(),
            title: joi_1.default.string().required(),
        })),
        otherwise: joi_1.default.forbidden(),
    }),
    educations: joi_1.default.when('roles', {
        is: joi_1.default.array().items(joi_1.default.valid(constants_1.Role.CANDIDATE)).required(),
        then: joi_1.default.array().items(joi_1.default.object({
            degree: joi_1.default.string().required(),
            endDate: joi_1.default.string().required(),
            startDate: joi_1.default.string().required(),
            university: joi_1.default.string().required(),
        })),
        otherwise: joi_1.default.forbidden(),
    }),
    experiences: joi_1.default.when('roles', {
        is: joi_1.default.array().items(joi_1.default.valid(constants_1.Role.CANDIDATE)).required(),
        then: joi_1.default.array().items(joi_1.default.object({
            company: joi_1.default.string().required(),
            contractType: joi_1.default.string().required(),
            duration: joi_1.default.string().required(),
            endDate: joi_1.default.string().required(),
            location: joi_1.default.string().required(),
            startDate: joi_1.default.string().required(),
            title: joi_1.default.string().required(),
        })),
        otherwise: joi_1.default.forbidden(),
    }),
    skills: joi_1.default.when('roles', {
        is: joi_1.default.array().items(joi_1.default.valid(constants_1.Role.CANDIDATE)).required(),
        then: joi_1.default.array().items(joi_1.default.string().required()),
        otherwise: joi_1.default.forbidden(),
    }),
});
exports.UpdateSchema = UpdateSchema;
const signInSchema = joi_1.default.object({
    email: joi_1.default.string().required().email(),
    password: joi_1.default.string().required(),
});
exports.signInSchema = signInSchema;
const forgotPasswordSchema = joi_1.default.object({
    email: joi_1.default.string().required().email(),
});
exports.forgotPasswordSchema = forgotPasswordSchema;
const resetPasswordSchema = joi_1.default.object({
    //   country: Joi.valid(...Object.values(Countries)).required(),
    // industry: Joi.valid(...Object.values(Industry)).required(),
    password: joi_1.default.string().min(8).required(),
});
exports.resetPasswordSchema = resetPasswordSchema;
const tokenResetPasswordSchema = joi_1.default.object({
    token: joi_1.default.string().min(8).required(),
});
exports.tokenResetPasswordSchema = tokenResetPasswordSchema;
const confirmAccountSchema = joi_1.default.object({
    token: joi_1.default.string().min(40).max(40).required(),
});
exports.confirmAccountSchema = confirmAccountSchema;
//# sourceMappingURL=auth.validations.js.map