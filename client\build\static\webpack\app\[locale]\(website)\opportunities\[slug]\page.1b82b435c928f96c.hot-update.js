"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/opportunities/[slug]/page",{

/***/ "(app-pages-browser)/./src/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItem.jsx":
/*!**************************************************************************************************************!*\
  !*** ./src/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItem.jsx ***!
  \**************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OpportunityItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _OpportunityItemByGrid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./OpportunityItemByGrid */ \"(app-pages-browser)/./src/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItemByGrid.jsx\");\n/* harmony import */ var _OpportunityItemByList__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./OpportunityItemByList */ \"(app-pages-browser)/./src/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItemByList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction OpportunityItem(param) {\n    let { key, opportunity, language, isList } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    if (isList) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OpportunityItemByList__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        opportunity: opportunity,\n        language: language\n    }, key, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\OpportunityComponents\\\\OpportunityItem.jsx\",\n        lineNumber: 20,\n        columnNumber: 7\n    }, this);\n    else return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OpportunityItemByGrid__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        opportunity: opportunity,\n        language: language\n    }, key, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\OpportunityComponents\\\\OpportunityItem.jsx\",\n        lineNumber: 28,\n        columnNumber: 7\n    }, this);\n}\n_s(OpportunityItem, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function() {\n    return [\n        _barrel_optimize_names_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = OpportunityItem;\nvar _c;\n$RefreshReg$(_c, \"OpportunityItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9vcHBvcnR1bml0eS9jb21wb25lbnRzL29wcG9ydHVuaXR5RnJvbnRPZmZpY2UvT3Bwb3J0dW5pdHlDb21wb25lbnRzL09wcG9ydHVuaXR5SXRlbS5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUV5QztBQUVtQjtBQUNBO0FBRTdDLFNBQVNHLGdCQUFnQixLQUt2QztRQUx1QyxFQUN0Q0MsR0FBRyxFQUNIQyxXQUFXLEVBQ1hDLFFBQVEsRUFDUkMsTUFBTSxFQUNQLEdBTHVDOztJQU9wQyxNQUFNQyxRQUFRUix3RkFBUUE7SUFHeEIsSUFBSU8sUUFDRixxQkFDRSw4REFBQ0wsOERBQXFCQTtRQUVwQkcsYUFBYUE7UUFDYkMsVUFBVUE7T0FGTEY7Ozs7O1NBTVQscUJBQ0UsOERBQUNILDhEQUFxQkE7UUFFcEJJLGFBQWFBO1FBQ2JDLFVBQVVBO09BRkxGOzs7OztBQUtiO0dBMUJ3QkQ7O1FBT05ILG9GQUFRQTs7O0tBUEZHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9vcHBvcnR1bml0eS9jb21wb25lbnRzL29wcG9ydHVuaXR5RnJvbnRPZmZpY2UvT3Bwb3J0dW5pdHlDb21wb25lbnRzL09wcG9ydHVuaXR5SXRlbS5qc3g/MGYxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxyXG5cclxuaW1wb3J0IHsgdXNlVGhlbWUgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5cclxuaW1wb3J0IE9wcG9ydHVuaXR5SXRlbUJ5R3JpZCBmcm9tIFwiLi9PcHBvcnR1bml0eUl0ZW1CeUdyaWRcIjtcclxuaW1wb3J0IE9wcG9ydHVuaXR5SXRlbUJ5TGlzdCBmcm9tIFwiLi9PcHBvcnR1bml0eUl0ZW1CeUxpc3RcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE9wcG9ydHVuaXR5SXRlbSh7XHJcbiAga2V5LFxyXG4gIG9wcG9ydHVuaXR5LFxyXG4gIGxhbmd1YWdlLFxyXG4gIGlzTGlzdCxcclxufSkge1xyXG5cclxuICAgIGNvbnN0IHRoZW1lID0gdXNlVGhlbWUoKTtcclxuICAgIFxyXG5cclxuICBpZiAoaXNMaXN0KVxyXG4gICAgcmV0dXJuIChcclxuICAgICAgPE9wcG9ydHVuaXR5SXRlbUJ5TGlzdFxyXG4gICAgICAgIGtleT17a2V5fVxyXG4gICAgICAgIG9wcG9ydHVuaXR5PXtvcHBvcnR1bml0eX1cclxuICAgICAgICBsYW5ndWFnZT17bGFuZ3VhZ2V9XHJcbiAgICAgIC8+XHJcbiAgICApO1xyXG4gIGVsc2VcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxPcHBvcnR1bml0eUl0ZW1CeUdyaWRcclxuICAgICAgICBrZXk9e2tleX1cclxuICAgICAgICBvcHBvcnR1bml0eT17b3Bwb3J0dW5pdHl9XHJcbiAgICAgICAgbGFuZ3VhZ2U9e2xhbmd1YWdlfVxyXG4gICAgICAvPlxyXG4gICAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlVGhlbWUiLCJPcHBvcnR1bml0eUl0ZW1CeUdyaWQiLCJPcHBvcnR1bml0eUl0ZW1CeUxpc3QiLCJPcHBvcnR1bml0eUl0ZW0iLCJrZXkiLCJvcHBvcnR1bml0eSIsImxhbmd1YWdlIiwiaXNMaXN0IiwidGhlbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItem.jsx\n"));

/***/ })

});