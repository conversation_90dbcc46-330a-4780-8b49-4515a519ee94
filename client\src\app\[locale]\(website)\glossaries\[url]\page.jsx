import { headers } from "next/headers";
import dynamic from "next/dynamic";

import { axiosGetJsonSSR } from "@/config/axios";
import GlossaryLoading from "@/components/loading/GlossaryLoading";

const GlossaryDetails = dynamic(
  () => import("../../../../../features/glossary/component/GlossaryDetails"),
  {
    loading: () => <GlossaryLoading />,
    ssr: true,
  }
);

function generateCanonicalUrl(language, url) {
  return `https://www.pentabell.com/${
    language !== "en" ? `${language}/` : ""
  }glossaries/${url}/`;
}

function generateLanguageAlternates(language, url, oppositeLanguageUrl) {
  if (language === "fr") {
    return {
      fr: `https://www.pentabell.com/fr/glossaries/${url}/`,
      en: `https://www.pentabell.com/glossaries/${oppositeLanguageUrl}/`,
      "x-default": `https://www.pentabell.com/glossaries/${oppositeLanguageUrl}/`,
    };
  }

  return {
    fr: `https://www.pentabell.com/fr/glossaries/${oppositeLanguageUrl}/`,
    en: `https://www.pentabell.com/glossaries/${url}/`,
    "x-default": `https://www.pentabell.com/glossaries/${url}/`,
  };
}

function cleanDescription(content) {
  return content
    ?.replace(/<\/?[^>]+(>|$)/g, " ")
    ?.replace(/&[a-z]+;/gi, "")
    ?.replace(/\s\s+/g, " ")
    ?.slice(0, 500);
}

export async function generateMetadata({ params }) {
  const requestHeaders = headers();
  const cookie = requestHeaders.get("cookie");
  const { locale: language, url } = params;

  const canonicalUrl = generateCanonicalUrl(language, url);

  try {
    const [glossaryRes, oppositeRes] = await Promise.allSettled([
      axiosGetJsonSSR.get(`/glossaries/${language}/${url}`, {
        headers: { Cookie: cookie },
      }),
      axiosGetJsonSSR.get(`/glossaries/opposite/${language}/${url}`),
    ]);

    if (glossaryRes.status !== "fulfilled") {
      throw new Error("Failed to fetch glossary");
    }

    const glossary = glossaryRes.value?.data;
    if (!glossary) {
      throw new Error("Glossary not found");
    }

    let languages = {};
    if (oppositeRes.status === "fulfilled") {
      const oppositeLanguageUrl = oppositeRes.value.data.slug;
      languages = generateLanguageAlternates(
        language,
        url,
        oppositeLanguageUrl
      );
    }

    return {
      title: glossary.metaTitle,
      description: glossary.metaDescription,
      robots:
        glossary.robotsMeta === "index"
          ? "follow, index, max-snippet:-1, max-image-preview:large"
          : "noindex",
      alternates: {
        canonical: canonicalUrl,
        languages,
      },
      openGraph: {
        title: glossary.title,
        description: cleanDescription(glossary.content),
        url: `https://www.pentabell.com/${
          language === "en" ? "" : `${language}/`
        }glossaries/${glossary.url}/`,
        images: glossary.image
          ? [
              {
                url: `https://www.pentabell.com/api/v1/files/${glossary.image}`,
                alt: glossary.alt || glossary.title,
              },
            ]
          : [],
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Glossary",
      description: "Glossary page with glossaries",
    };
  }
}

function generateStructuredData(glossary, language, url) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id":
        language === "en"
          ? `https://www.pentabell.com/glossaries/${url}`
          : `https://www.pentabell.com/${language}/glossaries/${url}`,
    },
    headline: glossary.title,
    description: glossary.metaDescription,
    image: glossary.image
      ? `https://www.pentabell.com/api/v1/files/${glossary.image}`
      : null,
    publisher: {
      "@type": "Organization",
      name: "Pentabell",
      logo: {
        "@type": "ImageObject",
        url: "https://www.pentabell.com/api/v1/images/pentabell.png",
      },
    },
    datePublished: glossary.createdAt,
    dateModified: glossary.updatedAt,
  };
}

export default async function Page({ params }) {
  const { locale: language, url } = params;
  const requestHeaders = headers();
  const cookie = requestHeaders.get("cookie");

  try {
    const res = await axiosGetJsonSSR.get(`/glossaries/${language}/${url}`, {
      headers: { Cookie: cookie },
    });

    const glossary = res?.data;
    if (!glossary) {
      throw new Error("Glossary not found");
    }

    const structuredData = generateStructuredData(glossary, language, url);

    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
        <GlossaryDetails article={glossary} language={language} />
      </>
    );
  } catch (error) {
    return (
      <div>
        <h1>Glossary not found</h1>
        <p>The requested glossary could not be found.</p>
      </div>
    );
  }
}
