import { headers } from "next/headers";

import { axiosGetJsonSSR } from "@/config/axios";
import GlossaryDetails from "../../../../../features/glossary/component/GlossaryDetails";

export async function generateMetadata({ params }) {
  const requestHeaders = headers();
  const cookie = requestHeaders.get("cookie");

  const language = params.locale;
  const url = params?.url;
  let canonicalUrl = `https://www.pentabell.com/${
    language !== "en" ? `${language}/` : ""
  }glossaries/${url}/`;

  try {
    const res = await axiosGetJsonSSR.get(`/glossaries/${language}/${url}`, {
      headers: {
        Cookie: cookie,
      },
    });

    const glossary = res?.data;

    let languages = {};

    try {
      const res = await axiosGetJsonSSR.get(
        `/glossaries/opposite/${language}/${url}`
      );

      const oppositeLanguageUrl = res.data.slug;

      if (language === "fr") {
        languages.fr = `https://www.pentabell.com/fr/glossaries/${url}/`;
        languages.en = `https://www.pentabell.com/glossaries/${oppositeLanguageUrl}/`;
        languages[
          "x-default"
        ] = `https://www.pentabell.com/glossaries/${oppositeLanguageUrl}/`;
        canonicalUrl = `https://www.pentabell.com/fr/glossaries/${url}/`;
      }

      if (language === "en") {
        languages.fr = `https://www.pentabell.com/fr/glossaries/${oppositeLanguageUrl}/`;
        languages.en = `https://www.pentabell.com/glossaries/${url}/`;
        languages["x-default"] = `https://www.pentabell.com/glossaries/${url}/`;
        canonicalUrl = `https://www.pentabell.com/glossaries/${url}/`;
      }
    } catch (error) {
      console.log("error", error.response.data);
    }

    if (glossary) {
      return {
        title: glossary.metaTitle,
        description: glossary.metaDescription,
        robots:
          res?.data?.robotsMeta === "index"
            ? "follow, index, max-snippet:-1, max-image-preview:large"
            : "noindex",
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
        openGraph: {
          title: glossary?.title,
          description: glossary?.content
            ?.replace(/<\/?[^>]+(>|$)/g, " ")
            ?.replace(/&[a-z]+;/gi, "")
            ?.replace(/\s\s+/g, " ")
            ?.slice(0, 500),
          url:
            params.locale === "en"
              ? `https://www.pentabell.com/glossaries/${glossary?.url}/`
              : `https://www.pentabell.com/${params.locale}/glossaries/${glossary?.url}/`,
          images: [
            {
              url: glossary?.image
                ? `https://www.pentabell.com/api/v1/files/${glossary?.image}`
                : null,
              alt: glossary?.alt,
            },
          ],
        },
      };
    }
  } catch (error) {
    return {
      title: "Error",
      description: "An error occurred while fetching the glossary",
    };
  }

  return {
    title: "Glossary",
    description: "Glossary page with glossaries",
  };
}

export default async function Page({ params }) {
  const language = params.locale;
  const url = params?.url;

  const requestHeaders = headers();
  const cookie = requestHeaders.get("cookie");

  try {
    const res = await axiosGetJsonSSR.get(`/glossaries/${language}/${url}`, {
      headers: {
        Cookie: cookie,
      },
    });

    const glossary = res?.data;
    const glossaryId = glossary?._id;

    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BlogPosting",
              mainEntityOfPage: {
                "@type": "WebPage",
                "@id":
                  language === "en"
                    ? `https://www.pentabell.com/blog/${url}`
                    : `https://www.pentabell.com/${language}/blog/${url}`,
              },
              headline: glossary?.title,
              description: glossary?.metaDescription,
              image: `https://www.pentabell.com/api/v1/files/${glossary?.image}`,
              publisher: {
                "@type": "Organization",
                name: "Pentabell",
                logo: {
                  "@type": "ImageObject",
                  url: "https://www.pentabell.com/api/v1/images/pentabell.png",
                },
              },
              datePublished: glossary?.createdAt,
              dateModified: glossary?.updatedAt,
            }),
          }}
        />
        <GlossaryDetails
          id={glossaryId}
          article={glossary}
          language={language}
          url={url}
        />
      </>
    );
  } catch (error) {
    console.log(error);
    // redirect(params.locale === "en" ? `/blog/` : `/${params.locale}/blog/`);
  }
}
