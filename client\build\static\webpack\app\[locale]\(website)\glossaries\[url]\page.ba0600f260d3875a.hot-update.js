"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\");\n/* harmony import */ var _hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePerformanceMonitor */ \"(app-pages-browser)/./src/hooks/usePerformanceMonitor.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst GlossaryDetails = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function GlossaryDetails(param) {\n    let { article, language } = param;\n    _s();\n    // Monitor render performance in development\n    (0,_hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_4__.useRenderPerformance)(\"GlossaryDetails\", \"development\" === \"development\");\n    // Memoize the breadcrumb schema to avoid recalculation\n    const breadcrumbSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"BreadcrumbList\",\n            itemListElement: [\n                {\n                    \"@type\": \"ListItem\",\n                    position: 1,\n                    item: {\n                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                        name: \"Glossary\"\n                    }\n                }\n            ]\n        }), [\n        language\n    ]);\n    // Memoize the glossary path URL\n    const glossaryPath = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>language === \"en\" ? \"/glossary\" : \"/\".concat(language, \"/glossary\"), [\n        language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page-details\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"glossary-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"custom-max-width\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glossary-path\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                locale: language === \"en\" ? \"en\" : \"fr\",\n                                href: \"\".concat(glossaryPath, \"/\"),\n                                className: \"link\",\n                                children: \"Glossary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            (article === null || article === void 0 ? void 0 : article.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"word\",\n                                        children: article.word\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(breadcrumbSchema)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"container\",\n                    container: true,\n                    columnSpacing: 2,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 8,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"glossary-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                htmlContent: article === null || article === void 0 ? void 0 : article.content\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}, \"ss/RV1DNZsl/fvk/D252rhH0FEE=\", false, function() {\n    return [\n        _hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_4__.useRenderPerformance\n    ];\n})), \"ss/RV1DNZsl/fvk/D252rhH0FEE=\", false, function() {\n    return [\n        _hooks_usePerformanceMonitor__WEBPACK_IMPORTED_MODULE_4__.useRenderPerformance\n    ];\n});\n_c1 = GlossaryDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryDetails);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlossaryDetails$memo\");\n$RefreshReg$(_c1, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/usePerformanceMonitor.js":
/*!********************************************!*\
  !*** ./src/hooks/usePerformanceMonitor.js ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMemoryMonitor: function() { return /* binding */ useMemoryMonitor; },\n/* harmony export */   usePerformanceMonitor: function() { return /* binding */ usePerformanceMonitor; },\n/* harmony export */   useRenderPerformance: function() { return /* binding */ useRenderPerformance; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n/**\n * Custom hook for monitoring performance metrics\n * @param {boolean} enabled - Whether to enable performance monitoring\n * @param {Function} onMetrics - Callback function to handle metrics\n */ function usePerformanceMonitor() {\n    let enabled = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true, onMetrics = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n    _s();\n    const measureWebVitals = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!enabled || \"object\" === \"undefined\") return;\n        // Largest Contentful Paint (LCP)\n        if (\"PerformanceObserver\" in window) {\n            try {\n                const lcpObserver = new PerformanceObserver((entryList)=>{\n                    const entries = entryList.getEntries();\n                    const lastEntry = entries[entries.length - 1];\n                    const lcp = lastEntry.startTime;\n                    if (onMetrics) {\n                        onMetrics({\n                            metric: \"LCP\",\n                            value: lcp,\n                            rating: lcp < 2500 ? \"good\" : lcp < 4000 ? \"needs-improvement\" : \"poor\"\n                        });\n                    }\n                    if (true) {\n                        console.log(\"LCP:\", lcp, \"ms\");\n                    }\n                });\n                lcpObserver.observe({\n                    entryTypes: [\n                        \"largest-contentful-paint\"\n                    ]\n                });\n                // First Input Delay (FID)\n                const fidObserver = new PerformanceObserver((entryList)=>{\n                    const entries = entryList.getEntries();\n                    entries.forEach((entry)=>{\n                        const fid = entry.processingStart - entry.startTime;\n                        if (onMetrics) {\n                            onMetrics({\n                                metric: \"FID\",\n                                value: fid,\n                                rating: fid < 100 ? \"good\" : fid < 300 ? \"needs-improvement\" : \"poor\"\n                            });\n                        }\n                        if (true) {\n                            console.log(\"FID:\", fid, \"ms\");\n                        }\n                    });\n                });\n                fidObserver.observe({\n                    entryTypes: [\n                        \"first-input\"\n                    ]\n                });\n                // Cumulative Layout Shift (CLS)\n                let clsValue = 0;\n                const clsObserver = new PerformanceObserver((entryList)=>{\n                    const entries = entryList.getEntries();\n                    entries.forEach((entry)=>{\n                        if (!entry.hadRecentInput) {\n                            clsValue += entry.value;\n                        }\n                    });\n                    if (onMetrics) {\n                        onMetrics({\n                            metric: \"CLS\",\n                            value: clsValue,\n                            rating: clsValue < 0.1 ? \"good\" : clsValue < 0.25 ? \"needs-improvement\" : \"poor\"\n                        });\n                    }\n                    if (true) {\n                        console.log(\"CLS:\", clsValue);\n                    }\n                });\n                clsObserver.observe({\n                    entryTypes: [\n                        \"layout-shift\"\n                    ]\n                });\n                // Total Blocking Time (TBT) approximation\n                const tbtObserver = new PerformanceObserver((entryList)=>{\n                    let tbt = 0;\n                    const entries = entryList.getEntries();\n                    entries.forEach((entry)=>{\n                        if (entry.duration > 50) {\n                            tbt += entry.duration - 50;\n                        }\n                    });\n                    if (onMetrics) {\n                        onMetrics({\n                            metric: \"TBT\",\n                            value: tbt,\n                            rating: tbt < 200 ? \"good\" : tbt < 600 ? \"needs-improvement\" : \"poor\"\n                        });\n                    }\n                    if (true) {\n                        console.log(\"TBT (approx):\", tbt, \"ms\");\n                    }\n                });\n                tbtObserver.observe({\n                    entryTypes: [\n                        \"longtask\"\n                    ]\n                });\n            } catch (error) {\n                console.warn(\"Performance monitoring not supported:\", error);\n            }\n        }\n    }, [\n        enabled,\n        onMetrics\n    ]);\n    const measurePageLoad = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!enabled || \"object\" === \"undefined\") return;\n        window.addEventListener(\"load\", ()=>{\n            setTimeout(()=>{\n                if (\"performance\" in window) {\n                    const perfData = performance.getEntriesByType(\"navigation\")[0];\n                    const metrics = {\n                        \"DNS Lookup\": perfData.domainLookupEnd - perfData.domainLookupStart,\n                        \"TCP Connection\": perfData.connectEnd - perfData.connectStart,\n                        \"Request\": perfData.responseStart - perfData.requestStart,\n                        \"Response\": perfData.responseEnd - perfData.responseStart,\n                        \"DOM Processing\": perfData.domComplete - perfData.domLoading,\n                        \"Total Load Time\": perfData.loadEventEnd - perfData.navigationStart,\n                        \"Time to Interactive\": perfData.domInteractive - perfData.navigationStart,\n                        \"DOM Content Loaded\": perfData.domContentLoadedEventEnd - perfData.navigationStart\n                    };\n                    if (onMetrics) {\n                        Object.entries(metrics).forEach((param)=>{\n                            let [metric, value] = param;\n                            onMetrics({\n                                metric,\n                                value,\n                                type: \"navigation\"\n                            });\n                        });\n                    }\n                    if (true) {\n                        console.table(metrics);\n                    }\n                }\n            }, 0);\n        });\n    }, [\n        enabled,\n        onMetrics\n    ]);\n    const measureResourceTiming = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!enabled || \"object\" === \"undefined\") return;\n        window.addEventListener(\"load\", ()=>{\n            setTimeout(()=>{\n                const resources = performance.getEntriesByType(\"resource\");\n                const slowResources = resources.filter((resource)=>resource.duration > 1000);\n                if (slowResources.length > 0 && \"development\" === \"development\") {\n                    console.warn(\"Slow loading resources:\", slowResources);\n                }\n                if (onMetrics) {\n                    onMetrics({\n                        metric: \"Slow Resources\",\n                        value: slowResources.length,\n                        details: slowResources.map((r)=>({\n                                name: r.name,\n                                duration: r.duration\n                            }))\n                    });\n                }\n            }, 1000);\n        });\n    }, [\n        enabled,\n        onMetrics\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!enabled) return;\n        measureWebVitals();\n        measurePageLoad();\n        measureResourceTiming();\n    }, [\n        enabled,\n        measureWebVitals,\n        measurePageLoad,\n        measureResourceTiming\n    ]);\n    return {\n        measureWebVitals,\n        measurePageLoad,\n        measureResourceTiming\n    };\n}\n_s(usePerformanceMonitor, \"t3FlveAqo7gc4iLs616ulQcHo/4=\");\n/**\n * Hook for monitoring component render performance\n * @param {string} componentName - Name of the component\n * @param {boolean} enabled - Whether to enable monitoring\n */ function useRenderPerformance(componentName) {\n    let enabled = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n    _s1();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!enabled || \"development\" !== \"development\") return;\n        const startTime = performance.now();\n        return ()=>{\n            const endTime = performance.now();\n            const renderTime = endTime - startTime;\n            if (renderTime > 16) {\n                console.warn(\"\".concat(componentName, \" render took \").concat(renderTime.toFixed(2), \"ms\"));\n            }\n        };\n    });\n}\n_s1(useRenderPerformance, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n/**\n * Hook for monitoring memory usage\n * @param {boolean} enabled - Whether to enable monitoring\n */ function useMemoryMonitor() {\n    let enabled = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n    _s2();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!enabled || \"object\" === \"undefined\" || !(\"memory\" in performance)) return;\n        const checkMemory = ()=>{\n            const memory = performance.memory;\n            const memoryInfo = {\n                usedJSHeapSize: (memory.usedJSHeapSize / 1048576).toFixed(2) + \" MB\",\n                totalJSHeapSize: (memory.totalJSHeapSize / 1048576).toFixed(2) + \" MB\",\n                jsHeapSizeLimit: (memory.jsHeapSizeLimit / 1048576).toFixed(2) + \" MB\"\n            };\n            if (true) {\n                console.log(\"Memory Usage:\", memoryInfo);\n            }\n            // Warn if memory usage is high\n            if (memory.usedJSHeapSize / memory.jsHeapSizeLimit > 0.8) {\n                console.warn(\"High memory usage detected\");\n            }\n        };\n        const interval = setInterval(checkMemory, 30000); // Check every 30 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        enabled\n    ]);\n}\n_s2(useMemoryMonitor, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/usePerformanceMonitor.js\n"));

/***/ })

});