"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const alertSchema = new mongoose_1.Schema({
    createdBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    industry: {
        type: [
            {
                type: String,
                enum: Object.values(constants_1.IndustryCandidat),
            },
        ],
        required: true,
    },
    title: {
        type: String,
    },
    country: {
        type: [
            {
                type: String,
                enum: Object.values(constants_1.Countries),
            },
        ],
    },
    isActive: {
        type: Boolean,
    },
    frequence: {
        type: String,
        enum: constants_1.Frequence,
    },
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
    discriminatorKey: 'type',
});
exports.default = (0, mongoose_1.model)('<PERSON>ert', alertSchema);
//# sourceMappingURL=alert.model.js.map