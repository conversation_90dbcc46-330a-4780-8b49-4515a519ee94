import { error } from "console";
import { DownloadReportModel } from "./downloadsreport.model";
import path from "path";
import { sendEmail } from "@/utils/services";

class DownloadReportService {

    private readonly DownloadReport = DownloadReportModel;

    public async handleReportDownload(fullName: string, email: string): Promise<string> {
        if (!fullName || !email) {
            throw new Error('FullName and Email are required');
        }
        const filePath = path.join(__dirname, '/../../../src/public/static/Pentabell Corporate Profile.pdf');
        const downloadUrl = 'https://www.pentabell.com/Pentabell Corporate Profile.pdf';
        const existingUser = await this.DownloadReport.findOne({ email: email.toLowerCase() });
        await sendEmail({
            to: email,
            subject: 'Your Corporate Profile is Ready to Download',
            template: 'downloadReport',
            context: {
                fullName,
                email,
                downloadUrl,
            },
            attachments: [
                {
                    filename: 'Pentabell Corporate Profile.pdf',
                    path: filePath,
                    contentType: 'application/pdf',
                },
            ],
        });
        if (!existingUser) {
            await this.DownloadReport.create({ fullName, email });
            return 'Email sent with report (new record)';
        }
        return 'Email sent with report (existing user)';
    }
 



}




export default DownloadReportService;