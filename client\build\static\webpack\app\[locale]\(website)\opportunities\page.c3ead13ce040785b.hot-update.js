"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/opportunities/page",{

/***/ "(app-pages-browser)/./src/features/opportunity/components/opportunityFrontOffice/FilterPopup.jsx":
/*!************************************************************************************!*\
  !*** ./src/features/opportunity/components/opportunityFrontOffice/FilterPopup.jsx ***!
  \************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _FilterComponents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FilterComponents */ \"(app-pages-browser)/./src/features/opportunity/components/opportunityFrontOffice/FilterComponents/index.js\");\n/* harmony import */ var _constants_filterOptions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../constants/filterOptions */ \"(app-pages-browser)/./src/features/opportunity/constants/filterOptions.js\");\n/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../hooks */ \"(app-pages-browser)/./src/features/opportunity/hooks/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst FilterPopup = (param)=>{\n    let { isOpen, onClose, setFieldValue, values, t, countries, setPageNumber, jobIndustry, setSelectedFilters } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const isTablet = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { expandedSections, toggleSection } = (0,_hooks__WEBPACK_IMPORTED_MODULE_4__.useFilterSections)(isMobile, isTablet);\n    const { handleCheckboxChange, handleSearchChange, handleCountryChange, handleClearFilters } = (0,_hooks__WEBPACK_IMPORTED_MODULE_4__.useFilterHandlers)({\n        setFieldValue,\n        values,\n        pathname,\n        setPageNumber,\n        setSelectedFilters\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"filter-actions\",\n        className: \"filter-popup \".concat(isOpen ? \"open\" : \"\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"filter-popup-content\",\n            children: [\n                isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.FilterAccordion, {\n                    title: \"Search\",\n                    expanded: expandedSections.search,\n                    onChange: ()=>toggleSection(\"search\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.SearchField, {\n                        value: values.keyWord,\n                        onChange: handleSearchChange,\n                        placeholder: t(\"Search\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, undefined),\n                isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.FilterAccordion, {\n                    title: \"Country\",\n                    expanded: expandedSections.country,\n                    onChange: ()=>toggleSection(\"country\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.CountrySelector, {\n                        value: values.country,\n                        options: countries,\n                        onChange: handleCountryChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, undefined),\n                !jobIndustry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.FilterAccordion, {\n                    title: \"Industry\",\n                    expanded: expandedSections.industry,\n                    onChange: ()=>toggleSection(\"industry\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.CheckboxGroup, {\n                        options: _constants_filterOptions__WEBPACK_IMPORTED_MODULE_3__.INDUSTRY_OPTIONS,\n                        values: values,\n                        category: \"industry\",\n                        onChange: handleCheckboxChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                        lineNumber: 88,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.FilterAccordion, {\n                    title: \"Contract Type\",\n                    expanded: expandedSections.contract,\n                    onChange: ()=>toggleSection(\"contract\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.CheckboxGroup, {\n                        options: _constants_filterOptions__WEBPACK_IMPORTED_MODULE_3__.CONTRACT_OPTIONS,\n                        values: values,\n                        category: \"contractType\",\n                        onChange: handleCheckboxChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.FilterAccordion, {\n                    title: \"Language\",\n                    expanded: expandedSections.language,\n                    onChange: ()=>toggleSection(\"language\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.CheckboxGroup, {\n                        options: _constants_filterOptions__WEBPACK_IMPORTED_MODULE_3__.LANGUAGE_OPTIONS,\n                        values: values,\n                        category: \"jobDescriptionLanguages\",\n                        onChange: handleCheckboxChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.FilterAccordion, {\n                    title: \"Level of Experience\",\n                    expanded: expandedSections.experience,\n                    onChange: ()=>toggleSection(\"experience\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.CheckboxGroup, {\n                        options: _constants_filterOptions__WEBPACK_IMPORTED_MODULE_3__.EXPERIENCE_OPTIONS,\n                        values: values,\n                        category: \"levelOfExperience\",\n                        onChange: handleCheckboxChange\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FilterComponents__WEBPACK_IMPORTED_MODULE_2__.FilterActions, {\n                    onClear: handleClearFilters\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\opportunity\\\\components\\\\opportunityFrontOffice\\\\FilterPopup.jsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FilterPopup, \"CmFUuBzncrtBFMOvpUDEiQFuRgQ=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        _hooks__WEBPACK_IMPORTED_MODULE_4__.useFilterSections,\n        _hooks__WEBPACK_IMPORTED_MODULE_4__.useFilterHandlers\n    ];\n});\n_c = FilterPopup;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FilterPopup);\nvar _c;\n$RefreshReg$(_c, \"FilterPopup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9vcHBvcnR1bml0eS9jb21wb25lbnRzL29wcG9ydHVuaXR5RnJvbnRPZmZpY2UvRmlsdGVyUG9wdXAuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBd0Q7QUFDVjtBQU9sQjtBQU1XO0FBRTRCO0FBRW5FLE1BQU1jLGNBQWM7UUFBQyxFQUNuQkMsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLGFBQWEsRUFDYkMsTUFBTSxFQUNOQyxDQUFDLEVBQ0RDLFNBQVMsRUFDVEMsYUFBYSxFQUNiQyxXQUFXLEVBQ1hDLGtCQUFrQixFQUNuQjs7SUFDQyxNQUFNQyxRQUFRdkIsc0dBQVFBO0lBQ3RCLE1BQU13QixXQUFXekIsc0dBQWFBLENBQUN3QixNQUFNRSxXQUFXLENBQUNDLElBQUksQ0FBQztJQUN0RCxNQUFNQyxXQUFXNUIsc0dBQWFBLENBQUN3QixNQUFNRSxXQUFXLENBQUNDLElBQUksQ0FBQztJQUN0RCxNQUFNRSxXQUFXM0IsNERBQVdBO0lBRTVCLE1BQU0sRUFBRTRCLGdCQUFnQixFQUFFQyxhQUFhLEVBQUUsR0FBR25CLHlEQUFpQkEsQ0FDM0RhLFVBQ0FHO0lBR0YsTUFBTSxFQUNKSSxvQkFBb0IsRUFDcEJDLGtCQUFrQixFQUNsQkMsbUJBQW1CLEVBQ25CQyxrQkFBa0IsRUFDbkIsR0FBR3RCLHlEQUFpQkEsQ0FBQztRQUNwQkk7UUFDQUM7UUFDQVc7UUFDQVI7UUFDQUU7SUFDRjtJQUVBLHFCQUNFLDhEQUFDYTtRQUFJQyxJQUFHO1FBQWlCQyxXQUFXLGdCQUFxQyxPQUFyQnZCLFNBQVMsU0FBUztrQkFDcEUsNEVBQUNxQjtZQUFJRSxXQUFVOztnQkFDWmIsMEJBQ0MsOERBQUN0Qiw4REFBZUE7b0JBQ2RvQyxPQUFNO29CQUNOQyxVQUFVVixpQkFBaUJXLE1BQU07b0JBQ2pDQyxVQUFVLElBQU1YLGNBQWM7OEJBRTlCLDRFQUFDMUIsMERBQVdBO3dCQUNWc0MsT0FBT3pCLE9BQU8wQixPQUFPO3dCQUNyQkYsVUFBVVQ7d0JBQ1ZZLGFBQWExQixFQUFFOzs7Ozs7Ozs7OztnQkFJcEJNLDBCQUNDLDhEQUFDdEIsOERBQWVBO29CQUNkb0MsT0FBTTtvQkFDTkMsVUFBVVYsaUJBQWlCZ0IsT0FBTztvQkFDbENKLFVBQVUsSUFBTVgsY0FBYzs4QkFFOUIsNEVBQUN6Qiw4REFBZUE7d0JBQ2RxQyxPQUFPekIsT0FBTzRCLE9BQU87d0JBQ3JCQyxTQUFTM0I7d0JBQ1RzQixVQUFVUjs7Ozs7Ozs7Ozs7Z0JBSWYsQ0FBQ1osNkJBQ0EsOERBQUNuQiw4REFBZUE7b0JBQ2RvQyxPQUFNO29CQUNOQyxVQUFVVixpQkFBaUJrQixRQUFRO29CQUNuQ04sVUFBVSxJQUFNWCxjQUFjOzhCQUU5Qiw0RUFBQzNCLDREQUFhQTt3QkFDWjJDLFNBQVN2QyxzRUFBZ0JBO3dCQUN6QlUsUUFBUUE7d0JBQ1IrQixVQUFTO3dCQUNUUCxVQUFVVjs7Ozs7Ozs7Ozs7OEJBSWhCLDhEQUFDN0IsOERBQWVBO29CQUNkb0MsT0FBTTtvQkFDTkMsVUFBVVYsaUJBQWlCb0IsUUFBUTtvQkFDbkNSLFVBQVUsSUFBTVgsY0FBYzs4QkFFOUIsNEVBQUMzQiw0REFBYUE7d0JBQ1oyQyxTQUFTdEMsc0VBQWdCQTt3QkFDekJTLFFBQVFBO3dCQUNSK0IsVUFBUzt3QkFDVFAsVUFBVVY7Ozs7Ozs7Ozs7OzhCQUdkLDhEQUFDN0IsOERBQWVBO29CQUNkb0MsT0FBTTtvQkFDTkMsVUFBVVYsaUJBQWlCcUIsUUFBUTtvQkFDbkNULFVBQVUsSUFBTVgsY0FBYzs4QkFFOUIsNEVBQUMzQiw0REFBYUE7d0JBQ1oyQyxTQUFTckMsc0VBQWdCQTt3QkFDekJRLFFBQVFBO3dCQUNSK0IsVUFBUzt3QkFDVFAsVUFBVVY7Ozs7Ozs7Ozs7OzhCQUdkLDhEQUFDN0IsOERBQWVBO29CQUNkb0MsT0FBTTtvQkFDTkMsVUFBVVYsaUJBQWlCc0IsVUFBVTtvQkFDckNWLFVBQVUsSUFBTVgsY0FBYzs4QkFFOUIsNEVBQUMzQiw0REFBYUE7d0JBQ1oyQyxTQUFTcEMsd0VBQWtCQTt3QkFDM0JPLFFBQVFBO3dCQUNSK0IsVUFBUzt3QkFDVFAsVUFBVVY7Ozs7Ozs7Ozs7OzhCQUdkLDhEQUFDekIsNERBQWFBO29CQUFDOEMsU0FBU2xCOzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQztHQXJITXJCOztRQVdVYixrR0FBUUE7UUFDTEQsa0dBQWFBO1FBQ2JBLGtHQUFhQTtRQUNiRSx3REFBV0E7UUFFZ0JVLHFEQUFpQkE7UUFVekRDLHFEQUFpQkE7OztLQTFCakJDO0FBdUhOLCtEQUFlQSxXQUFXQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9mZWF0dXJlcy9vcHBvcnR1bml0eS9jb21wb25lbnRzL29wcG9ydHVuaXR5RnJvbnRPZmZpY2UvRmlsdGVyUG9wdXAuanN4P2Q0YjEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlTWVkaWFRdWVyeSwgdXNlVGhlbWUgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcclxuaW1wb3J0IHtcclxuICBGaWx0ZXJBY2NvcmRpb24sXHJcbiAgQ2hlY2tib3hHcm91cCxcclxuICBTZWFyY2hGaWVsZCxcclxuICBDb3VudHJ5U2VsZWN0b3IsXHJcbiAgRmlsdGVyQWN0aW9ucyxcclxufSBmcm9tIFwiLi9GaWx0ZXJDb21wb25lbnRzXCI7XHJcbmltcG9ydCB7XHJcbiAgSU5EVVNUUllfT1BUSU9OUyxcclxuICBDT05UUkFDVF9PUFRJT05TLFxyXG4gIExBTkdVQUdFX09QVElPTlMsXHJcbiAgRVhQRVJJRU5DRV9PUFRJT05TLFxyXG59IGZyb20gXCIuLi8uLi9jb25zdGFudHMvZmlsdGVyT3B0aW9uc1wiO1xyXG5cclxuaW1wb3J0IHsgdXNlRmlsdGVyU2VjdGlvbnMsIHVzZUZpbHRlckhhbmRsZXJzIH0gZnJvbSBcIi4uLy4uL2hvb2tzXCI7XHJcblxyXG5jb25zdCBGaWx0ZXJQb3B1cCA9ICh7XHJcbiAgaXNPcGVuLFxyXG4gIG9uQ2xvc2UsXHJcbiAgc2V0RmllbGRWYWx1ZSxcclxuICB2YWx1ZXMsXHJcbiAgdCxcclxuICBjb3VudHJpZXMsXHJcbiAgc2V0UGFnZU51bWJlcixcclxuICBqb2JJbmR1c3RyeSxcclxuICBzZXRTZWxlY3RlZEZpbHRlcnMsXHJcbn0pID0+IHtcclxuICBjb25zdCB0aGVtZSA9IHVzZVRoZW1lKCk7XHJcbiAgY29uc3QgaXNNb2JpbGUgPSB1c2VNZWRpYVF1ZXJ5KHRoZW1lLmJyZWFrcG9pbnRzLmRvd24oXCJzbVwiKSk7XHJcbiAgY29uc3QgaXNUYWJsZXQgPSB1c2VNZWRpYVF1ZXJ5KHRoZW1lLmJyZWFrcG9pbnRzLmRvd24oXCJtZFwiKSk7XHJcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xyXG5cclxuICBjb25zdCB7IGV4cGFuZGVkU2VjdGlvbnMsIHRvZ2dsZVNlY3Rpb24gfSA9IHVzZUZpbHRlclNlY3Rpb25zKFxyXG4gICAgaXNNb2JpbGUsXHJcbiAgICBpc1RhYmxldFxyXG4gICk7XHJcblxyXG4gIGNvbnN0IHtcclxuICAgIGhhbmRsZUNoZWNrYm94Q2hhbmdlLFxyXG4gICAgaGFuZGxlU2VhcmNoQ2hhbmdlLFxyXG4gICAgaGFuZGxlQ291bnRyeUNoYW5nZSxcclxuICAgIGhhbmRsZUNsZWFyRmlsdGVycyxcclxuICB9ID0gdXNlRmlsdGVySGFuZGxlcnMoe1xyXG4gICAgc2V0RmllbGRWYWx1ZSxcclxuICAgIHZhbHVlcyxcclxuICAgIHBhdGhuYW1lLFxyXG4gICAgc2V0UGFnZU51bWJlcixcclxuICAgIHNldFNlbGVjdGVkRmlsdGVycyxcclxuICB9KTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgaWQ9XCJmaWx0ZXItYWN0aW9uc1wiIGNsYXNzTmFtZT17YGZpbHRlci1wb3B1cCAke2lzT3BlbiA/IFwib3BlblwiIDogXCJcIn1gfT5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaWx0ZXItcG9wdXAtY29udGVudFwiPlxyXG4gICAgICAgIHtpc01vYmlsZSAmJiAoXHJcbiAgICAgICAgICA8RmlsdGVyQWNjb3JkaW9uXHJcbiAgICAgICAgICAgIHRpdGxlPVwiU2VhcmNoXCJcclxuICAgICAgICAgICAgZXhwYW5kZWQ9e2V4cGFuZGVkU2VjdGlvbnMuc2VhcmNofVxyXG4gICAgICAgICAgICBvbkNoYW5nZT17KCkgPT4gdG9nZ2xlU2VjdGlvbihcInNlYXJjaFwiKX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPFNlYXJjaEZpZWxkXHJcbiAgICAgICAgICAgICAgdmFsdWU9e3ZhbHVlcy5rZXlXb3JkfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVTZWFyY2hDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3QoXCJTZWFyY2hcIil9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L0ZpbHRlckFjY29yZGlvbj5cclxuICAgICAgICApfVxyXG4gICAgICAgIHtpc01vYmlsZSAmJiAoXHJcbiAgICAgICAgICA8RmlsdGVyQWNjb3JkaW9uXHJcbiAgICAgICAgICAgIHRpdGxlPVwiQ291bnRyeVwiXHJcbiAgICAgICAgICAgIGV4cGFuZGVkPXtleHBhbmRlZFNlY3Rpb25zLmNvdW50cnl9XHJcbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiB0b2dnbGVTZWN0aW9uKFwiY291bnRyeVwiKX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPENvdW50cnlTZWxlY3RvclxyXG4gICAgICAgICAgICAgIHZhbHVlPXt2YWx1ZXMuY291bnRyeX1cclxuICAgICAgICAgICAgICBvcHRpb25zPXtjb3VudHJpZXN9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNvdW50cnlDaGFuZ2V9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L0ZpbHRlckFjY29yZGlvbj5cclxuICAgICAgICApfVxyXG4gICAgICAgIHsham9iSW5kdXN0cnkgJiYgKFxyXG4gICAgICAgICAgPEZpbHRlckFjY29yZGlvblxyXG4gICAgICAgICAgICB0aXRsZT1cIkluZHVzdHJ5XCJcclxuICAgICAgICAgICAgZXhwYW5kZWQ9e2V4cGFuZGVkU2VjdGlvbnMuaW5kdXN0cnl9XHJcbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiB0b2dnbGVTZWN0aW9uKFwiaW5kdXN0cnlcIil9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxDaGVja2JveEdyb3VwXHJcbiAgICAgICAgICAgICAgb3B0aW9ucz17SU5EVVNUUllfT1BUSU9OU31cclxuICAgICAgICAgICAgICB2YWx1ZXM9e3ZhbHVlc31cclxuICAgICAgICAgICAgICBjYXRlZ29yeT1cImluZHVzdHJ5XCJcclxuICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlQ2hlY2tib3hDaGFuZ2V9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L0ZpbHRlckFjY29yZGlvbj5cclxuICAgICAgICApfVxyXG4gICAgICAgIDxGaWx0ZXJBY2NvcmRpb25cclxuICAgICAgICAgIHRpdGxlPVwiQ29udHJhY3QgVHlwZVwiXHJcbiAgICAgICAgICBleHBhbmRlZD17ZXhwYW5kZWRTZWN0aW9ucy5jb250cmFjdH1cclxuICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiB0b2dnbGVTZWN0aW9uKFwiY29udHJhY3RcIil9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPENoZWNrYm94R3JvdXBcclxuICAgICAgICAgICAgb3B0aW9ucz17Q09OVFJBQ1RfT1BUSU9OU31cclxuICAgICAgICAgICAgdmFsdWVzPXt2YWx1ZXN9XHJcbiAgICAgICAgICAgIGNhdGVnb3J5PVwiY29udHJhY3RUeXBlXCJcclxuICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUNoZWNrYm94Q2hhbmdlfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L0ZpbHRlckFjY29yZGlvbj5cclxuICAgICAgICA8RmlsdGVyQWNjb3JkaW9uXHJcbiAgICAgICAgICB0aXRsZT1cIkxhbmd1YWdlXCJcclxuICAgICAgICAgIGV4cGFuZGVkPXtleHBhbmRlZFNlY3Rpb25zLmxhbmd1YWdlfVxyXG4gICAgICAgICAgb25DaGFuZ2U9eygpID0+IHRvZ2dsZVNlY3Rpb24oXCJsYW5ndWFnZVwiKX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8Q2hlY2tib3hHcm91cFxyXG4gICAgICAgICAgICBvcHRpb25zPXtMQU5HVUFHRV9PUFRJT05TfVxyXG4gICAgICAgICAgICB2YWx1ZXM9e3ZhbHVlc31cclxuICAgICAgICAgICAgY2F0ZWdvcnk9XCJqb2JEZXNjcmlwdGlvbkxhbmd1YWdlc1wiXHJcbiAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGVja2JveENoYW5nZX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9GaWx0ZXJBY2NvcmRpb24+XHJcbiAgICAgICAgPEZpbHRlckFjY29yZGlvblxyXG4gICAgICAgICAgdGl0bGU9XCJMZXZlbCBvZiBFeHBlcmllbmNlXCJcclxuICAgICAgICAgIGV4cGFuZGVkPXtleHBhbmRlZFNlY3Rpb25zLmV4cGVyaWVuY2V9XHJcbiAgICAgICAgICBvbkNoYW5nZT17KCkgPT4gdG9nZ2xlU2VjdGlvbihcImV4cGVyaWVuY2VcIil9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPENoZWNrYm94R3JvdXBcclxuICAgICAgICAgICAgb3B0aW9ucz17RVhQRVJJRU5DRV9PUFRJT05TfVxyXG4gICAgICAgICAgICB2YWx1ZXM9e3ZhbHVlc31cclxuICAgICAgICAgICAgY2F0ZWdvcnk9XCJsZXZlbE9mRXhwZXJpZW5jZVwiXHJcbiAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGVja2JveENoYW5nZX1cclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgPC9GaWx0ZXJBY2NvcmRpb24+XHJcbiAgICAgICAgPEZpbHRlckFjdGlvbnMgb25DbGVhcj17aGFuZGxlQ2xlYXJGaWx0ZXJzfSAvPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBGaWx0ZXJQb3B1cDtcclxuIl0sIm5hbWVzIjpbInVzZU1lZGlhUXVlcnkiLCJ1c2VUaGVtZSIsInVzZVBhdGhuYW1lIiwiRmlsdGVyQWNjb3JkaW9uIiwiQ2hlY2tib3hHcm91cCIsIlNlYXJjaEZpZWxkIiwiQ291bnRyeVNlbGVjdG9yIiwiRmlsdGVyQWN0aW9ucyIsIklORFVTVFJZX09QVElPTlMiLCJDT05UUkFDVF9PUFRJT05TIiwiTEFOR1VBR0VfT1BUSU9OUyIsIkVYUEVSSUVOQ0VfT1BUSU9OUyIsInVzZUZpbHRlclNlY3Rpb25zIiwidXNlRmlsdGVySGFuZGxlcnMiLCJGaWx0ZXJQb3B1cCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJzZXRGaWVsZFZhbHVlIiwidmFsdWVzIiwidCIsImNvdW50cmllcyIsInNldFBhZ2VOdW1iZXIiLCJqb2JJbmR1c3RyeSIsInNldFNlbGVjdGVkRmlsdGVycyIsInRoZW1lIiwiaXNNb2JpbGUiLCJicmVha3BvaW50cyIsImRvd24iLCJpc1RhYmxldCIsInBhdGhuYW1lIiwiZXhwYW5kZWRTZWN0aW9ucyIsInRvZ2dsZVNlY3Rpb24iLCJoYW5kbGVDaGVja2JveENoYW5nZSIsImhhbmRsZVNlYXJjaENoYW5nZSIsImhhbmRsZUNvdW50cnlDaGFuZ2UiLCJoYW5kbGVDbGVhckZpbHRlcnMiLCJkaXYiLCJpZCIsImNsYXNzTmFtZSIsInRpdGxlIiwiZXhwYW5kZWQiLCJzZWFyY2giLCJvbkNoYW5nZSIsInZhbHVlIiwia2V5V29yZCIsInBsYWNlaG9sZGVyIiwiY291bnRyeSIsIm9wdGlvbnMiLCJpbmR1c3RyeSIsImNhdGVnb3J5IiwiY29udHJhY3QiLCJsYW5ndWFnZSIsImV4cGVyaWVuY2UiLCJvbkNsZWFyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/opportunity/components/opportunityFrontOffice/FilterPopup.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/features/opportunity/hooks/useFilterSections.js":
/*!*************************************************************!*\
  !*** ./src/features/opportunity/hooks/useFilterSections.js ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _s = $RefreshSig$();\n\nconst useFilterSections = (isMobile, isTablet)=>{\n    _s();\n    const [expandedSections, setExpandedSections] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        industry: !isMobile && !isTablet,\n        contract: !isMobile && !isTablet,\n        search: isMobile || isTablet,\n        country: false,\n        language: false,\n        experience: false\n    });\n    const toggleSection = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((section)=>{\n        setExpandedSections((prev)=>({\n                ...prev,\n                [section]: !prev[section]\n            }));\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setExpandedSections((prev)=>({\n                ...prev,\n                industry: !isMobile && !isTablet,\n                contract: !isMobile && !isTablet,\n                search: isMobile || isTablet\n            }));\n    }, [\n        isMobile,\n        isTablet\n    ]);\n    return {\n        expandedSections,\n        toggleSection\n    };\n};\n_s(useFilterSections, \"n27ub1r4QlHs5PFELHg0GPkkHX8=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (useFilterSections);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9vcHBvcnR1bml0eS9ob29rcy91c2VGaWx0ZXJTZWN0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQXlEO0FBRXpELE1BQU1HLG9CQUFvQixDQUFDQyxVQUFVQzs7SUFDbkMsTUFBTSxDQUFDQyxrQkFBa0JDLG9CQUFvQixHQUFHUCwrQ0FBUUEsQ0FBQztRQUN2RFEsVUFBVSxDQUFDSixZQUFZLENBQUNDO1FBQ3hCSSxVQUFVLENBQUNMLFlBQVksQ0FBQ0M7UUFDeEJLLFFBQVFOLFlBQVlDO1FBQ3BCTSxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsWUFBWTtJQUNkO0lBRUEsTUFBTUMsZ0JBQWdCWixrREFBV0EsQ0FBQyxDQUFDYTtRQUNqQ1Isb0JBQW9CLENBQUNTLE9BQVU7Z0JBQzdCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0QsUUFBUSxFQUFFLENBQUNDLElBQUksQ0FBQ0QsUUFBUTtZQUMzQjtJQUNGLEdBQUcsRUFBRTtJQUVMZCxnREFBU0EsQ0FBQztRQUNSTSxvQkFBb0IsQ0FBQ1MsT0FBVTtnQkFDN0IsR0FBR0EsSUFBSTtnQkFDUFIsVUFBVSxDQUFDSixZQUFZLENBQUNDO2dCQUN4QkksVUFBVSxDQUFDTCxZQUFZLENBQUNDO2dCQUN4QkssUUFBUU4sWUFBWUM7WUFDdEI7SUFDRixHQUFHO1FBQUNEO1FBQVVDO0tBQVM7SUFFdkIsT0FBTztRQUFFQztRQUFrQlE7SUFBYztBQUMzQztHQTNCTVg7QUE2Qk4sK0RBQWVBLGlCQUFpQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvZmVhdHVyZXMvb3Bwb3J0dW5pdHkvaG9va3MvdXNlRmlsdGVyU2VjdGlvbnMuanM/YWUzMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcclxuXHJcbmNvbnN0IHVzZUZpbHRlclNlY3Rpb25zID0gKGlzTW9iaWxlLCBpc1RhYmxldCkgPT4ge1xyXG4gIGNvbnN0IFtleHBhbmRlZFNlY3Rpb25zLCBzZXRFeHBhbmRlZFNlY3Rpb25zXSA9IHVzZVN0YXRlKHtcclxuICAgIGluZHVzdHJ5OiAhaXNNb2JpbGUgJiYgIWlzVGFibGV0LFxyXG4gICAgY29udHJhY3Q6ICFpc01vYmlsZSAmJiAhaXNUYWJsZXQsXHJcbiAgICBzZWFyY2g6IGlzTW9iaWxlIHx8IGlzVGFibGV0LFxyXG4gICAgY291bnRyeTogZmFsc2UsXHJcbiAgICBsYW5ndWFnZTogZmFsc2UsXHJcbiAgICBleHBlcmllbmNlOiBmYWxzZSxcclxuICB9KTtcclxuXHJcbiAgY29uc3QgdG9nZ2xlU2VjdGlvbiA9IHVzZUNhbGxiYWNrKChzZWN0aW9uKSA9PiB7XHJcbiAgICBzZXRFeHBhbmRlZFNlY3Rpb25zKChwcmV2KSA9PiAoe1xyXG4gICAgICAuLi5wcmV2LFxyXG4gICAgICBbc2VjdGlvbl06ICFwcmV2W3NlY3Rpb25dLFxyXG4gICAgfSkpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIHNldEV4cGFuZGVkU2VjdGlvbnMoKHByZXYpID0+ICh7XHJcbiAgICAgIC4uLnByZXYsXHJcbiAgICAgIGluZHVzdHJ5OiAhaXNNb2JpbGUgJiYgIWlzVGFibGV0LFxyXG4gICAgICBjb250cmFjdDogIWlzTW9iaWxlICYmICFpc1RhYmxldCxcclxuICAgICAgc2VhcmNoOiBpc01vYmlsZSB8fCBpc1RhYmxldCxcclxuICAgIH0pKTtcclxuICB9LCBbaXNNb2JpbGUsIGlzVGFibGV0XSk7XHJcblxyXG4gIHJldHVybiB7IGV4cGFuZGVkU2VjdGlvbnMsIHRvZ2dsZVNlY3Rpb24gfTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IHVzZUZpbHRlclNlY3Rpb25zO1xyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsInVzZUZpbHRlclNlY3Rpb25zIiwiaXNNb2JpbGUiLCJpc1RhYmxldCIsImV4cGFuZGVkU2VjdGlvbnMiLCJzZXRFeHBhbmRlZFNlY3Rpb25zIiwiaW5kdXN0cnkiLCJjb250cmFjdCIsInNlYXJjaCIsImNvdW50cnkiLCJsYW5ndWFnZSIsImV4cGVyaWVuY2UiLCJ0b2dnbGVTZWN0aW9uIiwic2VjdGlvbiIsInByZXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/opportunity/hooks/useFilterSections.js\n"));

/***/ })

});