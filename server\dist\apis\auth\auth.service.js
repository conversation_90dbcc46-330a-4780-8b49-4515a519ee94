"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const services_1 = require("@/utils/services");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const user_model_1 = __importDefault(require("../user/user.model"));
const alert_model_1 = __importDefault(require("../alert/alert.model"));
const settings_model_1 = __importDefault(require("../settings/settings.model"));
const account_service_1 = __importDefault(require("../user/services/account.service"));
const constants_1 = require("@/utils/helpers/constants");
const candidat_model_1 = __importDefault(require("../candidat/candidat.model"));
const messages_1 = require("@/utils/helpers/messages");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const mailchecker_1 = __importDefault(require("mailchecker"));
const socket_1 = require("@/utils/config/socket");
const login_records_model_1 = __importDefault(require("./login-records/login-records.model"));
class AuthService {
    constructor() {
        this.User = user_model_1.default;
        this.UserSettings = settings_model_1.default;
        this.Candidat = candidat_model_1.default;
        this.accountService = new account_service_1.default();
        this.LoginRecord = login_records_model_1.default;
        this.resetPassword = async (userInfo, token) => {
            await this.accountService.resetPassword(token, userInfo);
        };
    }
    async signUp(signUpData) {
        const { email, password, firstName, lastName, phone, country, industry } = signUpData;
        if (!mailchecker_1.default.isValid(email)) {
            throw new http_exception_1.default(400, messages_1.MESSAGES.AUTH.INVALID_Email);
        }
        const existingUser = await this.User.findOne({ email: email.toLowerCase() });
        if (existingUser)
            throw new http_exception_1.default(409, messages_1.MESSAGES.AUTH.EMAIL_ALREADY_EXIST);
        const hashedPassword = await services_1.auth.hashPassword(password);
        const createdUser = await this.User.create({
            ...signUpData,
            password: hashedPassword,
            roles: constants_1.Role.CANDIDATE,
        });
        const token = jsonwebtoken_1.default.sign({ userId: createdUser._id }, process.env.Acc_Creation, { expiresIn: '24h' });
        createdUser.confirmAccountToken = token;
        await createdUser.save();
        const createdCandidat = await this.Candidat.create({
            user: createdUser._id,
            phones: [phone],
            emails: [email],
            fullName: firstName + ' ' + lastName,
            country,
            industry,
            industries: industry,
            profilePercentage: 20,
        });
        await this.User.findByIdAndUpdate(createdUser._id, { candidate: createdCandidat._id });
        const defaultUserSettings = {
            user: createdUser._id,
            notifications: {
                newJobAlerts: {
                    email: true,
                    website: true,
                },
                appliedJobStatusUpdates: {
                    email: true,
                    website: true,
                },
                newsLetter: {
                    email: true,
                    website: true,
                },
            },
        };
        await this.UserSettings.create(defaultUserSettings);
        const alertData = {
            country,
            industry,
            isActive: true,
        };
        const createdAlert = await alert_model_1.default.create({
            ...alertData,
            createdBy: createdUser._id,
        });
        await this.User.findByIdAndUpdate(createdUser._id, { alerts: createdAlert._id });
        (0, services_1.sendEmail)({
            to: email,
            subject: 'Activate Your New Account',
            template: 'activationAccount',
            context: {
                firstName: createdUser.firstName,
                activationLink: `${process.env.LOGIN_LINK}/activation?token=${token}`,
            },
        });
        const notificationMessage = 'Welcome to Pentabell,we are happy to see you here!';
        await (0, socket_1.sendNotification)({
            receiver: createdUser._id,
            sender: null,
            message: notificationMessage,
            link: `${process.env.LOGIN_LINK}`,
        });
        await this.LoginRecord.create({ user: createdUser._id, type: 'register' });
        return token;
    }
    async resendActivationEmail(token) {
        const decodedToken = jsonwebtoken_1.default.decode(token);
        const { userId } = decodedToken;
        const user = await this.User.findById(userId);
        if (!user)
            throw new http_exception_1.default(404, 'User not found.');
        if (user.isActive)
            throw new http_exception_1.default(409, 'Account already activated.');
        const newToken = jsonwebtoken_1.default.sign({ userId: user._id, isActive: user.isActive }, process.env.Acc_Creation, { expiresIn: '1h' });
        user.confirmAccountToken = newToken;
        await user.save();
        (0, services_1.sendEmail)({
            to: user.email,
            subject: 'Activate your account',
            template: 'resendactivation',
            context: {
                firstName: user.firstName,
                activationLink: `${process.env.LOGIN_LINK}/activation?token=${newToken}`,
            },
        });
    }
    async activateAccount(token) {
        const decodedToken = jsonwebtoken_1.default.decode(token);
        if (!decodedToken || !decodedToken.userId)
            throw new http_exception_1.default(400, 'Invalid Token');
        if (decodedToken.exp < Math.floor(new Date().getTime() / 1000)) {
            return await this.resendActivationEmail(token);
        }
        const { userId } = decodedToken;
        const user = await this.User.findById(userId);
        if (!user)
            throw new http_exception_1.default(404, 'User not found.');
        if (user.isActive === true)
            throw new http_exception_1.default(409, 'Account already activated.');
        user.isActive = true;
        await user.save();
    }
    async updateUser(userId, userData) {
        const { firstName, lastName, email, jobTitle, country, password, phone, roles, certifications, educations, experiences } = userData;
        const user = await this.User.findById(userId);
        if (!user)
            throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
        await this.User.findByIdAndUpdate(userId, {
            $set: {
                firstName,
                lastName,
                email,
                jobTitle,
                country,
                password,
                phone,
                roles: roles && roles.length ? roles : [constants_1.Role.CANDIDATE],
            },
            $push: {
                certifications: { $each: certifications || [] },
                educations: { $each: educations || [] },
                experiences: { $each: experiences || [] },
            },
        });
    }
    async checkUserExistence(token) {
        try {
            const { payload, expired } = await services_1.auth.verifyToken(token, String(process.env.RESET_PASSWORD_TOKEN_PRIVATE_KEY));
            if (expired) {
                console.error('Le token a expiré ou est invalide.');
                return { exist: false };
            }
            if (!payload) {
                console.error('Le payload est null.');
                return { exist: false };
            }
            if (typeof payload === 'object' && '_id' in payload) {
                const user = await this.User.findById(payload._id).select('exist');
                if (!user) {
                    console.error("Utilisateur non trouvé pour l'ID:", payload._id);
                    return { exist: false };
                }
                console.log("Existence de l'utilisateur:", user.exist);
                return { exist: user.exist };
            }
            else {
                console.error('Payload non valide :', payload);
                return { exist: false };
            }
        }
        catch (error) {
            console.error("Erreur lors de la vérification de l'existence de l'utilisateur:");
            return { exist: false };
        }
    }
    async getByEmail(email) {
        const user = await this.User.findOne({ email: new RegExp(`^${email}$`, 'i') });
        if (!user)
            throw new http_exception_1.default(404, messages_1.MESSAGES.USER.USER_NOT_FOUND);
        return user;
    }
    async signIn(signInData) {
        const { email, password, rememberMe } = signInData;
        const user = await this.getByEmail(email);
        if (!user.get('password', null, { getters: false }))
            throw new http_exception_1.default(403, 'Please login with your Google/Microsoft account!');
        if (user.isActive === false)
            throw new http_exception_1.default(403, 'Please activate your account before signing in.');
        // if (user.resetPasswordToken && user.resetPasswordToken !== '') throw new HttpException(403, 'Reset your password to log in!');
        const payload = { _id: user._id, roles: user.roles, profilePicture: user.profilePicture, candidate: user.candidate };
        const isMatched = await services_1.auth.comparePassword(password, user.get('password', null, { getters: false }));
        if (!isMatched) {
            throw new http_exception_1.default(401, messages_1.MESSAGES.AUTH.PASSWORD_INCORRECT);
        }
        if (user.isArchived === true) {
            throw new http_exception_1.default(403, messages_1.MESSAGES.AUTH.ARCHIVED);
        }
        const accessToken = await services_1.auth.generateToken(payload, String(process.env.ACCESS_TOKEN_PRIVATE_KEY), String(process.env.ACCESS_TOKEN_TIME));
        const refreshTokenExpiration = rememberMe ? process.env.LONG_REFRESH_TOKEN_TIME : process.env.REFRESH_TOKEN_TIME;
        const refreshToken = await services_1.auth.generateToken(payload, String(process.env.REFRESH_TOKEN_PRIVATE_KEY), String(refreshTokenExpiration));
        await this.LoginRecord.create({ user: user._id, type: 'login' });
        return { user, accessToken, refreshToken };
    }
    async forgotPassword(email) {
        const user = await this.getByEmail(email);
        user.exist = Boolean(user.password);
        await user.save();
        const payload = { _id: user._id, roles: user.roles };
        const token = await services_1.auth.generateToken(payload, String(process.env.RESET_PASSWORD_TOKEN_PRIVATE_KEY), String(process.env.RESET_PASSWORD_TIME));
        user.resetPasswordToken = token;
        await user.save();
        (0, services_1.sendEmail)({
            to: user.email,
            subject: 'Reset Your Password',
            template: 'resetPwd',
            context: {
                firstName: user.firstName,
                email: user.email,
                baseUrl: process.env.APP_PORT,
                link: `${process.env.RESET_PASSWORD_URL_FRONT}?token=${token}`,
            },
        });
        return { exist: user.exist, token };
    }
}
exports.default = AuthService;
//# sourceMappingURL=auth.service.js.map