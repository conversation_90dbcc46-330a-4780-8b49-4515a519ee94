"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const client_service_1 = __importDefault(require("../services/client.service"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const client_validations_1 = require("../client.validations");
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const cache_middleware_1 = require("@/middlewares/cache.middleware");
class ClientController {
    constructor() {
        this.path = '/clients';
        this.router = (0, express_1.Router)();
        this.clientService = new client_service_1.default();
        this.upload = (0, multer_1.default)();
        this.getAll = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.clientService.getAll(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.create = async (request, response, next) => {
            try {
                const isLogoExists = !!request.file;
                let file;
                if (isLogoExists)
                    file = request.file;
                const clientData = request.body;
                const result = await this.clientService.create(clientData, file);
                response.status(201).send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.createMany = async (request, response, next) => {
            try {
                const file = request.file;
                if (!file) {
                    return response.status(400).send({
                        message: messages_1.MESSAGES.FILE.NO_FILE,
                    });
                }
                const result = await this.clientService.createMany(file);
                response.status(201).send({
                    message: messages_1.MESSAGES.CLIENTS.CLIENTS_IMPORTED,
                    clients: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.get = async (request, response, next) => {
            try {
                const id = request.params.id;
                const result = await this.clientService.get(id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.delete = async (request, response, next) => {
            try {
                const id = request.params.id;
                await this.clientService.delete(id);
                response.send({
                    message: messages_1.MESSAGES.CLIENTS.CLIENT_DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const id = request.params.id;
                const client = request.body;
                const result = await this.clientService.update(id, client);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getManagers = async (request, response, next) => {
            try {
                const clientId = request.params.id;
                const result = await this.clientService.getManagers(clientId);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getAll);
        this.router.get(`${this.path}/:id/managers/`, validateApiKey_middleware_1.default, authentication_middleware_1.default, mongoId_validation_middleware_1.default, cache_middleware_1.validateCache, this.getManagers);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), (0, validation_middleware_1.validationMiddleware)(client_validations_1.createClientSchema), cache_middleware_1.invalidateCache, this.create);
        this.router.post(`${this.path}/import`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.upload.single('file'), cache_middleware_1.invalidateCache, this.createMany);
        this.router.get(`${this.path}/:id`, validateApiKey_middleware_1.default, authentication_middleware_1.default, mongoId_validation_middleware_1.default, cache_middleware_1.validateCache, this.get);
        this.router.delete(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.delete);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.update);
    }
}
exports.default = ClientController;
//# sourceMappingURL=client.controller.js.map