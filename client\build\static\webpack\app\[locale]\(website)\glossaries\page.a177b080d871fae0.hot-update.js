"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx":
/*!*******************************************************************!*\
  !*** ./src/features/glossary/component/GlossariesListWebsite.jsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryListWebsite; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction GlossaryListWebsite(param) {\n    let { glossaries } = param;\n    _s();\n    const letters = Object.keys(glossaries);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleToggle = ()=>{\n        setExpanded(!expanded);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"custom-max-width\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                container: true,\n                children: (letters === null || letters === void 0 ? void 0 : letters.length) > 0 && (letters === null || letters === void 0 ? void 0 : letters.map((letter, index)=>{\n                    var _glossaries_letter, _glossaries_letter1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        item: true,\n                        lg: 3,\n                        md: 4,\n                        sm: 6,\n                        xs: 12,\n                        className: \"letters\",\n                        id: letter,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"length\",\n                                children: (_glossaries_letter = glossaries[letter]) === null || _glossaries_letter === void 0 ? void 0 : _glossaries_letter.length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 30,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"letter\",\n                                children: letter\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 31,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w\",\n                                children: (expanded ? glossaries[letter] : glossaries[letter].slice(0, 5)).map((glossary)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"word\",\n                                        href: \"#\".concat(glossary),\n                                        children: glossary\n                                    }, glossary, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 32,\n                                columnNumber: 17\n                            }, this),\n                            ((_glossaries_letter1 = glossaries[letter]) === null || _glossaries_letter1 === void 0 ? void 0 : _glossaries_letter1.length) > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"glossary-button\",\n                                onClick: handleToggle,\n                                children: expanded ? \"Show less\" : \"Show more\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 43,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                        lineNumber: 20,\n                        columnNumber: 15\n                    }, this);\n                }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryListWebsite, \"DuL5jiiQQFgbn7gBKAyxwS/H4Ek=\");\n_c = GlossaryListWebsite;\nvar _c;\n$RefreshReg$(_c, \"GlossaryListWebsite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx\n"));

/***/ })

});