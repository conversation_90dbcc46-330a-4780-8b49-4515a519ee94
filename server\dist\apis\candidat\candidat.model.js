"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const mongoose_2 = __importDefault(require("mongoose"));
const candidatSchema = new mongoose_1.Schema({
    cv: [
        {
            fileName: { type: String, required: true },
            originalName: { type: String, required: true },
            publishDate: { type: Date },
        },
    ],
    summary: {
        type: String,
        default: '',
    },
    cvBase64: { type: String },
    user: {
        type: mongoose_2.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
    certifications: [
        {
            type: mongoose_2.default.Schema.Types.ObjectId,
            ref: 'Certification',
            required: true,
        },
    ],
    flatText: String,
    educations: [
        {
            type: mongoose_2.default.Schema.Types.ObjectId,
            ref: 'Education',
            required: true,
        },
    ],
    profilePercentage: {
        type: Number,
        required: false,
        min: 0,
        max: 100,
        default: 0,
    },
    emails: [String],
    phones: [String],
    nationalities: [String],
    experiences: [
        {
            type: mongoose_2.default.Schema.Types.ObjectId,
            ref: 'Experience',
            required: true,
        },
    ],
    industry: {
        type: String,
        enum: Object.values(constants_1.IndustryCandidat),
    },
    industries: {
        type: [String],
        enum: constants_1.IndustryCandidat,
    },
    jobTitleAng: {
        type: String,
    },
    jobTitleFr: String,
    country: {
        type: String,
        index: true,
        enum: constants_1.Countries,
    },
    linkedinUrl: String,
    monthsOfExperiences: Number,
    numberOfCertifications: Number,
    numberOfEducations: Number,
    numberOfExperiences: Number,
    numberOfSkills: Number,
    webSiteUrl: String,
    skills: [String],
    shortList: [
        {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Opportunity',
        },
    ],
    shortListArticle: [
        {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'Article',
        },
    ],
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
    discriminatorKey: 'type',
});
exports.default = (0, mongoose_1.model)('Candidat', candidatSchema);
//# sourceMappingURL=candidat.model.js.map