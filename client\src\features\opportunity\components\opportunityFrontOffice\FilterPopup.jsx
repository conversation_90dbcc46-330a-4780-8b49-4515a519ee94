import { useMediaQuery, useTheme } from "@mui/material";
import { usePathname } from "next/navigation";
import {
  FilterAccordion,
  CheckboxGroup,
  SearchField,
  CountrySelector,
  FilterActions,
} from "./FilterComponents";
import {
  INDUSTRY_OPTIONS,
  CONTRACT_OPTIONS,
  LANGUAGE_OPTIONS,
  EXPERIENCE_OPTIONS,
} from "../../constants/filterOptions";

import { useFilterSections, useFilterHandlers } from "../../hooks";

const FilterPopup = ({
  isOpen,
  onClose,
  setFieldValue,
  values,
  t,
  countries,
  setPageNumber,
  jobIndustry,
  setSelectedFilters,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));
  const pathname = usePathname();

  const { expandedSections, toggleSection } = useFilterSections(
    isMobile,
    isTablet
  );

  const {
    handleCheckboxChange,
    handleSearchChange,
    handleCountryChange,
    handleClearFilters,
  } = useFilterHandlers({
    setFieldValue,
    values,
    pathname,
    setPageNumber,
    setSelectedFilters,
  });

  return (
    <div id="filter-actions" className={`filter-popup ${isOpen ? "open" : ""}`}>
      <div className="filter-popup-content">
        {isMobile && (
          <FilterAccordion
            title="Search"
            expanded={expandedSections.search}
            onChange={() => toggleSection("search")}
          >
            <SearchField
              value={values.keyWord}
              onChange={handleSearchChange}
              placeholder={t("Search")}
            />
          </FilterAccordion>
        )}
        {isMobile && (
          <FilterAccordion
            title="Country"
            expanded={expandedSections.country}
            onChange={() => toggleSection("country")}
          >
            <CountrySelector
              value={values.country}
              options={countries}
              onChange={handleCountryChange}
            />
          </FilterAccordion>
        )}
        {!jobIndustry && (
          <FilterAccordion
            title="Industry"
            expanded={expandedSections.industry}
            onChange={() => toggleSection("industry")}
          >
            <CheckboxGroup
              options={INDUSTRY_OPTIONS}
              values={values}
              category="industry"
              onChange={handleCheckboxChange}
            />
          </FilterAccordion>
        )}
        <FilterAccordion
          title="Contract Type"
          expanded={expandedSections.contract}
          onChange={() => toggleSection("contract")}
        >
          <CheckboxGroup
            options={CONTRACT_OPTIONS}
            values={values}
            category="contractType"
            onChange={handleCheckboxChange}
          />
        </FilterAccordion>
        <FilterAccordion
          title="Language"
          expanded={expandedSections.language}
          onChange={() => toggleSection("language")}
        >
          <CheckboxGroup
            options={LANGUAGE_OPTIONS}
            values={values}
            category="jobDescriptionLanguages"
            onChange={handleCheckboxChange}
          />
        </FilterAccordion>
        <FilterAccordion
          title="Level of Experience"
          expanded={expandedSections.experience}
          onChange={() => toggleSection("experience")}
        >
          <CheckboxGroup
            options={EXPERIENCE_OPTIONS}
            values={values}
            category="levelOfExperience"
            onChange={handleCheckboxChange}
          />
        </FilterAccordion>
        <FilterActions onClear={handleClearFilters} />
      </div>
    </div>
  );
};

export default FilterPopup;
