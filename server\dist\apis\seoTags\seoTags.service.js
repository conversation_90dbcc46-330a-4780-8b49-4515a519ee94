"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const seoTags_model_1 = __importDefault(require("./seoTags.model"));
const messages_1 = require("@/utils/helpers/messages");
const constants_1 = require("@/utils/helpers/constants");
class SeoTagsService {
    constructor() {
        this.seoTags = seoTags_model_1.default;
    }
    async create(seoData) {
        const oldSeo = await this.seoTags.findOne({
            slug: seoData.slug,
        });
        if (oldSeo) {
            throw new http_exception_1.default(409, `Seo Tags with this slug: '${oldSeo.slug}' already exists`);
        }
        const defaultLanguage = constants_1.Language.ENGLISH;
        for (const version of seoData.versions) {
            if (!version.language) {
                version.language = defaultLanguage;
            }
        }
        return await this.seoTags.create(seoData);
    }
    async update(id, seoData) {
        const existingSeo = await this.seoTags.findOne({ _id: id });
        if (!existingSeo) {
            throw new http_exception_1.default(404, `Seo Tags with slug: '${seoData.slug}' not found`);
        }
        if (seoData.versions) {
            for (const newVersion of seoData.versions) {
                const index = existingSeo.versions.findIndex(v => v.language === newVersion.language);
                if (index !== -1) {
                    existingSeo.versions[index] = {
                        ...existingSeo.versions[index],
                        ...newVersion,
                    };
                }
                else {
                    existingSeo.versions.push(newVersion);
                }
            }
        }
        if (seoData.robotMeta) {
            existingSeo.robotMeta = seoData.robotMeta;
        }
        if (seoData.slug && seoData.slug !== existingSeo.slug) {
            const slugExists = await this.seoTags.findOne({ slug: seoData.slug });
            if (slugExists) {
                throw new http_exception_1.default(409, `Seo Tags with slug: '${seoData.slug}' already exists`);
            }
            existingSeo.slug = seoData.slug;
        }
        return await existingSeo.save();
    }
    async getSeoTagBySlug(slug, language) {
        const projection = {
            slug: 1,
            robotMeta: 1,
            createdAt: 1,
            updatedAt: 1,
            versions: { $elemMatch: { language: language } },
        };
        const tags = await this.seoTags.findOne({ slug, versions: { $elemMatch: { language } } }, projection);
        if (!tags)
            throw new http_exception_1.default(204, messages_1.MESSAGES.SEOTAGS.SEO_NOT_FOUND + slug);
        return tags;
    }
    async getSeoTagById(id) {
        const tags = await this.seoTags.findById(id);
        if (!tags)
            throw new http_exception_1.default(204, messages_1.MESSAGES.SEOTAGS.SEO_ID_NOT_FOUND + id);
        return tags;
    }
    async getAll(queries) {
        const { slug, paginated, language, sortOrder } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 10;
        const targetLanguage = language || 'en';
        const query = {
            'versions.language': targetLanguage,
        };
        if (slug) {
            query['slug'] = new RegExp(`.*${slug}.*`, 'i');
        }
        const sortCriteria = {};
        if (sortOrder) {
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        }
        let seoTags = [];
        let totalTags;
        let totalPages;
        const projection = {
            slug: 1,
            robotMeta: 1,
            createdAt: 1,
            updatedAt: 1,
            versions: { $elemMatch: { language: targetLanguage } },
        };
        if (paginated && paginated === 'false') {
            seoTags = await this.seoTags.find(query, projection).sort(sortCriteria);
            totalTags = await this.seoTags.countDocuments(query);
            return { totalTags, seoTags };
        }
        else {
            seoTags = await this.seoTags
                .find(query, projection)
                .sort(sortCriteria)
                .skip((pageNumber - 1) * pageSize)
                .limit(pageSize);
            totalTags = await this.seoTags.countDocuments(query);
            totalPages = Math.ceil(totalTags / pageSize);
            return {
                pageNumber,
                pageSize,
                totalTags,
                totalPages,
                seoTags,
            };
        }
    }
}
exports.default = SeoTagsService;
//# sourceMappingURL=seoTags.service.js.map