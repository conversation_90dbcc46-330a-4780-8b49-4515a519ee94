"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CandidateExperienceService = void 0;
const candidat_model_1 = __importDefault(require("../candidat.model"));
const candidat_service_1 = __importDefault(require("../candidat.service"));
const candidat_experience_model_1 = __importDefault(require("./candidat.experience.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
class CandidateExperienceService {
    constructor() {
        this.Candidate = candidat_model_1.default;
        this.candidateService = new candidat_service_1.default();
    }
    async update(candidateId, experienceId, experienceData) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const experienceIndex = candidate.experiences.findIndex((expId) => expId._id.toString() === experienceId);
        if (experienceIndex === -1)
            throw new http_exception_1.default(404, 'Experience not found');
        const existingExperience = await candidat_experience_model_1.default.findById(experienceId);
        if (!existingExperience)
            throw new http_exception_1.default(404, 'Experience not found');
        let monthsOfExperience = 0;
        if (experienceData.endDate) {
            const startDate = new Date(experienceData.startDate);
            const endDate = new Date(experienceData.endDate);
            monthsOfExperience = this.calculateMonthsDifference(startDate, endDate);
        }
        else {
            const startDate = new Date(experienceData.startDate);
            const currentDate = new Date();
            monthsOfExperience = this.calculateMonthsDifference(startDate, currentDate);
        }
        const existingMonths = this.calculateMonthsDifference(existingExperience.startDate, existingExperience.endDate);
        const newMonthOfExperience = candidate.monthsOfExperiences - existingMonths + monthsOfExperience;
        await candidat_model_1.default.findOneAndUpdate({ user: candidateId }, { monthsOfExperiences: newMonthOfExperience });
        await candidat_experience_model_1.default.findByIdAndUpdate(experienceId, { $set: experienceData });
    }
    async add(candidateId, experienceData) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const newExperience = new candidat_experience_model_1.default(experienceData);
        await newExperience.save();
        candidate.experiences = candidate.experiences || [];
        candidate.experiences.push(newExperience._id);
        candidate.experiencesCompany = [...(candidate.experiencesCompany || []), experienceData.company];
        candidate.numberOfExperiences = Math.max((candidate.numberOfExperiences || 0) + 1);
        const startDate = new Date(experienceData.startDate);
        const endDate = experienceData.currentJob ? null : experienceData.endDate ? new Date(experienceData.endDate) : new Date();
        const monthsOfExperience = this.calculateMonthsDifference(startDate, endDate);
        await candidat_model_1.default.findOneAndUpdate({ user: candidateId }, {
            $push: {
                experiences: newExperience._id,
                experiencesCompany: experienceData.company,
            },
            $inc: {
                numberOfExperiences: 1,
                monthsOfExperiences: monthsOfExperience,
            },
        });
        const { completionPercentage } = await this.candidateService.calculateProfileCompletion(candidateId);
        await this.Candidate.findOneAndUpdate({ user: candidateId }, { $set: { profilePercentage: completionPercentage } });
    }
    calculateMonthsDifference(startDate, endDate) {
        const start = new Date(startDate);
        let end;
        if (!endDate) {
            end = new Date();
        }
        else {
            end = new Date(endDate);
        }
        let months = (end.getFullYear() - start.getFullYear()) * 12;
        months -= start.getMonth() + 1;
        months += end.getMonth();
        if (end.getDate() >= start.getDate()) {
            months++;
        }
        return months >= 0 ? months : 0;
    }
    async delete(candidateId, experienceId) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const experienceIndex = candidate.experiences.findIndex((expId) => expId._id.toString() === experienceId);
        if (experienceIndex !== -1) {
            candidate.experiences.splice(experienceIndex, 1);
            candidate.numberOfExperiences = Math.max((candidate.numberOfExperiences || 0) - 1);
            candidate.experiencesCompany = candidate.experiencesCompany || [];
            candidate.experiencesCompany.splice(experienceIndex, 1);
            const existingExperience = await candidat_experience_model_1.default.findById(experienceId);
            if (existingExperience) {
                const existingMonths = this.calculateMonthsDifference(existingExperience.startDate, existingExperience.endDate);
                await candidat_model_1.default.findOneAndUpdate({ user: candidateId }, {
                    monthsOfExperiences: candidate.monthsOfExperiences - existingMonths,
                });
            }
            await candidat_experience_model_1.default.findByIdAndDelete(experienceId);
            await this.Candidate.findByIdAndUpdate(candidate._id, { $set: candidate });
        }
        const { completionPercentage } = await this.candidateService.calculateProfileCompletion(candidateId);
        await this.Candidate.findOneAndUpdate({ user: candidateId }, { $set: { profilePercentage: completionPercentage } });
    }
    async getAll(candidateId) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const experiencesDetails = [];
        for (const experience of candidate.experiences) {
            const experienceDetail = await candidat_experience_model_1.default.findById(experience._id).lean();
            if (experienceDetail) {
                experiencesDetails.push(experienceDetail);
            }
        }
        return experiencesDetails;
    }
}
exports.CandidateExperienceService = CandidateExperienceService;
exports.default = CandidateExperienceService;
//# sourceMappingURL=candidate.experience.service.js.map