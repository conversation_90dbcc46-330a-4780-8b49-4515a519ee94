"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const candidate_shortlist_service_1 = __importDefault(require("./candidate.shortlist.service"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class ShortlistController {
    constructor() {
        this.path = '/candidate/favourite';
        this.router = (0, express_1.Router)();
        this.shortlistService = new candidate_shortlist_service_1.default();
        this.getAllOpportunityinShortList = async (request, response, next) => {
            try {
                const queries = request.query;
                const id = request.user._id;
                const result = await this.shortlistService.getAllOpportunityinShortList(queries, id);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.deletefromShortList = async (request, response, next) => {
            try {
                const opportunityId = request.params.id;
                const candidateId = request.user._id;
                await this.shortlistService.deleteFromShortlist(candidateId, opportunityId);
                response.send({
                    message: messages_1.MESSAGES.CANDIDATE_SHORTLIST.OPPORTUNITY_DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.addOpportunityToShortlist = async (request, response, next) => {
            try {
                const opportunityId = request.params.id;
                const currentUser = request.user;
                const result = await this.shortlistService.addOpportunityToShortlist(opportunityId, currentUser);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), this.getAllOpportunityinShortList);
        this.router.delete(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), mongoId_validation_middleware_1.default, this.deletefromShortList);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), mongoId_validation_middleware_1.default, this.addOpportunityToShortlist);
    }
}
exports.default = ShortlistController;
//# sourceMappingURL=candidate.shortlist.controller.js.map