"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/opportunities/page",{

/***/ "(app-pages-browser)/./src/features/opportunity/hooks/useFilterHandlers.js":
/*!*************************************************************!*\
  !*** ./src/features/opportunity/hooks/useFilterHandlers.js ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\nvar _s = $RefreshSig$();\n\n\nconst useFilterHandlers = (param)=>{\n    let { setFieldValue, values, pathname, setPageNumber, setSelectedFilters } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    const isTablet = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(theme.breakpoints.down(\"md\"));\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleFiltersReset = (event)=>{\n            var _event_detail, _event_detail1;\n            const preserveIndustry = (_event_detail = event.detail) === null || _event_detail === void 0 ? void 0 : _event_detail.preserveIndustry;\n            const preserveCountry = (_event_detail1 = event.detail) === null || _event_detail1 === void 0 ? void 0 : _event_detail1.preserveCountry;\n            setFieldValue(\"jobDescriptionLanguages\", []);\n            setFieldValue(\"levelOfExperience\", []);\n            setFieldValue(\"contractType\", []);\n            setFieldValue(\"keyWord\", []);\n            if (!preserveIndustry) {\n                setFieldValue(\"industry\", []);\n            }\n            if (!preserveCountry) {\n                setFieldValue(\"country\", []);\n            }\n        };\n        window.addEventListener(\"filtersReset\", handleFiltersReset);\n        return ()=>{\n            window.removeEventListener(\"filtersReset\", handleFiltersReset);\n        };\n    }, [\n        setFieldValue\n    ]);\n    const updateUrlAndDispatchEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newParams)=>{\n        const scrollPosition = window.scrollY || document.documentElement.scrollTop;\n        const currentParams = new URLSearchParams(window.location.search);\n        if (currentParams.has(\"list\") && !newParams.has(\"list\")) {\n            newParams.set(\"list\", currentParams.get(\"list\"));\n        }\n        const newUrl = \"\".concat(pathname, \"?\").concat(newParams.toString());\n        window.history.replaceState({\n            path: newUrl\n        }, \"\", newUrl);\n        const paramsObject = {};\n        for (const [key, value] of newParams.entries()){\n            paramsObject[key] = value;\n        }\n        window.dispatchEvent(new CustomEvent(\"filterChanged\", {\n            detail: {\n                params: paramsObject,\n                maintainScroll: true,\n                scrollPosition: scrollPosition\n            }\n        }));\n        window.scrollTo({\n            top: scrollPosition,\n            behavior: \"instant\"\n        });\n    }, [\n        pathname\n    ]);\n    const handleCheckboxChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((category, value)=>{\n        var _values_category, _values_category1;\n        const isRemoving = (_values_category = values[category]) === null || _values_category === void 0 ? void 0 : _values_category.includes(value);\n        const newValues = isRemoving ? (_values_category1 = values[category]) === null || _values_category1 === void 0 ? void 0 : _values_category1.filter((item)=>item !== value) : [\n            ...values[category] || [],\n            value\n        ];\n        setFieldValue(category, newValues);\n        const newParams = new URLSearchParams(window.location.search);\n        if (newValues.length > 0) {\n            newParams.set(category, newValues.join(\",\"));\n        } else {\n            newParams.delete(category);\n        }\n        const currentParams = new URLSearchParams(window.location.search);\n        if (currentParams.has(\"list\") && !newParams.has(\"list\")) {\n            newParams.set(\"list\", currentParams.get(\"list\"));\n        }\n        newParams.set(\"pageNumber\", 1);\n        setPageNumber(1);\n        updateUrlAndDispatchEvent(newParams);\n        setSelectedFilters((prev)=>{\n            const filtered = prev.filter((f)=>f.category !== category);\n            if (newValues.length > 0) {\n                const updatedFilters = [\n                    ...filtered,\n                    ...newValues.map((item)=>({\n                            category,\n                            label: item\n                        }))\n                ];\n                return updatedFilters;\n            }\n            return filtered;\n        });\n        window.dispatchEvent(new CustomEvent(\"checkboxFilterChanged\", {\n            detail: {\n                category,\n                value,\n                newValues,\n                allValues: values,\n                maintainScroll: true,\n                scrollPosition: window.scrollY || document.documentElement.scrollTop\n            }\n        }));\n    }, [\n        values,\n        setFieldValue,\n        setPageNumber,\n        setSelectedFilters,\n        updateUrlAndDispatchEvent\n    ]);\n    const handleSearchChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((e)=>{\n        const inputValue = e.target.value;\n        const newValue = inputValue ? [\n            inputValue\n        ] : [];\n        setFieldValue(\"keyWord\", newValue);\n        // For mobile and tablet, apply filter instantly\n        if (isMobile || isTablet) {\n            const newParams = new URLSearchParams(window.location.search);\n            if (newValue.length > 0 && newValue[0].trim()) {\n                newParams.set(\"keyWord\", newValue[0]);\n            } else {\n                newParams.delete(\"keyWord\");\n            }\n            const currentParams = new URLSearchParams(window.location.search);\n            if (currentParams.has(\"list\") && !newParams.has(\"list\")) {\n                newParams.set(\"list\", currentParams.get(\"list\"));\n            }\n            newParams.set(\"pageNumber\", 1);\n            setPageNumber(1);\n            updateUrlAndDispatchEvent(newParams);\n            setSelectedFilters((prev)=>{\n                const filtered = prev.filter((f)=>f.category !== \"keyWord\");\n                if (newValue.length > 0 && newValue[0].trim()) {\n                    return [\n                        ...filtered,\n                        {\n                            category: \"keyWord\",\n                            label: newValue[0]\n                        }\n                    ];\n                }\n                return filtered;\n            });\n            window.dispatchEvent(new CustomEvent(\"checkboxFilterChanged\", {\n                detail: {\n                    category: \"keyWord\",\n                    value: newValue[0] || \"\",\n                    newValues: newValue,\n                    allValues: values,\n                    maintainScroll: true,\n                    scrollPosition: window.scrollY || document.documentElement.scrollTop\n                }\n            }));\n        }\n    }, [\n        setFieldValue,\n        isMobile,\n        isTablet,\n        setPageNumber,\n        setSelectedFilters,\n        updateUrlAndDispatchEvent,\n        values\n    ]);\n    const handleCountryChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((_, newValue)=>{\n        setFieldValue(\"country\", newValue);\n        // For mobile and tablet, apply filter instantly\n        if (isMobile || isTablet) {\n            const newParams = new URLSearchParams(window.location.search);\n            if (newValue && newValue.trim()) {\n                newParams.set(\"country\", newValue);\n            } else {\n                newParams.delete(\"country\");\n            }\n            const currentParams = new URLSearchParams(window.location.search);\n            if (currentParams.has(\"list\") && !newParams.has(\"list\")) {\n                newParams.set(\"list\", currentParams.get(\"list\"));\n            }\n            newParams.set(\"pageNumber\", 1);\n            setPageNumber(1);\n            updateUrlAndDispatchEvent(newParams);\n            setSelectedFilters((prev)=>{\n                const filtered = prev.filter((f)=>f.category !== \"country\");\n                if (newValue && newValue.trim()) {\n                    return [\n                        ...filtered,\n                        {\n                            category: \"country\",\n                            label: newValue\n                        }\n                    ];\n                }\n                return filtered;\n            });\n            window.dispatchEvent(new CustomEvent(\"checkboxFilterChanged\", {\n                detail: {\n                    category: \"country\",\n                    value: newValue || \"\",\n                    newValues: newValue ? [\n                        newValue\n                    ] : [],\n                    allValues: values,\n                    maintainScroll: true,\n                    scrollPosition: window.scrollY || document.documentElement.scrollTop\n                }\n            }));\n        }\n    }, [\n        setFieldValue,\n        isMobile,\n        isTablet,\n        setPageNumber,\n        setSelectedFilters,\n        updateUrlAndDispatchEvent,\n        values\n    ]);\n    const handleClearFilters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((e)=>{\n        if (e && e.preventDefault) {\n            e.preventDefault();\n        }\n        const scrollPosition = window.scrollY || document.documentElement.scrollTop;\n        window._isClearing = true;\n        setFieldValue(\"jobDescriptionLanguages\", []);\n        setFieldValue(\"levelOfExperience\", []);\n        setFieldValue(\"industry\", []);\n        setFieldValue(\"contractType\", []);\n        const currentParams = new URLSearchParams(window.location.search);\n        currentParams.delete(\"jobDescriptionLanguages\");\n        currentParams.delete(\"levelOfExperience\");\n        currentParams.delete(\"industry\");\n        currentParams.delete(\"contractType\");\n        const newUrl = \"\".concat(pathname, \"?\").concat(currentParams.toString());\n        window.history.replaceState({\n            path: newUrl\n        }, \"\", newUrl);\n        const params = {};\n        for (const [key, value] of currentParams.entries()){\n            params[key] = value;\n        }\n        window.dispatchEvent(new CustomEvent(\"filterChanged\", {\n            detail: {\n                params: params,\n                maintainScroll: true,\n                scrollPosition: scrollPosition\n            }\n        }));\n        setSelectedFilters((prev)=>{\n            return prev.filter((filter)=>filter.category === \"keyWord\" || filter.category === \"country\");\n        });\n        window.scrollTo({\n            top: scrollPosition,\n            behavior: \"instant\"\n        });\n        window._isClearing = false;\n        return false;\n    }, [\n        setFieldValue,\n        setSelectedFilters,\n        pathname\n    ]);\n    return {\n        handleCheckboxChange,\n        handleSearchChange,\n        handleCountryChange,\n        handleClearFilters\n    };\n};\n_s(useFilterHandlers, \"/ao+VBt18iwPBB84raUBAgTL5WI=\", false, function() {\n    return [\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (useFilterHandlers);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/opportunity/hooks/useFilterHandlers.js\n"));

/***/ })

});