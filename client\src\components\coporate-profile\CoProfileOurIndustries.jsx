"use client";
import { useState } from "react";
import { Container, Grid } from "@mui/material";
import CustomButton from "@/components/ui/CustomButton";
import ArrowLeft from "@/assets/images/icons/arrow-left.svg";
import SvgArrowLeft from "../../assets/images/icons/ArrowBackIcon.svg";
import bgEnegies from "../../assets/images/website/coporate-profile/enegies.png";
import bgBanking from "../../assets/images/website/coporate-profile/banking.png";
import bgItTelecom from "../../assets/images/website/coporate-profile/it-telecom.png";
import bgPharma from "../../assets/images/website/coporate-profile/pharma.png";
import bgTransport from "../../assets/images/website/coporate-profile/transport.png";
import industriesBg from "../../assets/images/website/coporate-profile/industries-bg.png";
import {
  findIndustryLogoSvg
} from "@/utils/functions";
const industries = [
  {
    id: "Energies",
    title: "Energies",
    description:
      "At a major LPG industrial complex located near Hassi-Messaoud, Pentabell supported the construction of a 4th LPG train under a large-scale EPC contract. With a nominal gas treatment capacity of 8 million SM³/day, the project aimed to boost output significantly. Pentabell mobilized 35 qualified specialists for a period of 36 months, contributing to an increase in daily LPG production from 3600T to 4800T and condensate output from 330T to 480T. Our contract, valued at 1.4 million €, was instrumental in meeting Algeria’s growing downstream processing demand while maintaining speed, safety, and compliance.",
    bg: bgEnegies,
  },
  {
    id: "Transport",
    title: "Transportation",
    description:
      "For one of the region’s most ambitious metro operations and maintenance projects, our team has been a critical partner since January 2020, deploying +99 skilled engineers and technicians across four metro lines under a renewable 5-year contract. Over 2043 cumulative months (170 years) of on-ground support have helped sustain the network’s efficiency and safety. Through our Payroll & Manpower Supply services, we’ve secured absolute workforce management amid high regulatory complexity.",
    bg: bgTransport,
  },
  {
    id: "It & Telecom",
    title: "It & Telecom",
    description:
      "In a mission-critical network project, our Payroll Services ensured uninterrupted compliance and workforce support for specialized consultants, covering 12 months from February 2024. Even for small-scale teams, our approach guarantees agility, compliance, and cost-efficiency.",
    bg: bgItTelecom,
  },
  {
    id: "Banking & Insurance",
    title: "Banking & Insurance",
    description:
      "We supported a large-scale transformation for a growing digital financial hub in the Middle East  by supplying highly qualified call center agents, project managers, portfolio specialists, SMEs, and validation experts. Our Payroll & Manpower Supply solution covered 82 professionals. Delivering a total of 804 cumulative months (67 years) of operational continuity since April 2022. Pentabell agility and compliance expertise ensured uninterrupted support in a sector where speed and accuracy are everything.",
    bg: bgBanking,
  },
  // {
  //   id: "Pharmaceutical",
  //   title: "Pharma",
  //   description:
  //     "Lorem ipsum dolor sit amet consectetur. Integer adipiscing eu urna massa non viverra facilisi cras egestas. Ut pharetra faucibus duis ultricies vel. Ac sit rhoncus nulla sagittis metus leo ac. Sagittis nibh quam aliquam morbi est bibendum ac id. Amet pellentesque quis tincidunt lectus tristique ut massa. Tristique eu in eleifend purus dictumst amet. Mollis venenatis lacus dictum vehicula at blandit. Et pulvinar magna sed nec sed metus duis. A mi aenean suspendisse.",
  //   bg: bgPharma,
  // },
];
function CoProfileOurIndustries() {
  const [activeId, setActiveId] = useState(null);

  const handleToggle = (id) => {
    setActiveId((prevId) => (prevId === id ? null : id));
  };
  return (
    <div
      id="coporate-profile-industries"
      style={{
        backgroundImage: `url("${industriesBg.src}")`,
        backgroundSize: "contain",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "top",
      }}
    >
      <Container className="custom-max-width white-bg">
        <Grid container>
          <Grid item xs={12} sm={12}>
            <p className="heading-h2 text-white  text-banner text-center semi-bold">
              Industries Where We Make Impact
            </p>
          </Grid>
          {industries.map((item, index) => (
            <Grid
              key={item.id}
              id={item.id}
              item
              xs={12}
              sm={12}
              className="single-industry"
              style={{
                backgroundImage: `url("${item.bg.src}")`,
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
                backgroundPosition: "top",
              }}
            >
              <Grid container alignItems={"center"}>
                <Grid item xs={9} sm={10}>
                  <p className="heading-h3 text-white semi-bold">
                    <span className="icon">{findIndustryLogoSvg(item.id)}</span>
                    {/* {item.title} */}
                  </p>
                </Grid>
                <Grid item xs={3} sm={2} className="align-right">
                  <CustomButton
                    onClick={() => handleToggle(item.id)}
                    icon={<SvgArrowLeft />}
                    className={`display-decription ${
                      activeId == item.id ? "rotate" : ""
                    }`}
                  />
                </Grid>

                {activeId == item.id && (
                  <Grid item xs={12} sm={12} className="description">
                    <p className="sub-heading text-white">{item.description}</p>
                  </Grid>
                )}
              </Grid>
            </Grid>
          ))}
        </Grid>
      </Container>
    </div>
  );
}

export default CoProfileOurIndustries;
