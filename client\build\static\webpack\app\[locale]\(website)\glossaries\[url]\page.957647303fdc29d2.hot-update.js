"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var moment_locale_fr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! moment/locale/fr */ \"(app-pages-browser)/./node_modules/moment/locale/fr.js\");\n/* harmony import */ var moment_locale_fr__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(moment_locale_fr__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction GlossaryDetails(param) {\n    let { id, article, language, url } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    moment__WEBPACK_IMPORTED_MODULE_2___default().locale(i18n.language || \"en\");\n    const [modifiedHtmlContent, setModifiedHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(article === null || article === void 0 ? void 0 : article.content);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const parser = new DOMParser();\n        const doc = parser.parseFromString(article === null || article === void 0 ? void 0 : article.content, \"text/html\");\n        const extractedHeadings = [];\n        Array.from(doc.querySelectorAll(\"h2, h3\")).forEach((heading)=>{\n            const id = heading.innerText.toLowerCase().replace(/\\s+/g, \"-\").replace(/[^a-z0-9\\-]/g, \"\");\n            heading.id = id;\n            extractedHeadings.push({\n                tagName: heading.tagName.toLowerCase(),\n                content: heading.innerText,\n                id\n            });\n        });\n        setModifiedHtmlContent(doc.body.innerHTML);\n    }, [\n        article === null || article === void 0 ? void 0 : article.content\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page-details\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"glossary-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"custom-max-width\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"categories-path\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    locale: language === \"en\" ? \"en\" : \"fr\",\n                                    href: \"\".concat(language === \"en\" ? \"/blog\" : \"/\".concat(language, \"/blog\"), \"/\"),\n                                    className: \"link\",\n                                    children: \"Blog\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                (article === null || article === void 0 ? void 0 : article.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: article === null || article === void 0 ? void 0 : article.word\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    type: \"application/ld+json\",\n                    dangerouslySetInnerHTML: {\n                        __html: JSON.stringify({\n                            \"@context\": \"https://schema.org\",\n                            \"@type\": \"BreadcrumbList\",\n                            itemListElement: [\n                                {\n                                    \"@type\": \"ListItem\",\n                                    position: 1,\n                                    item: {\n                                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                                        name: \"Glossary\"\n                                    }\n                                }\n                            ]\n                        })\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"custom-max-width\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"container\",\n                        container: true,\n                        columnSpacing: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glossary-content\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    htmlContent: modifiedHtmlContent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(GlossaryDetails, \"uSZFHA8dnwIqCnuai4cJTqtx7/0=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    ];\n});\n_c = GlossaryDetails;\nvar _c;\n$RefreshReg$(_c, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});