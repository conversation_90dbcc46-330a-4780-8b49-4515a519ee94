"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const request_ip_1 = __importDefault(require("request-ip"));
const body_parser_1 = __importDefault(require("body-parser"));
const path_1 = __importDefault(require("path"));
const http_1 = __importDefault(require("http"));
const yamljs_1 = __importDefault(require("yamljs"));
require("reflect-metadata");
const fs = __importStar(require("fs"));
const express_session_1 = __importDefault(require("express-session"));
const passport_1 = __importDefault(require("passport"));
const logger_1 = require("./utils/logger/");
const databases_1 = require("./utils/databases");
const cron_job_1 = require("./utils/cron-job");
const error_middleware_1 = __importDefault(require("./middlewares/error.middleware"));
const authentication_middleware_1 = __importStar(require("./middlewares/authentication.middleware"));
const socket_1 = require("./utils/config/socket");
const authorization_middleware_1 = require("./middlewares/authorization.middleware");
const constants_1 = require("./utils/helpers/constants");
const logging_middleware_1 = require("./middlewares/logging.middleware");
const pm2_middleware_1 = require("./middlewares/pm2.middleware");
const cache_middleware_1 = require("./middlewares/cache.middleware");
const scheduler_1 = require("./utils/scheduler");
const opportunity_service_1 = __importDefault(require("./apis/opportunity/service/opportunity.service"));
const uploadsFolderPath = path_1.default.join(__dirname, 'uploads');
const publicFolderPath = path_1.default.join(__dirname, 'public');
const buildFrontendFolderPath = path_1.default.join(__dirname, '../client/.next');
class App {
    constructor(controllers, port) {
        this.express = (0, express_1.default)();
        this.port = port;
        this.server = http_1.default.createServer(this.express);
        this.initialiseMiddleware();
        this.initialiseDatabaseConnection();
        this.initialiseControllers(controllers);
        (0, cron_job_1.scheduleCronJob)();
        if (process.env.NODE_ENV !== 'dev') {
            this.express.get('*', (request, response) => {
                response.sendFile(buildFrontendFolderPath);
            });
        }
        this.initialiseErrorHandling();
        (0, socket_1.initialiseSocket)(this.server);
        if (process.env.NODE_ENV === 'prod')
            (0, scheduler_1.scheduleImportOpportunityFromHunter)(new opportunity_service_1.default());
    }
    initialiseMiddleware() {
        this.express
            .use(express_1.default.static(uploadsFolderPath))
            .use(express_1.default.static(publicFolderPath))
            .use(express_1.default.static(buildFrontendFolderPath))
            .use('/assets', express_1.default.static(path_1.default.join(__dirname, 'src/assets')))
            .use(body_parser_1.default.json())
            .use((0, cors_1.default)({
            origin: process.env.ALLOWED_IPS?.split(','),
            credentials: true,
        }))
            .use((0, cookie_parser_1.default)())
            .use(request_ip_1.default.mw())
            .use(express_1.default.json({ limit: '50mb' }))
            .use(express_1.default.urlencoded({ limit: '50mb', extended: true }))
            .use(authentication_middleware_1.persistConnectedUser)
            .use(logging_middleware_1.requestLogger)
            .use((0, express_session_1.default)({
            secret: process.env.SESSION_SECRET,
            resave: false,
            saveUninitialized: true,
            cookie: { secure: process.env.NODE_ENV === 'prod', sameSite: 'none' },
        }))
            .use(passport_1.default.initialize())
            .use(passport_1.default.session())
            .use(pm2_middleware_1.requestCountMiddleware)
            .use(pm2_middleware_1.requestFrequencyMiddleware)
            .use(pm2_middleware_1.responseStatusMiddleware)
            .use(pm2_middleware_1.requestUrlMiddleware)
            .use(pm2_middleware_1.requestMethodMiddleware)
            .use(pm2_middleware_1.latencyMiddleware)
            .use(pm2_middleware_1.trackRequestCountsMiddleware);
    }
    initialiseControllers(controllers) {
        controllers.forEach((controller) => {
            this.express.use('/api/v1', controller.router);
        });
        this.express.get('/api/v1/logs', authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), async (request, response) => {
            try {
                const { date, fullName } = request.query;
                let LOG_FILE = `${process.env.NODE_ENV}-requests-${new Date().toISOString().substring(0, 10)}.log`;
                if (date)
                    LOG_FILE = `${process.env.NODE_ENV}-requests-${date}.log`;
                const LOG_FILE_PATH = path_1.default.join(__dirname, '../logs/', LOG_FILE);
                const logs = await fs.readFileSync(LOG_FILE_PATH, 'utf-8').split('\n');
                logs.pop();
                if (fullName) {
                    const regex = new RegExp(`.*${fullName}.*`, 'i');
                    const parsedLogs = logs.filter((log) => regex.test(log));
                    return response.json({ logs: parsedLogs });
                }
                return response.json({ logs });
            }
            catch (error) {
                response.status(500).json(error);
            }
        });
        this.express.delete('/api/v1/cache', authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), cache_middleware_1.invalidateCache);
    }
    initialiseErrorHandling() {
        this.express.use(error_middleware_1.default);
    }
    async initialiseDatabaseConnection() {
        await (0, databases_1.mongoConnect)();
    }
    listen() {
        const swaggerDocument = yamljs_1.default.load('./swagger.yaml');
        this.express.use('/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swaggerDocument));
        this.server.listen(this.port, () => {
            logger_1.logger.info(`App listening on the port ${this.port}`);
        });
    }
}
exports.default = App;
//# sourceMappingURL=app.js.map