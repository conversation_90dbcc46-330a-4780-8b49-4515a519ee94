{"c": ["app/[locale]/(website)/glossaries/[url]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Cfeatures%5C%5Cblog%5C%5Ccomponents%5C%5Cnew-blog%5C%5CBlogPageDetails.jsx%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Cfeatures%5C%5Cglossary%5C%5Ccomponent%5C%5CGlossaryDetails.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!", "(app-pages-browser)/./src/assets/images/icons/mailWithBg.svg", "(app-pages-browser)/./src/assets/images/icons/time.svg", "(app-pages-browser)/./src/components/sections/NewsletterSubscription.jsx", "(app-pages-browser)/./src/features/blog/components/CommentsListByBlog.jsx", "(app-pages-browser)/./src/features/blog/components/CreateBlogComment.jsx", "(app-pages-browser)/./src/features/blog/components/new-blog/BlogPageDetails.jsx", "(app-pages-browser)/./src/features/blog/components/new-blog/ContentTable.jsx", "(app-pages-browser)/./src/features/blog/components/new-blog/RelatedBlog.jsx", "(app-pages-browser)/./src/features/blog/components/new-blog/ShareOnSocialMedia.jsx"]}