"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/page",{

/***/ "(app-pages-browser)/./src/components/sections/LatestJobOffers.jsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/LatestJobOffers.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Container!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var embla_carousel_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! embla-carousel-react */ \"(app-pages-browser)/./node_modules/embla-carousel-react/esm/embla-carousel-react.esm.js\");\n/* harmony import */ var _ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* harmony import */ var i18n_iso_countries__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! i18n-iso-countries */ \"(app-pages-browser)/./node_modules/i18n-iso-countries/index.js\");\n/* harmony import */ var i18n_iso_countries_langs_en_json__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! i18n-iso-countries/langs/en.json */ \"(app-pages-browser)/./node_modules/i18n-iso-countries/langs/en.json\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LatestJobOffers(param) {\n    let { language } = param;\n    _s();\n    const OPTIONS = {\n        loop: false,\n        align: \"start\"\n    };\n    const [emblaRef] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(OPTIONS);\n    const [jobOffers, setJobOffers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        urgent: undefined\n    });\n    i18n_iso_countries__WEBPACK_IMPORTED_MODULE_7__.registerLocale(i18n_iso_countries_langs_en_json__WEBPACK_IMPORTED_MODULE_8__);\n    const fetchJobOffers = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_4__.axiosGetJsonSSR.get(\"\".concat(_utils_urls__WEBPACK_IMPORTED_MODULE_5__.baseURL).concat(_utils_urls__WEBPACK_IMPORTED_MODULE_5__.API_URLS.opportunity, \"/urgent\"), {\n                params: {\n                    urgent: query.urgent\n                }\n            });\n            if (response.data && response.data.length > 0) {\n                const fetchedOffers = response.data.map((offer)=>{\n                    var _offer_versions_language, _offer_versions_language1;\n                    return {\n                        id: offer._id,\n                        title: ((_offer_versions_language = offer.versions[language]) === null || _offer_versions_language === void 0 ? void 0 : _offer_versions_language.title) || \"Titre non disponible\",\n                        industry: offer.industry,\n                        country: offer.country,\n                        dateOfExpiration: offer.dateOfExpiration,\n                        minExperience: offer.minExperience,\n                        maxExperience: offer.maxExperience,\n                        existingLanguages: offer.existingLanguages,\n                        reference: offer.reference,\n                        urgent: offer.urgent,\n                        url: (_offer_versions_language1 = offer.versions[language]) === null || _offer_versions_language1 === void 0 ? void 0 : _offer_versions_language1.url\n                    };\n                });\n                setJobOffers(fetchedOffers);\n            } else {\n                setJobOffers([]);\n                setError(\"No job offers available at the moment.\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching job offers:\", err);\n            setError(\"Failed to fetch job offers\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchJobOffers();\n    }, [\n        query\n    ]);\n    const onClickFilter = (data)=>{\n        if (data.urgent !== undefined) {\n            setQuery((prev)=>({\n                    ...prev,\n                    urgent: data.urgent\n                }));\n        } else {\n            setQuery((prev)=>({\n                    ...prev,\n                    urgent: undefined\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        id: \"latest-offers\",\n        className: \"custom-max-width\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"heading-h1 text-center\",\n                children: t(\"homePage:s3:title\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"filter-btns\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        onClick: ()=>onClickFilter({\n                                urgent: !query.urgent\n                            }),\n                        text: t(\"homePage:s3:btu\"),\n                        className: \"\".concat(query.urgent ? \"btn btn-filter selected\" : \"btn btn-outlined\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        text: t(\"homePage:s3:btlast\"),\n                        onClick: ()=>onClickFilter({\n                                urgent: undefined\n                            }),\n                        className: \"\".concat(query.urgent === undefined ? \"btn btn-filter selected\" : \"btn btn-outlined\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"embla\",\n                id: \"jobs__slider\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"embla__viewport\",\n                    ref: emblaRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"embla__container\",\n                        children: jobOffers.map((offer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OpportunityItemByGrid, {\n                                opportunity: offer,\n                                language: language\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"center-div\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    text: t(\"homePage:s3:all\"),\n                    link: \"/\".concat(_helpers_routesList__WEBPACK_IMPORTED_MODULE_6__.websiteRoutesList.opportunities.route),\n                    // onClick={() => onClickFilter({ urgent: undefined })}\n                    className: \"btn btn-filled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(LatestJobOffers, \"3pdFVtrTsKhzPDGwe+Ms3jSedJI=\", false, function() {\n    return [\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = LatestJobOffers;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LatestJobOffers);\nvar _c;\n$RefreshReg$(_c, \"LatestJobOffers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL0xhdGVzdEpvYk9mZmVycy5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ0Y7QUFDSztBQUNLO0FBRU47QUFDRztBQUNBO0FBQ1E7QUFDZDtBQUNhO0FBRXhELFNBQVNZLGdCQUFnQixLQUFZO1FBQVosRUFBRUMsUUFBUSxFQUFFLEdBQVo7O0lBQ3ZCLE1BQU1DLFVBQVU7UUFBRUMsTUFBTTtRQUFPQyxPQUFPO0lBQVE7SUFDOUMsTUFBTSxDQUFDQyxTQUFTLEdBQUdiLGdFQUFnQkEsQ0FBQ1U7SUFDcEMsTUFBTSxDQUFDSSxXQUFXQyxhQUFhLEdBQUduQiwrQ0FBUUEsQ0FBQyxFQUFFO0lBQzdDLE1BQU0sQ0FBQ29CLFNBQVNDLFdBQVcsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3NCLE9BQU9DLFNBQVMsR0FBR3ZCLCtDQUFRQSxDQUFDO0lBRW5DLE1BQU0sRUFBRXdCLENBQUMsRUFBRSxHQUFHckIsNkRBQWNBO0lBQzVCLE1BQU0sQ0FBQ3NCLE9BQU9DLFNBQVMsR0FBRzFCLCtDQUFRQSxDQUFDO1FBQ2pDMkIsUUFBUUM7SUFDVjtJQUVBbEIsOERBQXdCLENBQUNDLDZEQUFRQTtJQUVqQyxNQUFNbUIsaUJBQWlCO1FBQ3JCVCxXQUFXO1FBQ1hFLFNBQVM7UUFFVCxJQUFJO1lBQ0YsTUFBTVEsV0FBVyxNQUFNekIsMERBQWVBLENBQUMwQixHQUFHLENBQ3hDLEdBQWF6QixPQUFWQyxnREFBT0EsRUFBd0IsT0FBckJELGlEQUFRQSxDQUFDMEIsV0FBVyxFQUFDLFlBQ2xDO2dCQUNFQyxRQUFRO29CQUNOUCxRQUFRRixNQUFNRSxNQUFNO2dCQUN0QjtZQUNGO1lBR0YsSUFBSUksU0FBU0ksSUFBSSxJQUFJSixTQUFTSSxJQUFJLENBQUNDLE1BQU0sR0FBRyxHQUFHO2dCQUM3QyxNQUFNQyxnQkFBZ0JOLFNBQVNJLElBQUksQ0FBQ0csR0FBRyxDQUFDLENBQUNDO3dCQUVoQ0EsMEJBU0ZBOzJCQVg2Qzt3QkFDbERDLElBQUlELE1BQU1FLEdBQUc7d0JBQ2JDLE9BQU9ILEVBQUFBLDJCQUFBQSxNQUFNSSxRQUFRLENBQUM5QixTQUFTLGNBQXhCMEIsK0NBQUFBLHlCQUEwQkcsS0FBSyxLQUFJO3dCQUMxQ0UsVUFBVUwsTUFBTUssUUFBUTt3QkFDeEJDLFNBQVNOLE1BQU1NLE9BQU87d0JBQ3RCQyxrQkFBa0JQLE1BQU1PLGdCQUFnQjt3QkFDeENDLGVBQWVSLE1BQU1RLGFBQWE7d0JBQ2xDQyxlQUFlVCxNQUFNUyxhQUFhO3dCQUNsQ0MsbUJBQW1CVixNQUFNVSxpQkFBaUI7d0JBQzFDQyxXQUFXWCxNQUFNVyxTQUFTO3dCQUMxQnZCLFFBQVFZLE1BQU1aLE1BQU07d0JBQ3BCd0IsR0FBRyxHQUFFWiw0QkFBQUEsTUFBTUksUUFBUSxDQUFDOUIsU0FBUyxjQUF4QjBCLGdEQUFBQSwwQkFBMEJZLEdBQUc7b0JBQ3BDOztnQkFDQWhDLGFBQWFrQjtZQUNmLE9BQU87Z0JBQ0xsQixhQUFhLEVBQUU7Z0JBQ2ZJLFNBQVM7WUFDWDtRQUNGLEVBQUUsT0FBTzZCLEtBQUs7WUFDWkMsUUFBUS9CLEtBQUssQ0FBQyw4QkFBOEI4QjtZQUM1QzdCLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBRUFwQixnREFBU0EsQ0FBQztRQUNSNkI7SUFDRixHQUFHO1FBQUNMO0tBQU07SUFFVixNQUFNNkIsZ0JBQWdCLENBQUNuQjtRQUNyQixJQUFJQSxLQUFLUixNQUFNLEtBQUtDLFdBQVc7WUFDN0JGLFNBQVMsQ0FBQzZCLE9BQVU7b0JBQ2xCLEdBQUdBLElBQUk7b0JBQ1A1QixRQUFRUSxLQUFLUixNQUFNO2dCQUNyQjtRQUNGLE9BQU87WUFDTEQsU0FBUyxDQUFDNkIsT0FBVTtvQkFDbEIsR0FBR0EsSUFBSTtvQkFDUDVCLFFBQVFDO2dCQUNWO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDMUIsc0ZBQVNBO1FBQUNzQyxJQUFHO1FBQWdCZ0IsV0FBVTs7MEJBQ3RDLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBMEJoQyxFQUFFOzs7Ozs7MEJBQzFDLDhEQUFDa0M7Z0JBQUlsQixJQUFHOztrQ0FDTiw4REFBQ25DLHdEQUFZQTt3QkFDWHNELFNBQVMsSUFBTUwsY0FBYztnQ0FBRTNCLFFBQVEsQ0FBQ0YsTUFBTUUsTUFBTTs0QkFBQzt3QkFDckRpQyxNQUFNcEMsRUFBRTt3QkFDUmdDLFdBQVcsR0FFVixPQURDL0IsTUFBTUUsTUFBTSxHQUFHLDRCQUE0Qjs7Ozs7O2tDQUcvQyw4REFBQ3RCLHdEQUFZQTt3QkFDWHVELE1BQU1wQyxFQUFFO3dCQUNSbUMsU0FBUyxJQUFNTCxjQUFjO2dDQUFFM0IsUUFBUUM7NEJBQVU7d0JBQ2pENEIsV0FBVyxHQUlWLE9BSEMvQixNQUFNRSxNQUFNLEtBQUtDLFlBQ2IsNEJBQ0E7Ozs7Ozs7Ozs7OzswQkFLViw4REFBQ2lDO2dCQUFRTCxXQUFVO2dCQUFRaEIsSUFBSTswQkFDN0IsNEVBQUNrQjtvQkFBSUYsV0FBVTtvQkFBa0JNLEtBQUs3Qzs4QkFDcEMsNEVBQUN5Qzt3QkFBSUYsV0FBVTtrQ0FDWnRDLFVBQVVvQixHQUFHLENBQUMsQ0FBQ0MsT0FBT3dCLHNCQUNyQiw4REFBQ0M7Z0NBRUMvQixhQUFhTTtnQ0FDYjFCLFVBQVVBOytCQUZMa0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNmLDhEQUFDTDtnQkFBSUYsV0FBVTswQkFDYiw0RUFBQ25ELHdEQUFZQTtvQkFDWHVELE1BQU1wQyxFQUFFO29CQUNSeUMsTUFBTSxJQUEwQyxPQUF0Q3hELGtFQUFpQkEsQ0FBQ3lELGFBQWEsQ0FBQ0MsS0FBSztvQkFDL0MsdURBQXVEO29CQUN2RFgsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLckI7R0F2SFM1Qzs7UUFFWVIsNERBQWdCQTtRQUtyQkQseURBQWNBOzs7S0FQckJTO0FBeUhULCtEQUFlQSxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL0xhdGVzdEpvYk9mZmVycy5qc3g/OTJhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBDb250YWluZXIgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gXCJyZWFjdC1pMThuZXh0XCI7XHJcbmltcG9ydCB1c2VFbWJsYUNhcm91c2VsIGZyb20gXCJlbWJsYS1jYXJvdXNlbC1yZWFjdFwiO1xyXG5cclxuaW1wb3J0IEN1c3RvbUJ1dHRvbiBmcm9tIFwiLi4vdWkvQ3VzdG9tQnV0dG9uXCI7XHJcbmltcG9ydCB7IGF4aW9zR2V0SnNvblNTUiB9IGZyb20gXCJAL2NvbmZpZy9heGlvc1wiO1xyXG5pbXBvcnQgeyBBUElfVVJMUywgYmFzZVVSTCB9IGZyb20gXCJAL3V0aWxzL3VybHNcIjtcclxuaW1wb3J0IHsgd2Vic2l0ZVJvdXRlc0xpc3QgfSBmcm9tIFwiQC9oZWxwZXJzL3JvdXRlc0xpc3RcIjtcclxuaW1wb3J0IGNvdW50cmllcyBmcm9tIFwiaTE4bi1pc28tY291bnRyaWVzXCI7XHJcbmltcG9ydCBlbkxvY2FsZSBmcm9tIFwiaTE4bi1pc28tY291bnRyaWVzL2xhbmdzL2VuLmpzb25cIjtcclxuXHJcbmZ1bmN0aW9uIExhdGVzdEpvYk9mZmVycyh7IGxhbmd1YWdlIH0pIHtcclxuICBjb25zdCBPUFRJT05TID0geyBsb29wOiBmYWxzZSwgYWxpZ246IFwic3RhcnRcIiB9O1xyXG4gIGNvbnN0IFtlbWJsYVJlZl0gPSB1c2VFbWJsYUNhcm91c2VsKE9QVElPTlMpO1xyXG4gIGNvbnN0IFtqb2JPZmZlcnMsIHNldEpvYk9mZmVyc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZShudWxsKTtcclxuXHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IFtxdWVyeSwgc2V0UXVlcnldID0gdXNlU3RhdGUoe1xyXG4gICAgdXJnZW50OiB1bmRlZmluZWQsXHJcbiAgfSk7XHJcblxyXG4gIGNvdW50cmllcy5yZWdpc3RlckxvY2FsZShlbkxvY2FsZSk7XHJcblxyXG4gIGNvbnN0IGZldGNoSm9iT2ZmZXJzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcclxuICAgIHNldEVycm9yKG51bGwpO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3NHZXRKc29uU1NSLmdldChcclxuICAgICAgICBgJHtiYXNlVVJMfSR7QVBJX1VSTFMub3Bwb3J0dW5pdHl9L3VyZ2VudGAsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgcGFyYW1zOiB7XHJcbiAgICAgICAgICAgIHVyZ2VudDogcXVlcnkudXJnZW50LFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICB9XHJcbiAgICAgICk7XHJcblxyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmxlbmd0aCA+IDApIHtcclxuICAgICAgICBjb25zdCBmZXRjaGVkT2ZmZXJzID0gcmVzcG9uc2UuZGF0YS5tYXAoKG9mZmVyKSA9PiAoe1xyXG4gICAgICAgICAgaWQ6IG9mZmVyLl9pZCxcclxuICAgICAgICAgIHRpdGxlOiBvZmZlci52ZXJzaW9uc1tsYW5ndWFnZV0/LnRpdGxlIHx8IFwiVGl0cmUgbm9uIGRpc3BvbmlibGVcIixcclxuICAgICAgICAgIGluZHVzdHJ5OiBvZmZlci5pbmR1c3RyeSxcclxuICAgICAgICAgIGNvdW50cnk6IG9mZmVyLmNvdW50cnksXHJcbiAgICAgICAgICBkYXRlT2ZFeHBpcmF0aW9uOiBvZmZlci5kYXRlT2ZFeHBpcmF0aW9uLFxyXG4gICAgICAgICAgbWluRXhwZXJpZW5jZTogb2ZmZXIubWluRXhwZXJpZW5jZSxcclxuICAgICAgICAgIG1heEV4cGVyaWVuY2U6IG9mZmVyLm1heEV4cGVyaWVuY2UsXHJcbiAgICAgICAgICBleGlzdGluZ0xhbmd1YWdlczogb2ZmZXIuZXhpc3RpbmdMYW5ndWFnZXMsXHJcbiAgICAgICAgICByZWZlcmVuY2U6IG9mZmVyLnJlZmVyZW5jZSxcclxuICAgICAgICAgIHVyZ2VudDogb2ZmZXIudXJnZW50LFxyXG4gICAgICAgICAgdXJsOiBvZmZlci52ZXJzaW9uc1tsYW5ndWFnZV0/LnVybCxcclxuICAgICAgICB9KSk7XHJcbiAgICAgICAgc2V0Sm9iT2ZmZXJzKGZldGNoZWRPZmZlcnMpO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldEpvYk9mZmVycyhbXSk7XHJcbiAgICAgICAgc2V0RXJyb3IoXCJObyBqb2Igb2ZmZXJzIGF2YWlsYWJsZSBhdCB0aGUgbW9tZW50LlwiKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBqb2Igb2ZmZXJzOlwiLCBlcnIpO1xyXG4gICAgICBzZXRFcnJvcihcIkZhaWxlZCB0byBmZXRjaCBqb2Igb2ZmZXJzXCIpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGZldGNoSm9iT2ZmZXJzKCk7XHJcbiAgfSwgW3F1ZXJ5XSk7XHJcblxyXG4gIGNvbnN0IG9uQ2xpY2tGaWx0ZXIgPSAoZGF0YSkgPT4ge1xyXG4gICAgaWYgKGRhdGEudXJnZW50ICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgc2V0UXVlcnkoKHByZXYpID0+ICh7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICB1cmdlbnQ6IGRhdGEudXJnZW50LFxyXG4gICAgICB9KSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRRdWVyeSgocHJldikgPT4gKHtcclxuICAgICAgICAuLi5wcmV2LFxyXG4gICAgICAgIHVyZ2VudDogdW5kZWZpbmVkLFxyXG4gICAgICB9KSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxDb250YWluZXIgaWQ9XCJsYXRlc3Qtb2ZmZXJzXCIgY2xhc3NOYW1lPVwiY3VzdG9tLW1heC13aWR0aFwiPlxyXG4gICAgICA8aDIgY2xhc3NOYW1lPVwiaGVhZGluZy1oMSB0ZXh0LWNlbnRlclwiPnt0KFwiaG9tZVBhZ2U6czM6dGl0bGVcIil9PC9oMj5cclxuICAgICAgPGRpdiBpZD1cImZpbHRlci1idG5zXCI+XHJcbiAgICAgICAgPEN1c3RvbUJ1dHRvblxyXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gb25DbGlja0ZpbHRlcih7IHVyZ2VudDogIXF1ZXJ5LnVyZ2VudCB9KX1cclxuICAgICAgICAgIHRleHQ9e3QoXCJob21lUGFnZTpzMzpidHVcIil9XHJcbiAgICAgICAgICBjbGFzc05hbWU9e2Ake1xyXG4gICAgICAgICAgICBxdWVyeS51cmdlbnQgPyBcImJ0biBidG4tZmlsdGVyIHNlbGVjdGVkXCIgOiBcImJ0biBidG4tb3V0bGluZWRcIlxyXG4gICAgICAgICAgfWB9XHJcbiAgICAgICAgLz5cclxuICAgICAgICA8Q3VzdG9tQnV0dG9uXHJcbiAgICAgICAgICB0ZXh0PXt0KFwiaG9tZVBhZ2U6czM6YnRsYXN0XCIpfVxyXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gb25DbGlja0ZpbHRlcih7IHVyZ2VudDogdW5kZWZpbmVkIH0pfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPXtgJHtcclxuICAgICAgICAgICAgcXVlcnkudXJnZW50ID09PSB1bmRlZmluZWRcclxuICAgICAgICAgICAgICA/IFwiYnRuIGJ0bi1maWx0ZXIgc2VsZWN0ZWRcIlxyXG4gICAgICAgICAgICAgIDogXCJidG4gYnRuLW91dGxpbmVkXCJcclxuICAgICAgICAgIH1gfVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwiZW1ibGFcIiBpZD17XCJqb2JzX19zbGlkZXJcIn0+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJlbWJsYV9fdmlld3BvcnRcIiByZWY9e2VtYmxhUmVmfT5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1ibGFfX2NvbnRhaW5lclwiPlxyXG4gICAgICAgICAgICB7am9iT2ZmZXJzLm1hcCgob2ZmZXIsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgPE9wcG9ydHVuaXR5SXRlbUJ5R3JpZFxyXG4gICAgICAgICAgICAgICAga2V5PXtpbmRleH1cclxuICAgICAgICAgICAgICAgIG9wcG9ydHVuaXR5PXtvZmZlcn1cclxuICAgICAgICAgICAgICAgIGxhbmd1YWdlPXtsYW5ndWFnZX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICApKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L3NlY3Rpb24+XHJcblxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNlbnRlci1kaXZcIj5cclxuICAgICAgICA8Q3VzdG9tQnV0dG9uXHJcbiAgICAgICAgICB0ZXh0PXt0KFwiaG9tZVBhZ2U6czM6YWxsXCIpfVxyXG4gICAgICAgICAgbGluaz17YC8ke3dlYnNpdGVSb3V0ZXNMaXN0Lm9wcG9ydHVuaXRpZXMucm91dGV9YH1cclxuICAgICAgICAgIC8vIG9uQ2xpY2s9eygpID0+IG9uQ2xpY2tGaWx0ZXIoeyB1cmdlbnQ6IHVuZGVmaW5lZCB9KX1cclxuICAgICAgICAgIGNsYXNzTmFtZT17XCJidG4gYnRuLWZpbGxlZFwifVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9Db250YWluZXI+XHJcbiAgKTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgTGF0ZXN0Sm9iT2ZmZXJzO1xyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDb250YWluZXIiLCJ1c2VUcmFuc2xhdGlvbiIsInVzZUVtYmxhQ2Fyb3VzZWwiLCJDdXN0b21CdXR0b24iLCJheGlvc0dldEpzb25TU1IiLCJBUElfVVJMUyIsImJhc2VVUkwiLCJ3ZWJzaXRlUm91dGVzTGlzdCIsImNvdW50cmllcyIsImVuTG9jYWxlIiwiTGF0ZXN0Sm9iT2ZmZXJzIiwibGFuZ3VhZ2UiLCJPUFRJT05TIiwibG9vcCIsImFsaWduIiwiZW1ibGFSZWYiLCJqb2JPZmZlcnMiLCJzZXRKb2JPZmZlcnMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJ0IiwicXVlcnkiLCJzZXRRdWVyeSIsInVyZ2VudCIsInVuZGVmaW5lZCIsInJlZ2lzdGVyTG9jYWxlIiwiZmV0Y2hKb2JPZmZlcnMiLCJyZXNwb25zZSIsImdldCIsIm9wcG9ydHVuaXR5IiwicGFyYW1zIiwiZGF0YSIsImxlbmd0aCIsImZldGNoZWRPZmZlcnMiLCJtYXAiLCJvZmZlciIsImlkIiwiX2lkIiwidGl0bGUiLCJ2ZXJzaW9ucyIsImluZHVzdHJ5IiwiY291bnRyeSIsImRhdGVPZkV4cGlyYXRpb24iLCJtaW5FeHBlcmllbmNlIiwibWF4RXhwZXJpZW5jZSIsImV4aXN0aW5nTGFuZ3VhZ2VzIiwicmVmZXJlbmNlIiwidXJsIiwiZXJyIiwiY29uc29sZSIsIm9uQ2xpY2tGaWx0ZXIiLCJwcmV2IiwiY2xhc3NOYW1lIiwiaDIiLCJkaXYiLCJvbkNsaWNrIiwidGV4dCIsInNlY3Rpb24iLCJyZWYiLCJpbmRleCIsIk9wcG9ydHVuaXR5SXRlbUJ5R3JpZCIsImxpbmsiLCJvcHBvcnR1bml0aWVzIiwicm91dGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/LatestJobOffers.jsx\n"));

/***/ })

});