"use client";
import { Container, Grid, useMediaQuery, useTheme } from "@mui/material";
import moment from "moment";
import "moment/locale/fr";
import { toast } from "react-toastify";
import { useTranslation } from "react-i18next";

import DialogModal from "@/features/user/component/updateProfile/experience/DialogModal";
import SvgTime from "@/assets/images/icons/time.svg";
import SvgCalendar from "@/assets/images/icons/calendar.svg";
import SvgDeadline from "@/assets/images/icons/deadline.svg";
import SvgFileText from "@/assets/images/icons/FileText.svg";
import SvgMapPin from "@/assets/images/icons/MapPin.svg";
import OpportunityItem from "./OpportunityComponents/OpportunityItem";
import CustomButton from "@/components/ui/CustomButton";
import SvgYoutube from "@/assets/images/icons/Youtube.svg";
import SvgInstagram from "@/assets/images/icons/instagram.svg";
import SvgFacebook from "@/assets/images/icons/facebook.svg";
import SvgLinkedin from "@/assets/images/icons/linkedin.svg";
import SvgX from "@/assets/images/icons/x.svg";
import { useAddToFavourite } from "../../hooks/opportunity.hooks";
import NewsletterSubscription from "@/components/sections/NewsletterSubscription";
import useCurrentUser from "../../../auth/hooks/currentUser.hooks";
import { Role } from "@/utils/constants";
import { capitalizeFirstLetter } from "@/utils/functions";
import { findIndustryLogoSvg } from "@/utils/functions";
import { useEffect, useState } from "react";
import { axiosGetJson } from "@/config/axios";

function OpportunityPage({ data, language, relatedOpportunities, industry }) {
  const theme = useTheme();
  const currentOpportunityId = data?._id;
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const useAddTofavouriteHook = useAddToFavourite();

  const { t } = useTranslation();
  const [isSaved, setIsSaved] = useState(false);
  const { user } = useCurrentUser();
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [jobsTodelete, setJobsTodelete] = useState(false);
  const { i18n } = useTranslation();
  moment.locale(i18n.language || "en");
  const handeleDeleteconfirmation = () => {
    setJobsTodelete(data?._id);
    setShowDeleteConfirmation(true);
  };
  const handlecanceldelete = () => {
    setShowDeleteConfirmation(false);
  };

  const isExpired =
    data?.dateOfExpiration && moment(data.dateOfExpiration).isBefore(moment());

  const canApply = !user || user?.roles?.includes(Role.CANDIDATE);
  const deleteOpportunityFromShortlist = async (opportunityId) => {
    try {
      await axiosGetJson.delete(`/favourite/${opportunityId}`, {
        data: { type: "opportunity" },
      });
      setIsSaved(false);
    } catch (error) {}
  };
  const handleToggleOpportunity = async () => {
    try {
      await deleteOpportunityFromShortlist(jobsTodelete);
    } catch (error) {}
    setShowDeleteConfirmation(false);
  };
  const handleSaveClick = () => {
    if (!user) {
      toast.warning("You should be logged in first");
    } else {
      useAddTofavouriteHook.mutate(
        {
          id: data?._id,
          title: data?.versions[language]?.title,
          typeOfFavourite: "opportunity",
        },
        {
          onSuccess: () => {
            setIsSaved(true);
          },
        }
      );
    }
  };
  useEffect(() => {
    const checkIfSaved = async () => {
      if (data?._id) {
        try {
          const response = await axiosGetJson.get(
            `/favourite/is-saved/${data?._id}`
          );
          setIsSaved(response.data);
        } catch (error) {
          if (process.env.NODE_ENV === "dev")
            console.error("Failed to check if opportunity is saved:", error);
        }
      }
    };

    checkIfSaved();
  }, [data?._id]);

  return (
    <Container className="custom-max-width">
      <Grid className="container" container columnSpacing={2}>
        <Grid item xs={12} sm={8}>
          {isMobile ? (
            <div className="opportunity-info">
              <p className="title">{data?.versions[language]?.title}</p>
              <p className="job-ref">Ref: {data?.reference}</p>
              <div className="flex children-componenent">
                {data?.contractType ? (
                  <div className="job-info">
                    <SvgFileText />
                    <p>
                      {t("listopportunity:contract")}
                      <br />
                      <span>{data?.contractType}</span>
                    </p>
                  </div>
                ) : null}

                <div className="job-info">
                  <SvgMapPin />
                  <p>
                    Location
                    <br />
                    <span>{data?.country}</span>
                  </p>
                </div>

                <div className="job-info">
                  <SvgCalendar />
                  <p>
                    {t("listopportunity:expirationDate")}
                    <br />
                    <span>
                      {data?.dateOfExpiration
                        ? moment(data?.dateOfExpiration).format("DD/MM/YYYY")
                        : moment().add(3, "months").format("DD/MM/YYYY")}
                    </span>
                  </p>
                </div>
                <div className="job-btns">
                  {(!user || user?.roles?.includes(Role.CANDIDATE)) && (
                    <CustomButton
                      text={isSaved ? "Saved" : "Save"}
                      onClick={
                        isSaved ? handeleDeleteconfirmation : handleSaveClick
                      }
                      className={"btn btn-outlined white"}
                    />
                  )}
                  <CustomButton
                    text={"Share"}
                    // onClick={()}
                    className={"btn btn-outlined white"}
                  />
                </div>
                <div className="job-btns">
                  <CustomButton
                    text={isExpired ? "Expired" : "Apply Now"}
                    link={
                      !isExpired && canApply
                        ? `/apply/${data?.versions[language]?.url}`
                        : null
                    }
                    className={`btn btn-filled ${
                      isExpired || !canApply ? "disabled" : ""
                    }`}
                    disabled={isExpired || !canApply}
                  />
                </div>
              </div>
            </div>
          ) : null}
          <div className="opportunity-content">
            <div
              dangerouslySetInnerHTML={{
                __html: data?.versions[language]?.jobDescription,
              }}
            />
          </div>
          <div className="apply-section">
            <div>
              <p className="heading-h2 text-white text-center">
                Does this offer catch your interest?
              </p>
              <div className="btns">
                <CustomButton
                  text={isExpired ? "Expired" : "Apply Now"}
                  link={
                    !isExpired && canApply
                      ? `/apply/${data?.versions[language]?.url}`
                      : null
                  }
                  className={`btn btn-filled ${
                    isExpired || !canApply ? "disabled" : ""
                  }`}
                  disabled={isExpired || !canApply}
                />
                {(!user || user?.roles?.includes(Role.CANDIDATE)) && (
                  <CustomButton
                    text={isSaved ? "Saved" : "Save"}
                    onClick={
                      isSaved ? handeleDeleteconfirmation : handleSaveClick
                    }
                    className={`btn btn-filled ${isSaved ? "" : "blue"} `}
                  />
                )}
              </div>
            </div>
          </div>
          <div className="related-opportunities">
            <p className="sub-heading text-blue">Related jobs</p>
            {Array.isArray(relatedOpportunities?.opportunities) &&
            relatedOpportunities.opportunities.length > 0 ? (
              relatedOpportunities.opportunities
                .slice(0, 4)
                .filter(
                  (opportunity) => opportunity?._id !== currentOpportunityId
                )
                .map((opportunity) => (
                  <OpportunityItem
                    key={opportunity._id}
                    opportunity={opportunity}
                    language={language}
                    isList
                  />
                ))
            ) : (
              <p>No related opportunities found.</p>
            )}
          </div>
        </Grid>
        <Grid item xs={12} sm={4} className="sidebar">
          {!isMobile ? (
            <div className="opportunity-info">
              {/* <img width={80} height={80} src={findIndustryLogoSvg(industry).src} /> */}
              {findIndustryLogoSvg(industry)}
              <p className="title">{data?.versions[language]?.title}</p>
              <p className="job-ref">Ref: {data?.reference}</p>
              <div className="flex-apply children-componenent">
                <div className="job-info">
                  <SvgFileText />
                  <p>
                    {t("listopportunity:contract")}
                    <br />
                    <span>{data?.contractType || "Agreement"}</span>
                  </p>
                </div>
                <div className="job-info">
                  <SvgMapPin />
                  <p>
                    Location
                    <br />
                    <span>{data?.country}</span>
                  </p>
                </div>
                <div className="job-info">
                  <SvgTime />
                  <p>
                    {t("listopportunity:postingDate")}
                    <br />
                    <span>
                      {moment(data?.versions[language]?.createdAt).format("LL")}
                    </span>
                  </p>
                </div>
                <div className="job-info">
                  <SvgDeadline />
                  <p>
                    {t("listopportunity:expirationDate")}
                    <br />
                    <span>
                      {data?.dateOfExpiration
                        ? capitalizeFirstLetter(
                            moment(data?.dateOfExpiration).format("LL")
                          )
                        : capitalizeFirstLetter(
                            moment().add(3, "months").format("LL")
                          )}
                    </span>
                  </p>
                </div>
                <div className="job-btns">
                  <CustomButton
                    text={isExpired ? "Expired" : "Apply Now"}
                    link={
                      !isExpired && canApply
                        ? `/apply/${data?.versions[language]?.url}`
                        : null
                    }
                    className={`btn btn-filled ${
                      isExpired || !canApply ? "disabled" : ""
                    }`}
                    disabled={isExpired || !canApply}
                  />
                </div>
              </div>
            </div>
          ) : null}

          <div className="section">
            <NewsletterSubscription />
          </div>
          <div id="social-media-share">
            <p className="title">Follow us</p>
            <div>
              <CustomButton
                icon={<SvgFacebook />}
                className={"btn btn-ghost"}
                externalLink={true}
                link="https://www.facebook.com/Pentabell"
              />
              <CustomButton
                icon={<SvgX />}
                className={"btn btn-ghost"}
                externalLink={true}
                link="https://twitter.com/pentabell_group#"
              />
              <CustomButton
                icon={<SvgLinkedin />}
                className={"btn btn-ghost"}
                externalLink={true}
                link="https://www.linkedin.com/company/1859573/"
              />
              <CustomButton
                icon={<SvgInstagram />}
                className={"btn btn-ghost"}
                externalLink={true}
                link="https://www.instagram.com/pentabell/?hl=en"
              />
              <CustomButton
                icon={<SvgYoutube />}
                className={"btn btn-ghost"}
                externalLink={true}
                link="https://www.youtube.com/channel/UCPzmQxP1jVhBSM2-uSSrPvA"
              />
            </div>
          </div>
        </Grid>
      </Grid>
      <DialogModal
        open={showDeleteConfirmation}
        message={t("messages:supprimeropportunityfavoris")}
        onClose={handlecanceldelete}
        onConfirm={handleToggleOpportunity}
      />
    </Container>
  );
}

export default OpportunityPage;
