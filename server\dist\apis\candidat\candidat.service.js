"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const candidat_model_1 = __importDefault(require("./candidat.model"));
const user_model_1 = __importDefault(require("../user/user.model"));
const article_model_1 = __importDefault(require("../article/article.model"));
const contact_model_1 = __importDefault(require("../contact/contact.model"));
const commentaire_model_1 = require("../article/commentaire/commentaire.model");
const opportunity_application_model_1 = __importDefault(require("../opportunity/model/opportunity.application.model"));
const opportunity_model_1 = __importDefault(require("../opportunity/model/opportunity.model"));
const path_1 = __importDefault(require("path"));
const fs = __importStar(require("fs"));
const files_model_1 = __importDefault(require("../storage/files.model"));
class CandidatService {
    constructor() {
        this.Candidat = candidat_model_1.default;
        this.User = user_model_1.default;
        this.File = files_model_1.default;
        this.Article = article_model_1.default;
        this.Contacts = contact_model_1.default;
        this.Comments = commentaire_model_1.CommentModel;
        this.Application = opportunity_application_model_1.default;
        this.Opportunity = opportunity_model_1.default;
    }
    async migrateCandidateCVs(file) {
        if (!file || file.mimetype !== 'application/json') {
            throw new Error('Invalid file type. Please upload a JSON file.');
        }
        const candidates = JSON.parse(file.buffer.toString('utf-8'));
        let updatedCandidates = 0;
        for (const candidate of candidates) {
            if (candidate.cv && Array.isArray(candidate.cv) && candidate.cv.every((cv) => typeof cv === 'string')) {
                const user = await this.getUserDetails(candidate.user.$oid);
                if (user) {
                    candidate.cv = candidate.cv.map((cv, index) => ({
                        fileName: cv,
                        originalName: this.formatOriginalName(user.firstName, user.lastName, index + 1),
                        publishDate: null,
                    }));
                    await this.Candidat.findByIdAndUpdate(candidate._id.$oid, { $set: { cv: candidate.cv } });
                    updatedCandidates++;
                }
            }
        }
    }
    async getUserDetails(userId) {
        return await this.User.findById(userId).select('firstName lastName');
    }
    formatOriginalName(firstName, lastName, nb) {
        return `Resume_${firstName}_${lastName}_${nb}`;
    }
    async calculateProfileCompletion(id) {
        try {
            const candidat = await this.Candidat.findOne({ user: id });
            if (!candidat) {
                throw new http_exception_1.default(404, 'Candidate not found');
            }
            const fieldWeights = {
                cv: 1,
                summary: 1,
                certifications: 1.5,
                educations: 1,
                experiences: 1.5,
                skills: 1,
            };
            let completedFields = 0;
            if (candidat.cv && Array.isArray(candidat.cv) && candidat.cv.length > 0) {
                completedFields += fieldWeights.cv;
            }
            if (candidat.summary && candidat.summary.trim() !== '') {
                completedFields += fieldWeights.summary;
            }
            if (candidat.certifications && Array.isArray(candidat.certifications) && candidat.certifications.length > 0) {
                completedFields += fieldWeights.certifications;
            }
            if (candidat.educations && Array.isArray(candidat.educations) && candidat.educations.length > 0) {
                completedFields += fieldWeights.educations;
            }
            if (candidat.experiences && Array.isArray(candidat.experiences) && candidat.experiences.length > 0) {
                completedFields += fieldWeights.experiences;
            }
            if (candidat.skills && Array.isArray(candidat.skills)) {
                const skillsCount = candidat.skills.length;
                if (skillsCount >= 3) {
                    completedFields += fieldWeights.skills;
                }
            }
            const totalWeight = Object.values(fieldWeights).reduce((sum, weight) => sum + weight, 0);
            let completionPercentage = (completedFields / totalWeight) * 100;
            if (completionPercentage < 20) {
                completionPercentage = 20;
            }
            return { completionPercentage: completionPercentage.toFixed(2) };
        }
        catch (error) {
            throw new http_exception_1.default(500, `Error calculating profile completion`);
        }
    }
    async calculateAllCandidatesProfileCompletion() {
        try {
            const candidats = await this.Candidat.find();
            if (!candidats || candidats.length === 0) {
                throw new Error('No candidates found');
            }
            const fieldWeights = {
                cv: 1,
                summary: 1,
                certifications: 1.5,
                educations: 1,
                experiences: 1.5,
                skills: 1,
            };
            const totalWeight = Object.values(fieldWeights).reduce((sum, weight) => sum + weight, 0);
            const results = [];
            for (const candidat of candidats) {
                let completedFields = 0;
                if (candidat.cv && Array.isArray(candidat.cv) && candidat.cv.length > 0) {
                    completedFields += fieldWeights.cv;
                }
                if (candidat.summary && candidat.summary.trim() !== '') {
                    completedFields += fieldWeights.summary;
                }
                if (candidat.certifications && Array.isArray(candidat.certifications) && candidat.certifications.length > 0) {
                    completedFields += fieldWeights.certifications;
                }
                if (candidat.educations && Array.isArray(candidat.educations) && candidat.educations.length > 0) {
                    completedFields += fieldWeights.educations;
                }
                if (candidat.experiences && Array.isArray(candidat.experiences) && candidat.experiences.length > 0) {
                    completedFields += fieldWeights.experiences;
                }
                if (candidat.skills && Array.isArray(candidat.skills) && candidat.skills.length >= 3) {
                    completedFields += fieldWeights.skills;
                }
                let completionPercentage = 20;
                if (completedFields > 0) {
                    completionPercentage = (completedFields / totalWeight) * 100;
                }
                completionPercentage = Math.max(completionPercentage, 20);
                await this.Candidat.findByIdAndUpdate(candidat._id, { $set: { profilePercentage: completionPercentage } }, { new: true, upsert: true });
                results.push({
                    id: candidat._id.toString(),
                    completionPercentage: completionPercentage.toFixed(2),
                });
            }
            return results;
        }
        catch (error) {
            console.error('Error calculating profiles completion:', error);
            throw new Error('Error calculating profiles completion');
        }
    }
    async getAll(queries) {
        const { name, industries, monthsOfExperiencesMin, monthsOfExperiencesMax, isArchived, jobTitleAng } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 8;
        const queryConditions = { isArchived: false };
        if (name)
            queryConditions['name'] = RegExp(`.*${name}.*`, 'i');
        if (industries) {
            queryConditions['industry'] = { $in: industries.split(',') };
            queryConditions['industries'] = { $in: industries.split(',') };
        }
        if (isArchived)
            queryConditions['isArchived'] = isArchived;
        if (jobTitleAng) {
            queryConditions['jobTitleAng'] = RegExp(`.*${jobTitleAng}.*`, 'i');
        }
        if (monthsOfExperiencesMin && monthsOfExperiencesMax) {
            queryConditions['monthsOfExperiences'] = { $gte: parseInt(monthsOfExperiencesMin), $lte: parseInt(monthsOfExperiencesMax) };
        }
        if (monthsOfExperiencesMin && !monthsOfExperiencesMax) {
            queryConditions['monthsOfExperiences'] = { $gte: parseInt(monthsOfExperiencesMin) };
        }
        if (!monthsOfExperiencesMin && monthsOfExperiencesMax) {
            queryConditions['monthsOfExperiences'] = { $lte: parseInt(monthsOfExperiencesMax) };
        }
        const totalCandidates = await this.Candidat.countDocuments(queryConditions);
        const totalPages = Math.ceil(totalCandidates / pageSize);
        const candidates = await this.Candidat.find(queryConditions)
            .populate('user')
            .sort({ createdAt: -1 })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize || 10)
            .lean();
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalCandidates,
            candidates,
        };
    }
    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    async archive(id) {
        const candidate = await this.Candidat.findOne({ user: id });
        if (!candidate)
            throw new http_exception_1.default(404, 'Candidate not found');
        await this.Candidat.findByIdAndUpdate(id, { isArchived: !candidate.isArchived });
    }
    async getbyid(id) {
        const candidate = await this.Candidat.findById(id).populate('user certifications educations experiences').lean();
        if (!candidate)
            throw new http_exception_1.default(404, 'Candidate not found');
        return candidate;
    }
    async get(id) {
        const candidate = await this.Candidat.findOne({ user: id }).populate('user certifications educations experiences').lean();
        if (!candidate)
            throw new http_exception_1.default(404, 'Candidate not found');
        return candidate;
    }
    async getCandidateBase64(id) {
        const candidate = await this.Candidat.findById(id).populate('user certifications educations experiences').lean();
        if (!candidate)
            throw new http_exception_1.default(404, 'Candidate not found');
        if (candidate.cv && candidate.cv.length > 0) {
            const cv = candidate.cv[0];
            const cvFileName = cv.fileName;
            try {
                const file = await this.File.findOne({ fileName: cvFileName });
                if (!file)
                    throw new http_exception_1.default(404, 'File not found');
                const folder = file.folder;
                const cvFilePath = path_1.default.join(__dirname, '../../../uploads/candidates', folder, cvFileName);
                if (fs.existsSync(cvFilePath)) {
                    const fileContent = await fs.promises.readFile(cvFilePath);
                    const cvBase64 = fileContent.toString('base64');
                    candidate.cvBase64 = cvBase64;
                    return candidate;
                }
                else {
                    console.error('CV not found:', cvFilePath);
                    throw new http_exception_1.default(404, 'CV file not found');
                }
            }
            catch (error) {
                console.error('Error reading CV:', error);
                throw new http_exception_1.default(500, 'Error reading CV');
            }
        }
        throw new http_exception_1.default(404, 'No CV found for this candidate');
    }
    async getById(id) {
        const candidate = await this.Candidat.findById(id).populate('user certifications educations experiences').lean();
        if (!candidate)
            throw new http_exception_1.default(404, 'Candidate not found');
        return candidate;
    }
    async getByUser(id) {
        const candidate = await this.Candidat.findOne({ user: id }).populate('user certifications educations experiences').lean();
        if (!candidate)
            throw new http_exception_1.default(404, 'Candidate not found');
        return candidate;
    }
    async update(currentUser, candidatData) {
        const candidate = await this.getByUser(currentUser);
        if (candidatData.cv) {
            if (!Array.isArray(candidatData.cv)) {
                candidatData.cv = [candidatData.cv];
            }
            if (candidatData.cv) {
                if (!Array.isArray(candidatData.cv)) {
                    candidatData.cv = [candidatData.cv];
                }
                if (candidate.cv) {
                    const existingCvs = new Map(candidate.cv.map((cv) => [cv.fileName, cv]));
                    const newCvs = candidatData.cv.filter((cv) => !existingCvs.has(cv.fileName));
                    candidatData.cv = [...candidate.cv, ...newCvs];
                }
                else {
                    const uniqueCvs = new Map(candidatData.cv.map((cv) => [cv.fileName, cv]));
                    candidatData.cv = Array.from(uniqueCvs.values());
                }
            }
        }
        await this.Candidat.findByIdAndUpdate(candidate._id, { $set: candidatData });
        const { completionPercentage } = await this.calculateProfileCompletion(currentUser);
        await this.Candidat.findByIdAndUpdate(candidate._id, { $set: { profilePercentage: completionPercentage } });
    }
    async deleteCandidateResume(candidateId, fileName) {
        const candidate = await this.Candidat.findOne({ user: candidateId });
        if (!candidate || !candidate.cv) {
            throw new http_exception_1.default(404, 'Candidate or CV not found.');
        }
        const updatedCVs = candidate.cv.filter((cv) => cv.fileName !== fileName);
        if (updatedCVs.length === candidate.cv.length) {
            throw new http_exception_1.default(404, 'Resume not found in candidate CV.');
        }
        candidate.cv = updatedCVs;
        await candidate.save();
        const { completionPercentage } = await this.calculateProfileCompletion(candidateId);
        await candidat_model_1.default.findByIdAndUpdate(candidateId, { $set: { profilePercentage: completionPercentage } });
    }
}
exports.default = CandidatService;
//# sourceMappingURL=candidat.service.js.map