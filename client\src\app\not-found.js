import Script from "next/script";
import { Container } from "@mui/material";

import CustomButton from "../components/ui/CustomButton";

export default function NotFound() {
  return (
    <html>
      <head>
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Black.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Bold.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Extrabold.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Light.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Medium.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Regular.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Semibold.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        <link
          rel="preload"
          href="/fonts/proxima-nova/WOFF/Proxima-Nova-Thin.woff"
          as="font"
          type="font/woff"
          crossOrigin="anonymous"
        />
        {/* <Partytown debug={true} forward={["dataLayer.push"]} /> */}
        <Script
          id="cookieyes-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
          window.addEventListener('load', function() {
            // Initialize CookieYes
            window.cookieyes || (window.cookieyes = {});

            // Load CookieYes Script
            var script = document.createElement('script');
            script.src = 'https://cdn.cookieyes.com/client_data.js'; 
            script.type = 'text/javascript';
            script.async = true;
            script.onload = function () {
              // CookieYes initialization with your domain settings
              cookieyes.init({
                consent_url: '/cookie-policy',
                onConsentGiven: function () {
                  window.dataLayer = window.dataLayer || [];
                  window.dataLayer.push({'event': 'cookie_consent_given'});
                },
              });
            };
            document.body.appendChild(script);
          });
        `,
          }}
        />
        <Script
          id="gtm-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
      (function(w,d,s,l,i){
        w[l]=w[l]||[];
        w[l].push({'gtm.start': new Date().getTime(), event:'gtm.js'});
        var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s), dl=l!='dataLayer'?'&l='+l:'';
        j.async=true; j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
        f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-NXLL5DG');
    `,
          }}
        />
      </head>
      <body>
        <noscript
          dangerouslySetInnerHTML={{
            __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NXLL5DG" height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
          }}
        ></noscript>
        <div id="page-not-found">
          <Container className="container">
            <p className="heading-h1 text-white text-center">
              404 - Page Not Found
            </p>
            <p className="sub-heading text-white text-center">
              Oops! The page you're looking for seems to have disappeared. Go
              back to the home page and continue to navigate.
            </p>
            <CustomButton
              text={"Go back to Home"}
              link={"/"}
              className={"btn btn-filled"}
            />
          </Container>
        </div>
      </body>
    </html>
  );
}
