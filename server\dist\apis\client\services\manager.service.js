"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const manager_model_1 = __importDefault(require("../models/manager.model"));
const client_model_1 = __importDefault(require("../models/client.model"));
const messages_1 = require("@/utils/helpers/messages");
class ManagerService {
    constructor() {
        this.Manager = manager_model_1.default;
        this.Client = client_model_1.default;
    }
    async create(managerData) {
        const existClient = await this.Client.findById(managerData.client);
        if (!existClient) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.CLIENTS.CLIENT_NOT_FOUND);
        }
        const oldManager = await this.Manager.findOne({
            email: managerData.email
        });
        if (oldManager)
            throw new http_exception_1.default(409, messages_1.MESSAGES.MANAGERS.ALREADY_EXIST);
        return await this.Manager.create(managerData);
    }
    async createMany(file) {
        const managersData = JSON.parse(file.buffer.toString());
        for (const manager of managersData) {
            const oldManager = await this.Manager.findOne({
                email: manager.email,
            });
            if (oldManager) {
                throw new http_exception_1.default(409, messages_1.MESSAGES.MANAGERS.ALREADY_EXIST);
            }
        }
        return await this.Manager.create(managersData);
    }
    async get(managerId) {
        const manager = await this.Manager.findById(managerId);
        if (!manager)
            throw new http_exception_1.default(404, messages_1.MESSAGES.MANAGERS.MANAGER_NOT_FOUND);
        return manager;
    }
    async getAll(queries) {
        const { firstName, lastName, clientName, paginated } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 10;
        const query = {};
        if (firstName) {
            query['firstName'] = RegExp(`.*${firstName}.*`, 'i');
        }
        if (lastName) {
            query['lastName'] = RegExp(`.*${lastName}.*`, 'i');
        }
        if (clientName) {
            const client = await this.Client.findOne({ name: new RegExp(clientName, 'i') });
            query['client'] = client?._id;
        }
        if (paginated && paginated === "false") {
            return await this.Manager.find(query).populate({ path: 'client', select: 'name' }).sort({ 'client.name': 1, createdAt: 'desc' });
        }
        const totalManagers = await this.Manager.countDocuments(query);
        const totalPages = Math.ceil(totalManagers / pageSize);
        const managers = await this.Manager.find(query)
            .populate({ path: 'client', select: 'name' })
            .sort({ 'client.name': 1, createdAt: 'desc' })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize || 10);
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalManagers,
            managers,
        };
    }
    async update(managerId, managerData) {
        await this.get(managerId);
        return await this.Manager.findByIdAndUpdate(managerId, managerData, { new: true });
    }
    async delete(managerId) {
        await this.get(managerId);
        await this.Manager.findOneAndDelete({ _id: managerId });
    }
}
exports.default = ManagerService;
//# sourceMappingURL=manager.service.js.map