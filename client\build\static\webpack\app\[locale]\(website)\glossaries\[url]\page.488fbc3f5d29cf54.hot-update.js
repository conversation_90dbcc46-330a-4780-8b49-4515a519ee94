"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GlossaryDetails(param) {\n    let { id, article, language, url } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    moment.locale(i18n.language || \"en\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page-details\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"glossary-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"custom-max-width\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"glossary-path\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    locale: language === \"en\" ? \"en\" : \"fr\",\n                                    href: \"\".concat(language === \"en\" ? \"/glossary\" : \"/\".concat(language, \"/glossary\"), \"/\"),\n                                    className: \"link\",\n                                    children: \"Glossary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 15\n                                }, this),\n                                (article === null || article === void 0 ? void 0 : article.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"word\",\n                                            children: article === null || article === void 0 ? void 0 : article.word\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    type: \"application/ld+json\",\n                    dangerouslySetInnerHTML: {\n                        __html: JSON.stringify({\n                            \"@context\": \"https://schema.org\",\n                            \"@type\": \"BreadcrumbList\",\n                            itemListElement: [\n                                {\n                                    \"@type\": \"ListItem\",\n                                    position: 1,\n                                    item: {\n                                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                                        name: \"Glossary\"\n                                    }\n                                }\n                            ]\n                        })\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"custom-max-width\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"container\",\n                        container: true,\n                        columnSpacing: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glossary-content\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    htmlContent: article === null || article === void 0 ? void 0 : article.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(GlossaryDetails, \"ppfPIy26qNvoC3TgdLaSQDj56YY=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = GlossaryDetails;\nvar _c;\n$RefreshReg$(_c, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});