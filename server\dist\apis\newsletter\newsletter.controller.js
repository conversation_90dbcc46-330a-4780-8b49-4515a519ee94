"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsletterController = void 0;
const express_1 = require("express");
const newsletter_service_1 = __importDefault(require("./newsletter.service"));
class NewsletterController {
    constructor() {
        this.path = '/newsletter';
        this.router = (0, express_1.Router)();
        this.newsletterService = new newsletter_service_1.default();
        this.subscribeToNewsletter = async (request, response, next) => {
            try {
                const { email } = request.body;
                const subscription = await this.newsletterService.subscribeToNewsletter(email);
                response.status(201).send(subscription);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllNewsletters = async (request, response, next) => {
            try {
                const queries = request.query;
                const newsletters = await this.newsletterService.getAllNewsletters(queries);
                response.status(200).send(newsletters);
            }
            catch (error) {
                next(error);
            }
        };
        this.unsubscribeToNewsletter = async (request, response, next) => {
            try {
                const { email } = request.body;
                const unsubscription = await this.newsletterService.unsubscribeFromNewsletter(email);
                response.status(201).send(unsubscription);
            }
            catch (error) {
                next(error);
            }
        };
        this.fillGetInTouchFrom = async (request, response, next) => {
            try {
                await this.newsletterService.fillGetInTouchFrom(request.body);
                response.status(201).send({ message: 'Application sent successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.post(`${this.path}`, this.subscribeToNewsletter);
        this.router.get(`${this.path}`, this.getAllNewsletters);
        this.router.post(`${this.path}/contact`, this.fillGetInTouchFrom);
        this.router.post(`${this.path}/unsubscribe`, this.unsubscribeToNewsletter);
    }
}
exports.NewsletterController = NewsletterController;
//# sourceMappingURL=newsletter.controller.js.map