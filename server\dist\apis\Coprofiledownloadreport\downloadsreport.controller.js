"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const downloadsreport_model_1 = require("./downloadsreport.model");
const downloadsreport_service_1 = __importDefault(require("./downloadsreport.service"));
class DownloadReport {
    constructor() {
        this.path = '/report';
        this.router = (0, express_1.Router)();
        this.downloadreportService = new downloadsreport_service_1.default();
        this.handleReportDownload = async (req, res, next) => {
            try {
                const { fullName, email } = req.body;
                const message = await this.downloadreportService.handleReportDownload(fullName, email);
                return res.status(200).json({ message });
            }
            catch (error) {
                if (error.message === 'FullName and email are required') {
                    return res.status(400).json({ message: error.message });
                }
                next(error);
            }
        };
        this.getAllDownloadReport = async (req, res, next) => {
            try {
                const { keyWord, paginated, pageNumber = '1', pageSize = '3' } = req.query;
                const page = Number(pageNumber) || 1;
                const size = Number(pageSize) || 2;
                const queryConditions = {};
                if (keyWord) {
                    queryConditions['email'] = {
                        $regex: new RegExp(`.*${keyWord}.*`, 'i'),
                    };
                }
                const totalDownloads = await downloadsreport_model_1.DownloadReportModel.countDocuments(queryConditions);
                const totalPages = Math.ceil(totalDownloads / size);
                let downloads;
                if (paginated === 'false' || !paginated) {
                    downloads = await downloadsreport_model_1.DownloadReportModel.find(queryConditions).sort({ createdAt: -1 });
                    return res.status(200).json({
                        totalDownloads,
                        downloads,
                    });
                }
                downloads = await downloadsreport_model_1.DownloadReportModel.find(queryConditions)
                    .sort({ createdAt: -1 })
                    .skip((page - 1) * size)
                    .limit(size);
                return res.status(200).json({
                    pageNumber: page,
                    pageSize: size,
                    totalPages,
                    totalDownloads,
                    downloads,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.post(`${this.path}/download`, this.handleReportDownload);
        this.router.get(`${this.path}`, this.getAllDownloadReport);
    }
}
exports.default = DownloadReport;
//# sourceMappingURL=downloadsreport.controller.js.map