"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/PentabellCompanySection.jsx":
/*!*********************************************************************!*\
  !*** ./src/features/glossary/component/PentabellCompanySection.jsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PentabellCompanySection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _assets_images_website_PentabellOfficesIcon_svg__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/assets/images/website/PentabellOfficesIcon.svg */ \"(app-pages-browser)/./src/assets/images/website/PentabellOfficesIcon.svg\");\n/* harmony import */ var _barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n\n\n\nfunction PentabellCompanySection() {\n    const handleSocialMediaClick = (url)=>{\n        window.open(url, \"_blank\");\n    };\n    const handleContactUsClick = ()=>{\n        window.open(\"https://www.pentabell.com/contact-us/\", \"_blank\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pentabell-company\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"title\",\n                        children: \"Pentabell Company\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\PentabellCompanySection.jsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"description\",\n                        children: [\n                            \"Absenteeism is the persistent absence of individuals from work, usually without a valid or authorized reason. \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\PentabellCompanySection.jsx\",\n                                lineNumber: 19,\n                                columnNumber: 57\n                            }, this),\n                            \"In most instances, employee absenteeism is characterized by repeated and intentional failure to fulfill obligations which can lead to decreased.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\PentabellCompanySection.jsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"btn btn-filled-yellow\",\n                        children: \"Contact us\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\PentabellCompanySection.jsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\PentabellCompanySection.jsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pentabell-offices-icon\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_website_PentabellOfficesIcon_svg__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\PentabellCompanySection.jsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\PentabellCompanySection.jsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\PentabellCompanySection.jsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_c = PentabellCompanySection;\nvar _c;\n$RefreshReg$(_c, \"PentabellCompanySection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/PentabellCompanySection.jsx\n"));

/***/ })

});