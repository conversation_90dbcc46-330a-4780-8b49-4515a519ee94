"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryHeader.jsx":
/*!************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryHeader.jsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryHeader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Container!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\");\n\n\n\nfunction GlossaryHeader(param) {\n    let { language, glossaryPath } = param;\n    var _article, _article1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-header\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"custom-max-width\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"glossary-path\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            locale: language === \"en\" ? \"en\" : \"fr\",\n                            href: \"\".concat(glossaryPath, \"/\"),\n                            className: \"link\",\n                            children: \"Glossary\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryHeader.jsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this),\n                        ((_article = article) === null || _article === void 0 ? void 0 : _article.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryHeader.jsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"word\",\n                                    children: article.word\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryHeader.jsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryHeader.jsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"letter\",\n                    children: (_article1 = article) === null || _article1 === void 0 ? void 0 : _article1.letter\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryHeader.jsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryHeader.jsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryHeader.jsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n_c = GlossaryHeader;\nvar _c;\n$RefreshReg$(_c, \"GlossaryHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryHeader.jsx\n"));

/***/ })

});