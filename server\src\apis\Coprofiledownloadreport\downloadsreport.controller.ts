
import { Request, Response, NextFunction, Router } from 'express';
import path from 'path';
import { DownloadReportModel } from './downloadsreport.model';
import { sendEmail } from '@/utils/services';
import Controller from '@/utils/interfaces/controller.interface';
import DownloadReportService from './downloadsreport.service';

class DownloadReport implements Controller {
  public readonly path = '/report';
  public readonly router = Router();
private readonly downloadreportService = new DownloadReportService();
  constructor() {
    this.initialiseRoutes();
  }
  private initialiseRoutes(): void {
    this.router.post(`${this.path}/download`, this.handleReportDownload)
    this.router.get(`${this.path}`, this.getAllDownloadReport)
  }
  private handleReportDownload = async (req: Request, res: Response, next: NextFunction) =>{
    try {
      const { fullName, email } = req.body;
      const message = await this.downloadreportService.handleReportDownload(fullName, email);
      return res.status(200).json({ message });
    } catch (error: any) {
      if (error.message === 'FullName and email are required') {
        return res.status(400).json({ message: error.message });
      }
      next(error);
    }
  }


  private readonly getAllDownloadReport = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { keyWord, paginated, pageNumber = '1', pageSize = '3' } = req.query;

      const page = Number(pageNumber) || 1;
      const size = Number(pageSize) || 2;

      const queryConditions: any = {};

      if (keyWord) {
        queryConditions['email'] = {
          $regex: new RegExp(`.*${keyWord}.*`, 'i'),
        };

      }

      const totalDownloads = await DownloadReportModel.countDocuments(queryConditions);
      const totalPages = Math.ceil(totalDownloads / size);

      let downloads;

      if (paginated === 'false' || !paginated) {
        downloads = await DownloadReportModel.find(queryConditions).sort({ createdAt: -1 });

        return res.status(200).json({
          totalDownloads,
          downloads,
        });
      }

      downloads = await DownloadReportModel.find(queryConditions)
        .sort({ createdAt: -1 })
        .skip((page - 1) * size)
        .limit(size);

      return res.status(200).json({
        pageNumber: page,
        pageSize: size,
        totalPages,
        totalDownloads,
        downloads,
      });
    } catch (error) {
      next(error);
    }
  };

}
export default DownloadReport;