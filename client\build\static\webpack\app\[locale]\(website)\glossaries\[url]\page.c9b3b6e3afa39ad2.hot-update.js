"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _assets_images_icons_instagram_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/instagram.svg */ \"(app-pages-browser)/./src/assets/images/icons/instagram.svg\");\n/* harmony import */ var _assets_images_icons_linkedin_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/linkedin.svg */ \"(app-pages-browser)/./src/assets/images/icons/linkedin.svg\");\n/* harmony import */ var _assets_images_icons_facebook_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/facebook.svg */ \"(app-pages-browser)/./src/assets/images/icons/facebook.svg\");\n/* harmony import */ var _assets_images_icons_x_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/x.svg */ \"(app-pages-browser)/./src/assets/images/icons/x.svg\");\n/* harmony import */ var _PentabellCompanySection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PentabellCompanySection */ \"(app-pages-browser)/./src/features/glossary/component/PentabellCompanySection.jsx\");\n/* harmony import */ var _GlossaryHeader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./GlossaryHeader */ \"(app-pages-browser)/./src/features/glossary/component/GlossaryHeader.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst GlossaryDetails = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function GlossaryDetails(param) {\n    let { glossary, language, isMobileSSR } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(theme.breakpoints.down(\"sm\")) || isMobileSSR;\n    const isTablet = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(theme.breakpoints.down(\"md\")) || isMobileSSR;\n    const breadcrumbSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"BreadcrumbList\",\n            itemListElement: [\n                {\n                    \"@type\": \"ListItem\",\n                    position: 1,\n                    item: {\n                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                        name: \"Glossary\"\n                    }\n                }\n            ]\n        }), [\n        language\n    ]);\n    const glossaryPath = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>language === \"en\" ? \"/glossaries\" : \"/\".concat(language, \"/glossaries\"), [\n        language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page-details\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    type: \"application/ld+json\",\n                    dangerouslySetInnerHTML: {\n                        __html: JSON.stringify(breadcrumbSchema)\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"container\",\n                    container: true,\n                    columnSpacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            item: true,\n                            sm: 12,\n                            md: 12,\n                            lg: 9,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlossaryHeader__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"custom-max-width\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            htmlContent: glossary === null || glossary === void 0 ? void 0 : glossary.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PentabellCompanySection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            language: language,\n                                            glossaryPath: glossaryPath,\n                                            glossary: glossary\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        isMobile && isTablet && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glossary-social-media-icons\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_facebook_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_linkedin_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_x_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_instagram_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        !(isMobile && isTablet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            item: true,\n                            sm: 12,\n                            md: 12,\n                            lg: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"custom-max-width\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glossary-social-media-icons\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_facebook_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_linkedin_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_x_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_instagram_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n            lineNumber: 52,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}, \"b0xrQkBwMRtDSXZDGNM7QmBe+QA=\", false, function() {\n    return [\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    ];\n})), \"b0xrQkBwMRtDSXZDGNM7QmBe+QA=\", false, function() {\n    return [\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    ];\n});\n_c1 = GlossaryDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryDetails);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlossaryDetails$memo\");\n$RefreshReg$(_c1, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});