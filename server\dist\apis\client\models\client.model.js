"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const clientSchema = new mongoose_1.Schema({
    name: {
        type: String,
        unique: true,
        index: true,
        required: true,
    },
    hidden: {
        type: Boolean,
        default: false
    },
    industry: {
        type: String,
        enum: constants_1.Industry
    },
    logo: {
        type: String,
        default: '',
    }
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
    discriminatorKey: 'type',
});
exports.default = (0, mongoose_1.model)('Client', clientSchema);
//# sourceMappingURL=client.model.js.map