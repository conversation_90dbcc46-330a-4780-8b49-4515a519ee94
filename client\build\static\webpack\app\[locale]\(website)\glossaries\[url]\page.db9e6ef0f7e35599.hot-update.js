"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/hooks/usePerformanceMonitor.js":
/*!********************************************!*\
  !*** ./src/hooks/usePerformanceMonitor.js ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMemoryMonitor: function() { return /* binding */ useMemoryMonitor; },\n/* harmony export */   usePerformanceMonitor: function() { return /* binding */ usePerformanceMonitor; },\n/* harmony export */   useRenderPerformance: function() { return /* binding */ useRenderPerformance; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ usePerformanceMonitor,useRenderPerformance,useMemoryMonitor auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n/**\n * Custom hook for monitoring performance metrics\n * @param {boolean} enabled - Whether to enable performance monitoring\n * @param {Function} onMetrics - Callback function to handle metrics\n */ function usePerformanceMonitor() {\n    let enabled = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true, onMetrics = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n    _s();\n    const measureWebVitals = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!enabled || \"object\" === \"undefined\") return;\n        // Largest Contentful Paint (LCP)\n        if (\"PerformanceObserver\" in window) {\n            try {\n                const lcpObserver = new PerformanceObserver((entryList)=>{\n                    const entries = entryList.getEntries();\n                    const lastEntry = entries[entries.length - 1];\n                    const lcp = lastEntry.startTime;\n                    if (onMetrics) {\n                        onMetrics({\n                            metric: \"LCP\",\n                            value: lcp,\n                            rating: lcp < 2500 ? \"good\" : lcp < 4000 ? \"needs-improvement\" : \"poor\"\n                        });\n                    }\n                    if (true) {\n                        console.log(\"LCP:\", lcp, \"ms\");\n                    }\n                });\n                lcpObserver.observe({\n                    entryTypes: [\n                        \"largest-contentful-paint\"\n                    ]\n                });\n                // First Input Delay (FID)\n                const fidObserver = new PerformanceObserver((entryList)=>{\n                    const entries = entryList.getEntries();\n                    entries.forEach((entry)=>{\n                        const fid = entry.processingStart - entry.startTime;\n                        if (onMetrics) {\n                            onMetrics({\n                                metric: \"FID\",\n                                value: fid,\n                                rating: fid < 100 ? \"good\" : fid < 300 ? \"needs-improvement\" : \"poor\"\n                            });\n                        }\n                        if (true) {\n                            console.log(\"FID:\", fid, \"ms\");\n                        }\n                    });\n                });\n                fidObserver.observe({\n                    entryTypes: [\n                        \"first-input\"\n                    ]\n                });\n                // Cumulative Layout Shift (CLS)\n                let clsValue = 0;\n                const clsObserver = new PerformanceObserver((entryList)=>{\n                    const entries = entryList.getEntries();\n                    entries.forEach((entry)=>{\n                        if (!entry.hadRecentInput) {\n                            clsValue += entry.value;\n                        }\n                    });\n                    if (onMetrics) {\n                        onMetrics({\n                            metric: \"CLS\",\n                            value: clsValue,\n                            rating: clsValue < 0.1 ? \"good\" : clsValue < 0.25 ? \"needs-improvement\" : \"poor\"\n                        });\n                    }\n                    if (true) {\n                        console.log(\"CLS:\", clsValue);\n                    }\n                });\n                clsObserver.observe({\n                    entryTypes: [\n                        \"layout-shift\"\n                    ]\n                });\n                // Total Blocking Time (TBT) approximation\n                const tbtObserver = new PerformanceObserver((entryList)=>{\n                    let tbt = 0;\n                    const entries = entryList.getEntries();\n                    entries.forEach((entry)=>{\n                        if (entry.duration > 50) {\n                            tbt += entry.duration - 50;\n                        }\n                    });\n                    if (onMetrics) {\n                        onMetrics({\n                            metric: \"TBT\",\n                            value: tbt,\n                            rating: tbt < 200 ? \"good\" : tbt < 600 ? \"needs-improvement\" : \"poor\"\n                        });\n                    }\n                    if (true) {\n                        console.log(\"TBT (approx):\", tbt, \"ms\");\n                    }\n                });\n                tbtObserver.observe({\n                    entryTypes: [\n                        \"longtask\"\n                    ]\n                });\n            } catch (error) {\n                console.warn(\"Performance monitoring not supported:\", error);\n            }\n        }\n    }, [\n        enabled,\n        onMetrics\n    ]);\n    const measurePageLoad = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!enabled || \"object\" === \"undefined\") return;\n        window.addEventListener(\"load\", ()=>{\n            setTimeout(()=>{\n                if (\"performance\" in window) {\n                    const perfData = performance.getEntriesByType(\"navigation\")[0];\n                    const metrics = {\n                        \"DNS Lookup\": perfData.domainLookupEnd - perfData.domainLookupStart,\n                        \"TCP Connection\": perfData.connectEnd - perfData.connectStart,\n                        Request: perfData.responseStart - perfData.requestStart,\n                        Response: perfData.responseEnd - perfData.responseStart,\n                        \"DOM Processing\": perfData.domComplete - perfData.domLoading,\n                        \"Total Load Time\": perfData.loadEventEnd - perfData.navigationStart,\n                        \"Time to Interactive\": perfData.domInteractive - perfData.navigationStart,\n                        \"DOM Content Loaded\": perfData.domContentLoadedEventEnd - perfData.navigationStart\n                    };\n                    if (onMetrics) {\n                        Object.entries(metrics).forEach((param)=>{\n                            let [metric, value] = param;\n                            onMetrics({\n                                metric,\n                                value,\n                                type: \"navigation\"\n                            });\n                        });\n                    }\n                    if (true) {\n                        console.table(metrics);\n                    }\n                }\n            }, 0);\n        });\n    }, [\n        enabled,\n        onMetrics\n    ]);\n    const measureResourceTiming = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!enabled || \"object\" === \"undefined\") return;\n        window.addEventListener(\"load\", ()=>{\n            setTimeout(()=>{\n                const resources = performance.getEntriesByType(\"resource\");\n                const slowResources = resources.filter((resource)=>resource.duration > 1000);\n                if (slowResources.length > 0 && \"development\" === \"development\") {\n                    console.warn(\"Slow loading resources:\", slowResources);\n                }\n                if (onMetrics) {\n                    onMetrics({\n                        metric: \"Slow Resources\",\n                        value: slowResources.length,\n                        details: slowResources.map((r)=>({\n                                name: r.name,\n                                duration: r.duration\n                            }))\n                    });\n                }\n            }, 1000);\n        });\n    }, [\n        enabled,\n        onMetrics\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!enabled) return;\n        measureWebVitals();\n        measurePageLoad();\n        measureResourceTiming();\n    }, [\n        enabled,\n        measureWebVitals,\n        measurePageLoad,\n        measureResourceTiming\n    ]);\n    return {\n        measureWebVitals,\n        measurePageLoad,\n        measureResourceTiming\n    };\n}\n_s(usePerformanceMonitor, \"t3FlveAqo7gc4iLs616ulQcHo/4=\");\n/**\n * Hook for monitoring component render performance\n * @param {string} componentName - Name of the component\n * @param {boolean} enabled - Whether to enable monitoring\n */ function useRenderPerformance(componentName) {\n    let enabled = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n    _s1();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!enabled || \"development\" !== \"development\") return;\n        const startTime = performance.now();\n        return ()=>{\n            const endTime = performance.now();\n            const renderTime = endTime - startTime;\n            if (renderTime > 16) {\n                // More than one frame (16ms)\n                console.warn(\"\".concat(componentName, \" render took \").concat(renderTime.toFixed(2), \"ms\"));\n            }\n        };\n    });\n}\n_s1(useRenderPerformance, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n/**\n * Hook for monitoring memory usage\n * @param {boolean} enabled - Whether to enable monitoring\n */ function useMemoryMonitor() {\n    let enabled = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n    _s2();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!enabled || \"object\" === \"undefined\" || !(\"memory\" in performance)) return;\n        const checkMemory = ()=>{\n            const memory = performance.memory;\n            const memoryInfo = {\n                usedJSHeapSize: (memory.usedJSHeapSize / 1048576).toFixed(2) + \" MB\",\n                totalJSHeapSize: (memory.totalJSHeapSize / 1048576).toFixed(2) + \" MB\",\n                jsHeapSizeLimit: (memory.jsHeapSizeLimit / 1048576).toFixed(2) + \" MB\"\n            };\n            if (true) {\n                console.log(\"Memory Usage:\", memoryInfo);\n            }\n            // Warn if memory usage is high\n            if (memory.usedJSHeapSize / memory.jsHeapSizeLimit > 0.8) {\n                console.warn(\"High memory usage detected\");\n            }\n        };\n        const interval = setInterval(checkMemory, 30000); // Check every 30 seconds\n        return ()=>clearInterval(interval);\n    }, [\n        enabled\n    ]);\n}\n_s2(useMemoryMonitor, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/usePerformanceMonitor.js\n"));

/***/ })

});