import { redirect } from "next/navigation";

import { axiosGetJsonSSR } from "@/config/axios";
import BlogPageDetails from "@/features/blog/components/new-blog/BlogPageDetails";
import { headers } from "next/headers";

export async function generateMetadata({ params }) {
  const requestHeaders = headers();
  const cookie = requestHeaders.get("cookie");

  const language = params.locale;
  const url = params?.url;
  let canonicalUrl = `https://www.pentabell.com/${
    language !== "en" ? `${language}/` : ""
  }blog/${url}/`;

  try {
    const res = await axiosGetJsonSSR.get(`/articles/${language}/blog/${url}`, {
      headers: {
        Cookie: cookie,
      },
    });

    const article = res?.data?.versions[0];

    let languages = {};

    try {
      const res = await axiosGetJsonSSR.get(
        `/articles/opposite/${language}/${url}`
      );

      const oppositeLanguageUrl = res.data.slug;

      if (language === "fr") {
        languages.fr = `https://www.pentabell.com/fr/blog/${url}/`;
        languages.en = `https://www.pentabell.com/blog/${oppositeLanguageUrl}/`;
        languages[
          "x-default"
        ] = `https://www.pentabell.com/blog/${oppositeLanguageUrl}/`;
        canonicalUrl = `https://www.pentabell.com/fr/blog/${url}/`;
      }

      if (language === "en") {
        languages.fr = `https://www.pentabell.com/fr/blog/${oppositeLanguageUrl}/`;
        languages.en = `https://www.pentabell.com/blog/${url}/`;
        languages["x-default"] = `https://www.pentabell.com/blog/${url}/`;
        canonicalUrl = `https://www.pentabell.com/blog/${url}/`;
      }
    } catch (error) {
      console.log("error", error.response.data);
    }

    if (article) {
      return {
        title: article.metaTitle,
        description: article.metaDescription,
        robots:
          res?.data?.robotsMeta === "index"
            ? "follow, index, max-snippet:-1, max-image-preview:large"
            : "noindex",
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
        openGraph: {
          title: article?.title,
          description: article?.content
            ?.replace(/<\/?[^>]+(>|$)/g, " ")
            ?.replace(/&[a-z]+;/gi, "")
            ?.replace(/\s\s+/g, " ")
            ?.slice(0, 500),
          url:
            params.locale === "en"
              ? `https://www.pentabell.com/opportunities/${article?.url}/`
              : `https://www.pentabell.com/opportunities/${params.locale}/${article?.url}/`,
          images: [
            {
              url: article?.image
                ? `https://www.pentabell.com/api/v1/files/${article?.image}`
                : null,
              alt: article?.alt,
            },
          ],
        },
      };
    }
  } catch (error) {
    return {
      title: "Error",
      description: "An error occurred while fetching the article",
    };
  }

  return {
    title: "Blog",
    description: "Blog page with articles and categories",
  };
}

export default async function Page({ params }) {
  const language = params.locale;
  const url = params?.url;

  const requestHeaders = headers();
  const cookie = requestHeaders.get("cookie");

  try {
    const res = await axiosGetJsonSSR.get(`/articles/${language}/blog/${url}`, {
      headers: {
        Cookie: cookie,
      },
    });

    const oneArticle = res?.data;
    const article = oneArticle?.versions[0];
    const articleId = oneArticle?._id;

    return (
      <>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BlogPosting",
              mainEntityOfPage: {
                "@type": "WebPage",
                "@id":
                  language === "en"
                    ? `https://www.pentabell.com/blog/${url}`
                    : `https://www.pentabell.com/${language}/blog/${url}`,
              },
              headline: oneArticle?.versions[0]?.title,
              description: oneArticle?.versions[0]?.metaDescription,
              image: `https://www.pentabell.com/api/v1/files/${oneArticle?.versions[0]?.image}`,
              publisher: {
                "@type": "Organization",
                name: "Pentabell",
                logo: {
                  "@type": "ImageObject",
                  url: "https://www.pentabell.com/api/v1/images/pentabell.png",
                },
              },
              datePublished: oneArticle?.versions[0]?.publishDate,
              dateModified: oneArticle?.versions[0]?.updatedAt,
              keywords: oneArticle?.versions[0]?.keywords,
              articleSection: oneArticle?.versions[0]?.categories?.map(
                (category) => category?.name
              ),
            }),
          }}
        />
        <BlogPageDetails
          id={articleId}
          article={article}
          language={language}
          url={url}
        />
      </>
    );
  } catch (error) {
    redirect(params.locale === "en" ? `/blog/` : `/${params.locale}/blog/`);
  }
}
