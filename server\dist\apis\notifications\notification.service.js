"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const notification_model_1 = __importDefault(require("./notification.model"));
class NotificationService {
    constructor() {
        this.Notification = notification_model_1.default;
    }
    async get(currentUser, query) {
        const { pageNumber = 1, pageSize = 4, startDate, endDate, isRead, isFavourite, type } = query;
        const queryConditions = {
            receiver: currentUser._id,
        };
        if (startDate || endDate) {
            queryConditions['createdAt'] = {};
            if (startDate) {
                queryConditions['createdAt'].$gte = new Date(startDate);
            }
            if (endDate) {
                const endDateObj = new Date(endDate);
                endDateObj.setHours(23, 59, 59);
                queryConditions['createdAt'].$lte = endDateObj;
            }
        }
        if (isRead && isRead !== '' && isRead !== 'all') {
            queryConditions['isRead'] = isRead === 'true';
        }
        if (isFavourite && isFavourite !== '' && isFavourite !== 'all') {
            queryConditions['isFavourite'] = isFavourite === 'true';
        }
        if (type !== undefined) {
            queryConditions['type'] = type;
        }
        const notifications = await this.Notification.find(queryConditions)
            .populate([
            {
                path: 'receiver',
                select: 'firstName lastName roles',
            },
            {
                path: 'sender',
                select: 'firstName lastName roles',
            },
        ])
            .sort({ createdAt: -1 })
            .limit(pageSize)
            .skip((pageNumber - 1) * pageSize);
        const numberOfNotifications = await this.Notification.countDocuments(queryConditions);
        const numberOfNotReadNotifications = await this.Notification.countDocuments({ ...queryConditions, isRead: false });
        const totalPages = Math.ceil(numberOfNotifications / pageSize);
        return {
            pageNumber,
            pageSize,
            totalPages,
            numberOfNotifications,
            numberOfNotReadNotifications,
            notifications,
        };
    }
    async update(notificationId, dataToUpdate) {
        const notification = await this.Notification.findById(notificationId);
        if (!notification)
            throw new http_exception_1.default(404, 'Notification not found');
        await this.Notification.findByIdAndUpdate(notification._id, dataToUpdate);
    }
    async markAllAsRead(currentUser) {
        const notReadNotifications = await this.Notification.find({ receiver: currentUser._id });
        for (const notification of notReadNotifications) {
            await this.Notification.findByIdAndUpdate(notification._id, { $set: { isRead: true } });
        }
    }
}
exports.NotificationService = NotificationService;
//# sourceMappingURL=notification.service.js.map