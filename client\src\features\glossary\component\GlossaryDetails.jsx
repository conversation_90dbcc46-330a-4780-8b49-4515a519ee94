"use client";
import { memo, useMemo } from "react";
import { Container, Grid } from "@mui/material";

import ArticleContent from "../../blog/components/ArticleContent";
import SvgArrowRight from "@/assets/images/icons/arrowRight.svg";
import { useRenderPerformance } from "@/hooks/usePerformanceMonitor";

const GlossaryDetails = memo(function GlossaryDetails({ article, language }) {
  // Monitor render performance in development
  useRenderPerformance(
    "GlossaryDetails",
    process.env.NODE_ENV === "development"
  );
  // Memoize the breadcrumb schema to avoid recalculation
  const breadcrumbSchema = useMemo(
    () => ({
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement: [
        {
          "@type": "ListItem",
          position: 1,
          item: {
            "@id":
              language === "en"
                ? `https://www.pentabell.com/glossaries/`
                : `https://www.pentabell.com/${language}/glossaries/`,
            name: "Glossary",
          },
        },
      ],
    }),
    [language]
  );

  // Memoize the glossary path URL
  const glossaryPath = useMemo(
    () => (language === "en" ? `/glossary` : `/${language}/glossary`),
    [language]
  );

  return (
    <div id="glossary-page-details">
      <div id="glossary-header">
        <Container className="custom-max-width">
          <div className="glossary-path">
            <a
              locale={language === "en" ? "en" : "fr"}
              href={`${glossaryPath}/`}
              className="link"
            >
              Glossary
            </a>
            {article?.word && (
              <>
                <SvgArrowRight />
                <p className="word">{article.word}</p>
              </>
            )}
          </div>
        </Container>
      </div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbSchema),
        }}
      />
      <Container className="custom-max-width">
        <Grid className="container" container columnSpacing={2}>
          <Grid item xs={12} sm={8}>
            <div className="glossary-content">
              <ArticleContent htmlContent={article?.content} />
            </div>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
});

export default GlossaryDetails;
