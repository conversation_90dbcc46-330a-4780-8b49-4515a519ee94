"use client";
import moment from "moment";
import { Container, Grid } from "@mui/material";
import { useTheme, useMediaQuery } from "@mui/material";
import "moment/locale/fr";
import { useTranslation } from "react-i18next";

import ArticleContent from "../../blog/components/ArticleContent";
import SvgArrowRight from "@/assets/images/icons/arrowRight.svg";

export default function GlossaryDetails({ id, article, language, url }) {
  const { t } = useTranslation();

  const theme = useTheme();
  const { i18n } = useTranslation();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  moment.locale(i18n.language || "en");

  return (
    <>
      <div id="glossary-page-details">
        <div id="glossary-header">
          <Container className="custom-max-width">
            <div className="glossary-path">
              <a
                locale={language === "en" ? "en" : "fr"}
                href={`${
                  language === "en" ? `/glossary` : `/${language}/glossary`
                }/`}
                className="link"
              >
                Glossary
              </a>
              {article?.word && (
                <>
                  <SvgArrowRight />
                  <p className="word">{article?.word}</p>
                </>
              )}
            </div>
          </Container>
        </div>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "BreadcrumbList",
              itemListElement: [
                {
                  "@type": "ListItem",
                  position: 1,
                  item: {
                    "@id":
                      language === "en"
                        ? `https://www.pentabell.com/glossaries/`
                        : `https://www.pentabell.com/${language}/glossaries/`,
                    name: "Glossary",
                  },
                },
              ],
            }),
          }}
        />
        <Container className="custom-max-width">
          <Grid className="container" container columnSpacing={2}>
            <Grid item xs={12} sm={8}>
              <div className="glossary-content">
                <ArticleContent htmlContent={article?.content} />
              </div>
            </Grid>
          </Grid>
          {/* <Grid className="container" container columnSpacing={2}>
          </Grid> */}
        </Container>
      </div>
    </>
  );
}
