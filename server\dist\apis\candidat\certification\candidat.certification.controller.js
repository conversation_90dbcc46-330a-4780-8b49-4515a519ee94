"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const candidat_certification_service_1 = __importDefault(require("./candidat.certification.service"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class CandidateCertificationController {
    constructor() {
        this.path = '/candidates/certifications';
        this.router = (0, express_1.Router)();
        this.candidateCertificationService = new candidat_certification_service_1.default();
        this.addCertificationOfCandidate = async (request, response, next) => {
            try {
                const id = request.user._id;
                const certification = request.body;
                const result = await this.candidateCertificationService.add(id, certification);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.updateCertificationsOfCandidate = async (request, response, next) => {
            try {
                const id = request.user._id;
                const certificationId = request.params.certificationId;
                const certification = request.body;
                await this.candidateCertificationService.update(id, certificationId, certification);
                response.send({ message: 'Certification updated successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.affichageCertificationsCandidat = async (request, response, next) => {
            try {
                const candidateId = request.user._id;
                const certifications = await this.candidateCertificationService.getAll(candidateId);
                response.send(certifications);
            }
            catch (error) {
                next(error);
            }
        };
        this.delelteCertificationsOfCandidate = async (request, response, next) => {
            try {
                const id = request.user._id;
                const certificationId = request.params.certificationId;
                await this.candidateCertificationService.delete(id, certificationId);
                response.send({ message: "Candidate's certification deleted successfully" });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), cache_middleware_1.invalidateCache, this.addCertificationOfCandidate);
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), 
        // validateCache,
        this.affichageCertificationsCandidat);
        this.router.put(`${this.path}/:certificationId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), cache_middleware_1.invalidateCache, this.updateCertificationsOfCandidate);
        this.router.delete(`${this.path}/:certificationId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), cache_middleware_1.invalidateCache, this.delelteCertificationsOfCandidate);
    }
}
exports.default = CandidateCertificationController;
//# sourceMappingURL=candidat.certification.controller.js.map