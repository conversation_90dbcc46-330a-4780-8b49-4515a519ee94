"use client";
import { useEffect, useState, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { Container, Grid } from "@mui/material";
import { useMediaQuery, useTheme } from "@mui/material";

import { getOpportunities } from "@/features/opportunity/services/opportunity.services";
import useOpportunityFilters from "@/features/opportunity/hooks/useOpportunityFilters";
import {
  SearchBar,
  FilterChips,
  OpportunityList,
  FilterSidebar,
} from "./OpportunityComponents";
import IconGrid from "@/assets/images/icons/GridIcon.svg";
import IconList from "@/assets/images/icons/ListIcon.svg";
import CustomButton from "@/components/ui/CustomButton";

export default function OpportunityCard({
  language,
  initialOpportunities,
  searchParams,
  countries,
  typeCategory,
  jobIndustry,
  countryName,
  industryName,
  jobLocation,
  initialListView,
}) {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [opportunitiesData, setOpportunitiesData] = useState(
    initialOpportunities || null
  );
  const [isLoading, setIsLoading] = useState(false);

  const totalOpportunities = opportunitiesData?.totalOpportunities;

  const fetchOpportunities = useCallback(
    async (params) => {
      try {
        setIsLoading(true);
        const requestParams = {
          language: language,
          pageSize: 10,
          pageNumber: params.pageNumber || 1,
          visibility: "Public",
          keyWord: params.keyWord || "",
          levelOfExperience: params.levelOfExperience || "",
          contractType: params.contractType || "",
          jobDescriptionLanguages: params.jobDescriptionLanguages || "",
          opportunityType: params.opportunityType || "",
        };

        if (jobIndustry && industryName) {
          requestParams.industry = industryName;
        } else if (params.industry) {
          requestParams.industry = params.industry.replace("IT", "It");
        } else {
          requestParams.industry = "";
        }

        if (jobLocation && countryName) {
          requestParams.country = countryName;
        } else if (params.country) {
          requestParams.country = params.country;
        } else {
          requestParams.country = "";
        }

        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Request timeout")), 10000)
        );

        const response = await Promise.race([
          getOpportunities(requestParams),
          timeoutPromise,
        ]);

        setOpportunitiesData(response);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching opportunities:", error);
        setIsLoading(false);
      }
    },
    [language, jobIndustry, industryName, jobLocation, countryName]
  );

  const {
    keyWord,
    setKeyWord,
    country,
    setCountry,
    pageNumber,
    setPageNumber,
    setIndustry,
    setContractType,
    setLevelOfExperience,
    setJobDescriptionLanguages,
    selectedFilters,
    setSelectedFilters,
    isFilterOpen,
    setIsFilterOpen,
    handleSearchChange,
    handleSearchClick,
    resetSearch,
    handlePageChange,
    searchParamsContent,
    isList,
    setIsList,
    handleViewModeChange,
  } = useOpportunityFilters(fetchOpportunities, language, initialListView);

  useEffect(() => {
    const params = {};
    if (searchParams?.pageNumber) params.pageNumber = searchParams.pageNumber;
    if (searchParams?.country) params.country = searchParams.country;
    if (searchParams?.keyWord) params.keyWord = searchParams.keyWord;
    if (searchParams?.levelOfExperience)
      params.levelOfExperience = searchParams.levelOfExperience;
    if (searchParams?.industry) params.industry = searchParams.industry;
    if (searchParams?.contractType)
      params.contractType = searchParams.contractType;
    if (searchParams?.jobDescriptionLanguages)
      params.jobDescriptionLanguages = searchParams.jobDescriptionLanguages;
    if (searchParams?.isList) params.isList = searchParams.isList;

    if (
      !initialOpportunities ||
      (Object.keys(params).length > 0 &&
        !searchParams?.pageNumber &&
        !searchParams?.isList)
    ) {
      fetchOpportunities(params);
    }
  }, [fetchOpportunities, searchParams, initialOpportunities]);

  useEffect(() => {
    const handleFilterChange = (event) => {
      const params = event.detail.params;
      const maintainScroll = event.detail.maintainScroll;
      const scrollPosition = event.detail.scrollPosition;

      const currentScrollPosition =
        scrollPosition || window.scrollY || document.documentElement.scrollTop;

      let newFilters = [];
      const filterCategories = [
        { param: "industry", category: "industry" },
        { param: "contractType", category: "contractType" },
        { param: "levelOfExperience", category: "levelOfExperience" },
        {
          param: "jobDescriptionLanguages",
          category: "jobDescriptionLanguages",
        },
        { param: "country", category: "country" },
        { param: "keyWord", category: "keyWord" },
      ];

      filterCategories.forEach(({ param, category }) => {
        if (params[param]) {
          const values = params[param].split(",");
          newFilters = [
            ...newFilters,
            ...values.map((value) => ({ category, label: value.trim() })),
          ];
        }
      });

      if (params.hasOwnProperty("list")) {
        setIsList(params.list === "Yes");
      }

      setSelectedFilters(newFilters);

      params.pageSize = 10;

      fetchOpportunities(params);

      if (maintainScroll) {
        window.scrollTo({
          top: currentScrollPosition,
          behavior: "instant",
        });
      }
    };

    const handleCheckboxFilterChange = (event) => {
      const { category, newValues } = event.detail;

      if (category === "keyWord") {
        setKeyWord(
          Array.isArray(newValues) && newValues.length > 0
            ? newValues.join(",")
            : ""
        );
      } else if (category === "country") {
        setCountry(
          Array.isArray(newValues) && newValues.length > 0
            ? newValues.join(",")
            : ""
        );
      }
    };

    window.addEventListener("filterChanged", handleFilterChange);
    window.addEventListener(
      "checkboxFilterChanged",
      handleCheckboxFilterChange
    );

    return () => {
      window.removeEventListener("filterChanged", handleFilterChange);
      window.removeEventListener(
        "checkboxFilterChanged",
        handleCheckboxFilterChange
      );
    };
  }, [fetchOpportunities, setSelectedFilters, setKeyWord, setCountry]);

  const initialValues = {
    industry:
      searchParams?.industry &&
      searchParams.industry.split(",").map((item) => item.trim()).length > 0
        ? decodeURIComponent(searchParams?.industry).split(",")
        : "",
    contractType:
      searchParams?.contractType &&
      searchParams.contractType.split(",").map((item) => item.trim()).length > 0
        ? decodeURIComponent(searchParams?.contractType).split(",")
        : "",
    jobDescriptionLanguages:
      searchParams?.jobDescriptionLanguages &&
      searchParams.jobDescriptionLanguages.split(",").map((item) => item.trim())
        .length > 0
        ? decodeURIComponent(searchParams?.jobDescriptionLanguages).split(",")
        : "",
    levelOfExperience:
      searchParams?.levelOfExperience &&
      searchParams.levelOfExperience.split(",").map((item) => item.trim())
        .length > 0
        ? decodeURIComponent(searchParams?.levelOfExperience).split(",")
        : "",
    keyWord:
      searchParams?.keyWord &&
      searchParams.keyWord.split(",").map((item) => item.trim()).length > 0
        ? decodeURIComponent(searchParams?.keyWord).split(",")
        : "",
    country:
      searchParams?.country &&
      searchParams.country.split(",").map((item) => item.trim()).length > 0
        ? decodeURIComponent(searchParams?.country).split(",")
        : "",
  };

  const handleSubmitFilter = (values, { setFieldValue }) => {
    const newParams = new URLSearchParams(searchParamsContent);
    if (values.industry?.length) {
      newParams.set("industry", values.industry.join(","));
      setIndustry(values.industry.join(","));
    }
    if (values.contractType?.length) {
      newParams.set("contractType", values.contractType.join(","));
      setContractType(values.contractType.join(","));
    }
    if (values.levelOfExperience?.length) {
      newParams.set("levelOfExperience", values.levelOfExperience.join(","));
      setLevelOfExperience(values.levelOfExperience.join(","));
    }
    if (values.jobDescriptionLanguages?.length) {
      newParams.set(
        "jobDescriptionLanguages",
        values.jobDescriptionLanguages.join(",")
      );
      setJobDescriptionLanguages(values.jobDescriptionLanguages.join(","));
    }
    if (values.keyWord?.length) {
      newParams.set("keyWord", values.keyWord.join(","));
      setKeyWord(values.keyWord.join(","));
    }
    if (values.country) {
      newParams.set("country", values.country);
      setCountry(values.country);
    }

    const scrollPosition = window.scrollY || document.documentElement.scrollTop;
    const newUrl = `${window.location.pathname}?${newParams.toString()}`;
    window.history.replaceState({ path: newUrl }, "", newUrl);

    const params = {};
    for (const [key, value] of newParams.entries()) {
      params[key] = value;
    }

    fetchOpportunities(params);

    let filtersArray = [];
    if (values.industry?.length) {
      filtersArray.push(
        ...values.industry.map((item) => ({
          category: "industry",
          label: item,
        }))
      );
    }
    if (values.contractType?.length) {
      filtersArray.push(
        ...values.contractType.map((item) => ({
          category: "contractType",
          label: item,
        }))
      );
    }
    if (values.levelOfExperience?.length) {
      filtersArray.push(
        ...values.levelOfExperience.map((item) => ({
          category: "levelOfExperience",
          label: item,
        }))
      );
    }
    if (values.jobDescriptionLanguages?.length) {
      filtersArray.push(
        ...values.jobDescriptionLanguages.map((item) => ({
          category: "jobDescriptionLanguages",
          label: item,
        }))
      );
    }
    if (values.country) {
      filtersArray.push(
        ...values.country.map((item) => ({ category: "country", label: item }))
      );
    }
    if (values.keyWord) {
      filtersArray.push(
        ...values.keyWord.map((item) => ({ category: "keyWord", label: item }))
      );
    }

    setSelectedFilters(filtersArray);
    setFieldValue(
      "filters",
      filtersArray.map((f) => f.label)
    );

    window.scrollTo({
      top: scrollPosition,
      behavior: "instant",
    });
  };

  return (
    <>
      {typeCategory && (
        <div id="search-bar-opportunities">
          <Container className="custom-max-width">
            <p className="sub-heading text-banking">{t("global:findCareer")}</p>
            {!isMobile && (
              <SearchBar
                keyWord={keyWord}
                country={country}
                countries={countries}
                handleSearchChange={handleSearchChange}
                setCountry={setCountry}
                resetSearch={(e) =>
                  resetSearch(e, {
                    preserveIndustry: jobIndustry,
                    industryName: industryName,
                    preserveCountry: jobLocation,
                    countryName: countryName,
                  })
                }
                handleSearchClick={handleSearchClick}
                setPageNumber={setPageNumber}
                jobLocation={jobLocation}
                jobIndustry={jobIndustry}
                industryName={industryName}
                countryName={countryName}
                t={t}
              />
            )}
            <FilterChips selectedFilters={selectedFilters} />
          </Container>
        </div>
      )}

      <div id="opportunities">
        <Container className="custom-max-width">
          <div className="display">
            <div className="opportunity-chip">
              <p className="sub-heading text-banking">
                Opportunities{" "}
                <span className="opportunities-nbr">{totalOpportunities}</span>
              </p>{" "}
            </div>
            {!isMobile && (
              <div className="grid-list-buttons">
                <CustomButton
                  icon={<IconGrid />}
                  className={`btn btn-ghost ${!isList ? "active" : ""}`}
                  onClick={() => handleViewModeChange(false)}
                />
                <CustomButton
                  icon={<IconList />}
                  className={`btn btn-ghost ${isList ? "active" : ""}`}
                  onClick={() => handleViewModeChange(true)}
                />
              </div>
            )}
          </div>
          <Grid
            className="container opportunity-card"
            container
            columnSpacing={2}
          >
            <FilterSidebar
              initialValues={initialValues}
              isFilterOpen={isFilterOpen}
              setIsFilterOpen={setIsFilterOpen}
              t={t}
              jobLocation={jobLocation}
              jobIndustry={jobIndustry}
              countries={countries}
              setSelectedFilters={setSelectedFilters}
              setPageNumber={setPageNumber}
              handleSubmitFilter={handleSubmitFilter}
            />
            <OpportunityList
              opportunitiesData={opportunitiesData}
              language={language}
              pageNumber={pageNumber}
              handlePageChange={handlePageChange}
              searchParamsContent={searchParamsContent}
              t={t}
              isList={isList}
              isLoading={isLoading}
            />
          </Grid>
        </Container>
      </div>
    </>
  );
}
