"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _PentabellCompanySection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PentabellCompanySection */ \"(app-pages-browser)/./src/features/glossary/component/PentabellCompanySection.jsx\");\n/* harmony import */ var _GlossaryHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./GlossaryHeader */ \"(app-pages-browser)/./src/features/glossary/component/GlossaryHeader.jsx\");\n/* harmony import */ var _GlossarySocialMediaIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./GlossarySocialMediaIcon */ \"(app-pages-browser)/./src/features/glossary/component/GlossarySocialMediaIcon.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst GlossaryDetails = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function GlossaryDetails(param) {\n    let { glossary, language, isMobileSSR } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(theme.breakpoints.down(\"sm\")) || isMobileSSR;\n    const isTablet = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(theme.breakpoints.down(\"md\")) || isMobileSSR;\n    const breadcrumbSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"BreadcrumbList\",\n            itemListElement: [\n                {\n                    \"@type\": \"ListItem\",\n                    position: 1,\n                    item: {\n                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                        name: \"Glossary\"\n                    }\n                }\n            ]\n        }), [\n        language\n    ]);\n    const glossaryPath = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>language === \"en\" ? \"/glossaries\" : \"/\".concat(language, \"/glossaries\"), [\n        language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page-details\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(breadcrumbSchema)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"container\",\n                container: true,\n                columnSpacing: 2,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        sm: 12,\n                        md: 12,\n                        lg: 9,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlossaryHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                language: language,\n                                glossaryPath: glossaryPath,\n                                glossary: glossary\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"custom-max-width\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        htmlContent: glossary === null || glossary === void 0 ? void 0 : glossary.content\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PentabellCompanySection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        locale: locale\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    isMobile && isTablet && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlossarySocialMediaIcon__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 38\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    !(isMobile && isTablet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        sm: 12,\n                        md: 12,\n                        lg: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"custom-max-width\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlossarySocialMediaIcon__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}, \"b0xrQkBwMRtDSXZDGNM7QmBe+QA=\", false, function() {\n    return [\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n})), \"b0xrQkBwMRtDSXZDGNM7QmBe+QA=\", false, function() {\n    return [\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n});\n_c1 = GlossaryDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryDetails);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlossaryDetails$memo\");\n$RefreshReg$(_c1, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});