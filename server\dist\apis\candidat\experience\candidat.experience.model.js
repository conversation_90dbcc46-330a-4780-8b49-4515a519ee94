"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const experienceSchema = new mongoose_1.Schema({
    company: String,
    contractType: String,
    duration: String,
    endDate: Date,
    location: String,
    startDate: Date,
    title: String,
    jobDescription: String,
    currentJob: { type: Boolean, default: false }
});
exports.default = (0, mongoose_1.model)('Experience', experienceSchema);
//# sourceMappingURL=candidat.experience.model.js.map