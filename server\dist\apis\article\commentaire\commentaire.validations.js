"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticatedResponseSchema = exports.unauthenticatedResponseSchema = exports.unauthenticatedCommentSchema = exports.authenticatedCommentSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const authenticatedCommentSchema = joi_1.default.object({
    comment: joi_1.default.string().min(1).required(),
    email: joi_1.default.forbidden(),
    firstName: joi_1.default.forbidden()
}).options({ abortEarly: false });
exports.authenticatedCommentSchema = authenticatedCommentSchema;
const unauthenticatedCommentSchema = joi_1.default.object({
    comment: joi_1.default.string().min(1).required(),
    email: joi_1.default.string().email().required(),
    firstName: joi_1.default.string().min(1).required()
}).options({ abortEarly: false });
exports.unauthenticatedCommentSchema = unauthenticatedCommentSchema;
const authenticatedResponseSchema = joi_1.default.object({
    text: joi_1.default.string().min(1).required(),
    email: joi_1.default.forbidden(),
    firstName: joi_1.default.forbidden()
}).options({ abortEarly: false });
exports.authenticatedResponseSchema = authenticatedResponseSchema;
const unauthenticatedResponseSchema = joi_1.default.object({
    text: joi_1.default.string().min(1).required(),
    email: joi_1.default.string().email().required(),
    firstName: joi_1.default.string().min(1).required()
}).options({ abortEarly: false });
exports.unauthenticatedResponseSchema = unauthenticatedResponseSchema;
//# sourceMappingURL=commentaire.validations.js.map