"use client";;
import useEmblaCarousel from "embla-carousel-react";
import Autoplay from "embla-carousel-autoplay";
import { Container, Grid, useMediaQuery, useTheme } from "@mui/material";
import union from "@/assets/images/website/coporate-profile/union.png";
import user from "@/assets/images/website/coporate-profile/user.png";
import { coporateProfileTestimonials } from "@/utils/constants";
import {
  NextButton,
  PrevButton,
  usePrevNextButtons,
} from "../ui/emblaCarousel/EmblaCarouselArrowButtons";

import ArrowLeft from "@/assets/images/icons/arrow-left.svg";
import ArrowRight from "@/assets/images/icons/arrow-right.svg";

function Testimonials() {
  const options = { loop: true };
  const theme = useTheme();

  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [emblaRef, emblaApi] = useEmblaCarousel(options, [
    Autoplay({ playOnInit: !isMobile, delay: 3500 }),
  ]);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);
  return (
    <section id="coporate-profile-testimonials" className="embla">
      <Container>
      <p className="heading-h2 text-yellow text-banner semi-bold text-center">
      What they say
            </p>
      <div className="embla__viewport" ref={emblaRef}>
        <div className="embla__container">
          {coporateProfileTestimonials.map((item, index) => (
            <Container className="embla__slide" key={index}>
              <Grid container className="slide__container" columnSpacing={2}>
                <Grid item xs={1} sm={1}>
                  <img
                    width={"100%"}
                    height={"auto"}
                    alt={"altImg"}
                    src={union.src}
                    loading="lazy"
                    className="union-img"
                  />
                </Grid>
                <Grid item xs={10} sm={10}>
                  <p className="sub-heading text-white semi-bold mb-0">
                    {item.description}
                  </p>
                </Grid>
                <Grid
                  item
                  xs={1}
                  sm={1}
                  alignSelf={"flex-end"}
                  sx={{ textAlign: "right" }}
                >
                  <img
                    className="union-img union-img-rotate"
                    width={"100%"}
                    height={"auto"}
                    alt={"altImg"}
                    src={union.src}
                    loading="lazy"
                  />
                </Grid>

                <Grid item xs={12} sm={12} className="user-info">
                  <img
                    className="user-picture"
                    width={"64px"}
                    height={"64px"}
                    alt={"altImg"}
                    src={user.src}
                    loading="lazy"
                  />
                  <p className="sub-heading text-white semi-bold ml-2">
                    {item.author}
                  </p>
                </Grid>
              </Grid>
            </Container>
          ))}
        </div>
        {!!coporateProfileTestimonials.length && (
          <Container className="embla__controls">
            <div className="embla__buttons">
              <PrevButton
                children={<ArrowLeft />}
                onClick={onPrevButtonClick}
                disabled={prevBtnDisabled}
              />
              <NextButton
                children={<ArrowRight />}
                onClick={onNextButtonClick}
                disabled={nextBtnDisabled}
              />
            </div>
          </Container>
        )}
      </div>

      </Container>
    </section>
  );
}

export default Testimonials;
