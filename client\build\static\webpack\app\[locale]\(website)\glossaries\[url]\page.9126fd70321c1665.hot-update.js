"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst GlossaryDetails = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function GlossaryDetails(param) {\n    let { article, language } = param;\n    _s();\n    const breadcrumbSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"BreadcrumbList\",\n            itemListElement: [\n                {\n                    \"@type\": \"ListItem\",\n                    position: 1,\n                    item: {\n                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                        name: \"Glossary\"\n                    }\n                }\n            ]\n        }), [\n        language\n    ]);\n    // Memoize the glossary path URL\n    const glossaryPath = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>language === \"en\" ? \"/glossary\" : \"/\".concat(language, \"/glossary\"), [\n        language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page-details\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"glossary-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"custom-max-width\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glossary-path\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                locale: language === \"en\" ? \"en\" : \"fr\",\n                                href: \"\".concat(glossaryPath, \"/\"),\n                                className: \"link\",\n                                children: \"Glossary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this),\n                            (article === null || article === void 0 ? void 0 : article.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"word\",\n                                        children: article.word\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(breadcrumbSchema)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"custom-max-width\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"container\",\n                    container: true,\n                    columnSpacing: 2,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 8,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"glossary-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                htmlContent: article === null || article === void 0 ? void 0 : article.content\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 67,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}, \"VzwIqTxcUnk2PZ6sQQGwhDIlTdM=\")), \"VzwIqTxcUnk2PZ6sQQGwhDIlTdM=\");\n_c1 = GlossaryDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryDetails);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlossaryDetails$memo\");\n$RefreshReg$(_c1, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});