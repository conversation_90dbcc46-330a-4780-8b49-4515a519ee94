"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("../../../utils/exceptions/http.exception"));
const opportunity_application_model_1 = __importDefault(require("../model/opportunity.application.model"));
const services_1 = require("@/utils/services");
const fs = __importStar(require("fs"));
const path_1 = __importDefault(require("path"));
const axios_1 = __importDefault(require("axios"));
const form_data_1 = __importDefault(require("form-data"));
const constants_1 = require("../../../utils/helpers/constants");
const opportunity_model_1 = __importDefault(require("../../opportunity/model/opportunity.model"));
const user_model_1 = __importDefault(require("@/apis/user/user.model"));
const candidat_model_1 = __importDefault(require("../../candidat/candidat.model"));
const files_model_1 = __importDefault(require("../../storage/files.model"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const messages_1 = require("@/utils/helpers/messages");
const settings_model_1 = __importDefault(require("@/apis/settings/settings.model"));
const files_service_1 = __importDefault(require("@/apis/storage/files.service"));
const functions_1 = require("@/utils/helpers/functions");
const mongoose_1 = __importDefault(require("mongoose"));
const alert_model_1 = __importDefault(require("@/apis/alert/alert.model"));
const socket_1 = require("@/utils/config/socket");
class ApplicationService {
    constructor() {
        this.Application = opportunity_application_model_1.default;
        this.Opportunity = opportunity_model_1.default;
        this.Candidate = candidat_model_1.default;
        this.File = files_model_1.default;
        this.User = user_model_1.default;
        this.filesService = new files_service_1.default();
    }
    async applyForJob(applyData, opportunityId, currentUser) {
        const { firstName, lastName, email, resume, country, industry, password, phone, note } = applyData;
        if (!resume)
            throw new http_exception_1.default(400, 'Resume is required!');
        let user;
        if (currentUser)
            user = await user_model_1.default.findById(currentUser._id);
        else {
            const existedUser = await user_model_1.default.findOne({ email });
            if (existedUser)
                throw new http_exception_1.default(409, 'User already exists, to apply please login !');
        }
        let candidate;
        if (user) {
            candidate = await this.Candidate.findOne({ user: user._id });
            if (!candidate)
                throw new http_exception_1.default(404, 'Candidate not found');
            const existingApplication = await opportunity_application_model_1.default.findOne({ candidate: user.candidate, opportunity: opportunityId }).lean();
            if (existingApplication)
                throw new http_exception_1.default(409, 'You have already applied for this opportunity.');
            const opportunity = await this.Opportunity.findById(opportunityId);
            const opportunityTitle = opportunity?.versions?.get('en')?.title ?? opportunity?.versions?.get('fr')?.title;
            (0, services_1.sendEmail)({
                to: user.email,
                subject: `Application for ${opportunityTitle} is submitted`,
                template: 'applyForOpportunity',
                context: {
                    firstName: user.firstName,
                    OpportunityTitle: opportunityTitle,
                },
            });
            await this.Opportunity.findByIdAndUpdate(opportunityId, { $push: { applicants: candidate._id } });
        }
        else {
            const hashedPassword = await services_1.auth.hashPassword(password);
            user = await this.User.create({
                firstName,
                lastName,
                email,
                country,
                industry,
                password: hashedPassword,
                phone,
                roles: ['Candidate'],
            });
            const defaultUserSettings = {
                user: user._id,
                notifications: {
                    newJobAlerts: {
                        email: true,
                        website: true,
                    },
                    appliedJobStatusUpdates: {
                        email: true,
                        website: true,
                    },
                    newsLetter: {
                        email: true,
                        website: true,
                    },
                },
            };
            await settings_model_1.default.create(defaultUserSettings);
            const alertData = {
                isActive: true,
            };
            const createdAlert = await alert_model_1.default.create({
                ...alertData,
                createdBy: user._id,
            });
            await user_model_1.default.findByIdAndUpdate(user._id, { alerts: createdAlert._id });
            const notificationMessage = 'Welcome to Pentabell, we are happy to see you here!';
            await (0, socket_1.sendNotification)({
                receiver: user._id,
                sender: null,
                message: notificationMessage,
                link: `${process.env.LOGIN_LINK}`,
            });
            const token = jsonwebtoken_1.default.sign({ userId: user._id }, process.env.Acc_Creation, { expiresIn: '24h' });
            user.confirmAccountToken = token;
            await user.save();
            candidate = await new candidat_model_1.default({
                user: user._id,
                emails: [email],
                cv: [
                    {
                        fileName: resume,
                        originalName: `CV_${user.firstName}_${user.lastName}`,
                        publishDate: new Date(),
                    },
                ],
                country,
                industry,
                phones: [phone],
                industries: [industry],
                profilePercentage: 20,
            }).save();
            await user_model_1.default.findByIdAndUpdate(user._id, { candidate: candidate._id });
            const opportunity = await this.Opportunity.findById(opportunityId);
            const opportunityTitle = opportunity?.versions?.get('en')?.title ?? opportunity?.versions?.get('fr')?.title;
            const activationLink = `${process.env.LOGIN_LINK}/activation?token=${token}`;
            (0, services_1.sendEmail)({
                to: email,
                subject: `Application for ${opportunityTitle} is submitted`,
                template: 'applyNotConnected',
                context: {
                    firstName: user.firstName,
                    lastName: user.lastName,
                    activationLink: activationLink,
                    email: user.email,
                    password: password,
                    OpportunityTitle: opportunityTitle,
                },
            });
            await this.Opportunity.findByIdAndUpdate(opportunityId, { $push: { applicants: candidate._id } });
        }
        const newApplication = new opportunity_application_model_1.default({
            candidate: candidate._id,
            opportunity: opportunityId,
            resume,
            note,
            applicationDate: new Date().toISOString(),
        });
        await newApplication.save();
        await this.sendApplicationToHunter(newApplication, user, opportunityId);
        return { message: 'Application submitted successfully.' };
    }
    async sendApplicationToHunter(applicationData, currentUser, id) {
        const details = {
            applicationDate: (0, functions_1.formatDate)(new Date()),
            candidate: currentUser._id,
            opportunity: id,
        };
        const filePath = await this.filesService.findFile(applicationData.resume);
        const fileExtension = path_1.default.extname(filePath).toLowerCase();
        let contentType = 'application/octet-stream';
        switch (fileExtension) {
            case '.pdf':
                contentType = 'application/pdf';
                break;
            case '.doc':
                contentType = 'application/msword';
                break;
            case '.docx':
                contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                break;
        }
        const candidateData = await this.User.findById(currentUser._id);
        const candidateInfos = await this.Candidate.find({ user: currentUser._id })
            .populate('certifications')
            .populate('educations')
            .populate('experiences');
        const fileData = await this.File.findOne({ fileName: applicationData.resume });
        const checksum = fileData?.checksum;
        const opportunity = await this.Opportunity.findByIdAndUpdate(details.opportunity, { $push: { applicants: details.candidate } });
        const reference = opportunity?.reference;
        if (process.env.NODE_ENV !== 'dev') {
            try {
                const formData = new form_data_1.default();
                formData.append('file', fs.readFileSync(filePath), {
                    filename: path_1.default.basename(filePath),
                    contentType,
                });
                const jsonData = JSON.stringify({
                    dateCreation: (0, functions_1.formatDate)(new Date()),
                    emails: candidateInfos[0].emails,
                    fileName: applicationData.resume,
                    flatText: await (0, functions_1.extractTextFromFile)(contentType, fs.readFileSync(filePath)),
                    state: 'Flat',
                    industry: 'OTHERS',
                    industries: ['OTHERS'],
                    industriesBinary: candidateInfos[0].industriesBinary,
                    jobTitleAng: candidateInfos[0].jobTitleAng,
                    location: candidateData?.country,
                    monthsOfExperiences: candidateInfos[0].monthsOfExperiences,
                    name: candidateData?.firstName + ' ' + candidateData?.lastName,
                    nationalities: candidateInfos[0].nationalities,
                    numberOfCertifications: candidateInfos[0].numberOfCertifications,
                    numberOfEducations: candidateInfos[0].numberOfEducations,
                    numberOfExperiences: candidateInfos[0].numberOfExperiences,
                    numberOfSkills: candidateInfos[0].numberOfSkills,
                    certifications: candidateInfos[0].certifications,
                    experiences: candidateInfos[0].experiences,
                    educations: candidateInfos[0].educations,
                    experiencesCompany: candidateInfos[0].experiencesCompany,
                    phones: candidateInfos[0].phones,
                    skills: candidateInfos[0].skills,
                    checksum: checksum,
                });
                formData.append('body', jsonData, { filename: 'data.json', contentType: 'application/json' });
                await axios_1.default.post(`${process.env.HUNTER_BASE_URL}/openings/${reference}/pentabell`, formData, {
                    headers: {
                        ...formData.getHeaders(),
                        Cookie: `accessToken=${process.env.HUNTER_ACCESS_TOKEN}; refreshToken=${process.env.HUNTER_REFRESH_TOKEN}`,
                    },
                });
            }
            catch (error) {
                if (axios_1.default.isAxiosError(error)) {
                    if (error.response) {
                        throw new http_exception_1.default(Number(error.response.data.status), error.response.data.message);
                    }
                }
                else {
                    console.error('Unexpected error:', error);
                }
            }
        }
    }
    async get(applicationId) {
        const application = await this.Application.findById(applicationId)
            .populate([
            {
                path: 'opportunity',
                select: 'versions',
            },
            {
                path: 'candidate',
                select: 'user',
                populate: {
                    path: 'user',
                    select: 'firstName lastName email phone country jobTitle',
                },
            },
        ])
            .lean();
        if (!application)
            throw new http_exception_1.default(404, messages_1.MESSAGES.APPLICATION.NOT_FOUND);
        return application;
    }
    async getAllApplicationsByCandidat(queries, candidateId) {
        const { jobTitle, industry, status, paginated, createdAt, opportunity, sortOrder, isActive } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 10;
        if (!candidateId) {
            throw new http_exception_1.default(400, 'Candidate ID is required');
        }
        const candidate = await this.Candidate.findById(candidateId);
        if (!candidate) {
            throw new http_exception_1.default(404, 'Candidate not found');
        }
        const query = { candidate: candidate._id };
        if (status) {
            query.status = status;
        }
        if (createdAt) {
            const startDate = new Date(createdAt);
            const nextYear = startDate.getFullYear() + 1;
            const endDate = new Date(nextYear, 0, 0, 23, 59, 59, 999);
            query.createdAt = {
                $gte: startDate.toISOString(),
                $lte: endDate.toISOString(),
            };
        }
        if (opportunity) {
            query.opportunity = opportunity;
        }
        if (jobTitle || industry || isActive) {
            const filter = {};
            if (jobTitle) {
                const jobTitleConditions = constants_1.languages.map(lang => ({
                    [`versions.${lang}.title`]: new RegExp(`.*${jobTitle}.*`, 'i'),
                }));
                filter['$or'] = jobTitleConditions;
            }
            if (industry) {
                filter.industry = industry;
            }
            if (isActive !== undefined) {
                const dateNow = new Date();
                if (isActive === 'true') {
                    filter['dateOfExpiration'] = { $gte: dateNow };
                }
                else if (isActive === 'false') {
                    filter['dateOfExpiration'] = { $lt: dateNow };
                }
            }
            const opportunities = await this.Opportunity.find(filter);
            query.opportunity = { $in: opportunities.map(opportunity => opportunity._id) };
        }
        if (paginated === 'false') {
            const applications = await this.Application.find(query)
                .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
                .lean();
            return applications;
        }
        const totalApplications = await this.Application.countDocuments(query);
        const totalPages = Math.ceil(totalApplications / pageSize);
        const applications = await this.Application.find(query)
            .populate([
            { path: 'opportunity', select: 'versions industry country dateOfExpiration status' },
            { path: 'candidate', select: 'firstName lastName email' },
        ])
            .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize)
            .lean();
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalApplications,
            applications,
        };
    }
    async getApplicationsStats(queries, currentUser) {
        const { status, startDate, endDate, opportunity, barChart = true, timeFrame } = queries;
        const matchConditions = {};
        const statuses = Object.values(constants_1.ApplicationStatus);
        const candidate = await this.Candidate.findOne({ user: currentUser._id });
        if (!candidate)
            throw new http_exception_1.default(404, 'Candidate not found');
        matchConditions['candidate'] = candidate._id;
        // Filtrage par timeFrame
        if (timeFrame) {
            let from;
            const today = new Date();
            switch (timeFrame) {
                case 'lastMonth':
                    from = new Date(today.setMonth(today.getMonth() - 1));
                    break;
                case 'last3Months':
                    from = new Date(today.setMonth(today.getMonth() - 3));
                    break;
                case 'last6Months':
                    from = new Date(today.setMonth(today.getMonth() - 6));
                    break;
                default:
                    throw new http_exception_1.default(400, `Invalid time frame '${timeFrame}'`);
            }
            matchConditions['createdAt'] = { $gte: from, $lte: new Date() };
        }
        // Filtrage par statut
        if (status) {
            if (!statuses.includes(status))
                throw new http_exception_1.default(400, `Invalid status '${status}'`);
            matchConditions['status'] = status;
        }
        // Filtrage par opportunité
        if (opportunity) {
            const foundOpening = await this.Opportunity.findById(opportunity);
            if (!foundOpening)
                throw new http_exception_1.default(404, `Opportunity with ID ${opportunity} not found!`);
            matchConditions['opportunity'] = foundOpening._id;
        }
        // Filtrage par dates
        if (startDate || endDate) {
            matchConditions['createdAt'] = {};
            if (startDate)
                matchConditions['createdAt'].$gte = typeof startDate === 'string' ? new Date(startDate) : startDate;
            if (endDate)
                matchConditions['createdAt'].$lte = typeof endDate === 'string' ? new Date(endDate) : endDate;
        }
        // Si barChart est true, regrouper par année et mois
        if (barChart) {
            return await this.Application.aggregate([
                { $match: matchConditions },
                {
                    $group: {
                        _id: {
                            year: { $year: '$createdAt' },
                            month: { $month: '$createdAt' },
                        },
                        applications: { $sum: 1 },
                    },
                },
                {
                    $sort: { '_id.year': 1, '_id.month': 1 },
                },
                {
                    $project: {
                        _id: 0,
                        year: '$_id.year',
                        month: '$_id.month',
                        applications: 1,
                    },
                },
            ]);
        }
        // Si barChart est false, regrouper par statut
        const applicationsStats = await this.Application.aggregate([
            {
                $match: matchConditions,
            },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    status: '$_id',
                    totalApplications: '$count',
                    _id: 0,
                },
            },
            {
                $sort: { totalApplications: -1 },
            },
        ]);
        // Mapper les statistiques par statut
        const statusMap = applicationsStats.reduce((acc, stat) => {
            acc[stat.status] = stat.totalApplications;
            return acc;
        }, {});
        // Construire le résultat final
        const applicationStatuses = ['Accepted', 'Rejected'];
        const result = applicationStatuses.map(status => ({
            status,
            totalApplications: statusMap[status] || 0,
        }));
        return result;
    }
    async getAll(queries, currentUser) {
        const { jobTitle, industry, status, paginated, createdAt, opportunity, sortOrder, isActive } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 10;
        const query = {};
        let expiredOpportunities = 0;
        let activeOpportunities = 0;
        let candidateNumberOfApp = 0;
        const candidate = await this.Candidate.findOne({ user: currentUser._id });
        if (!candidate)
            throw new http_exception_1.default(404, 'Candidate not found');
        query.candidate = candidate._id;
        const candidateApplications = await this.Application.find({ candidate: candidate._id }).select('opportunity').lean();
        candidateNumberOfApp = candidateApplications.length;
        const opportunityIds = candidateApplications.map(app => app.opportunity);
        expiredOpportunities = await this.Opportunity.countDocuments({
            _id: { $in: opportunityIds },
            status: 'Expired',
        });
        activeOpportunities = await this.Opportunity.countDocuments({
            _id: { $in: opportunityIds },
            status: 'Active',
        });
        if (opportunity) {
            query.opportunity = opportunity;
        }
        else if (jobTitle || industry || isActive) {
            const filter = {};
            if (jobTitle) {
                const visibilityConditions = constants_1.languages.map(lang => ({
                    [`versions.${lang}.title`]: RegExp(`.*${jobTitle}.*`, 'i'),
                }));
                filter['$or'] = visibilityConditions;
            }
            if (industry) {
                filter.industry = industry;
            }
            if (isActive !== undefined) {
                if (isActive === 'true') {
                    filter['dateOfExpiration'] = { $gte: (0, functions_1.formatDate)(new Date()) };
                }
                else if (isActive === 'false') {
                    filter['dateOfExpiration'] = { $lt: (0, functions_1.formatDate)(new Date()) };
                }
            }
            const opportunities = await this.Opportunity.find(filter);
            query.opportunity = { $in: opportunities.map(opportunity => opportunity._id) };
        }
        if (status) {
            query['status'] = status;
        }
        if (createdAt) {
            const date = new Date(createdAt);
            const nextYear = date.getFullYear() + 1;
            const antDate = new Date(nextYear, 0, 0, 24, 59, 59, 999);
            const isoDate = date.toISOString();
            const isoAntDate = antDate.toISOString();
            query['createdAt'] = {
                $gte: isoDate,
                $lte: isoAntDate,
            };
        }
        if (paginated && paginated === 'false') {
            const applications = await this.Application.find(query)
                .populate([
                { path: 'opportunity', select: 'versions industry country dateOfExpiration status' },
                { path: 'candidate', select: 'firstName lastName email' },
            ])
                .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
                .lean();
            return applications;
        }
        const totalApplications = await this.Application.countDocuments(query);
        const totalAccepted = await this.Application.countDocuments({ ...query, status: 'Accepted' });
        const totalRejected = await this.Application.countDocuments({ ...query, status: 'Rejected' });
        const totalPending = await this.Application.countDocuments({ ...query, status: 'Pending' });
        const totalPages = Math.ceil(totalApplications / pageSize);
        const applications = await this.Application.find(query, {
            status: 1,
            applicationDate: 1,
            createdAt: 1,
        })
            .populate([
            { path: 'opportunity', select: 'versions industry country dateOfExpiration status' },
            { path: 'candidate', select: 'firstName lastName email' },
        ])
            .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize || 10)
            .lean();
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalApplications,
            totalAccepted,
            totalRejected,
            totalPending,
            applications,
            expiredOpportunities,
            activeOpportunities,
            candidateNumberOfApp,
        };
    }
    async getAllApplications(queries) {
        const { jobTitle, industry, status, paginated, createdAt, opportunity, sortOrder, isActive, searchQuery, applicationDateFrom, applicationDateTo, } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 10;
        const query = {};
        let expiredOpportunities = 0;
        let activeOpportunities = 0;
        let candidateNumberOfApp = 0;
        const candidateApplications = await this.Application.find().select('opportunity candidate').lean();
        const opportunityIds = candidateApplications.map(app => app.opportunity);
        expiredOpportunities = await this.Opportunity.countDocuments({
            _id: { $in: opportunityIds },
            status: 'Expired',
        });
        activeOpportunities = await this.Opportunity.countDocuments({
            _id: { $in: opportunityIds },
            status: 'Active',
        });
        if (searchQuery) {
            query['$or'] = [
                { 'candidate.user.firstName': new RegExp(`.*${searchQuery}.*`, 'i') },
                { 'candidate.user.lastName': new RegExp(`.*${searchQuery}.*`, 'i') },
                { 'candidate.user.jobTitle': new RegExp(`.*${searchQuery}.*`, 'i') },
                { 'opportunity.versions.fr.title': new RegExp(`.*${searchQuery}.*`, 'i') },
                { 'opportunity.versions.en.title': new RegExp(`.*${searchQuery}.*`, 'i') },
                { note: new RegExp(`.*${searchQuery}.*`, 'i') },
            ];
        }
        if (opportunity) {
            query.opportunity = opportunity;
        }
        else if (jobTitle || industry || isActive) {
            const filter = {};
            if (jobTitle) {
                const visibilityConditions = constants_1.languages.map(lang => ({
                    [`versions.${lang}.title`]: jobTitle,
                }));
                filter['$or'] = visibilityConditions;
            }
            if (industry) {
                filter.industry = industry;
            }
            if (isActive !== undefined) {
                if (isActive === 'true') {
                    filter['dateOfExpiration'] = { $gte: (0, functions_1.formatDate)(new Date()) };
                }
                else if (isActive === 'false') {
                    filter['dateOfExpiration'] = { $lt: (0, functions_1.formatDate)(new Date()) };
                }
            }
            const opportunities = await this.Opportunity.find(filter);
            query.opportunity = { $in: opportunities.map(opportunity => opportunity._id) };
        }
        if (status) {
            query['status'] = status;
        }
        if (createdAt) {
            const date = new Date(createdAt);
            const nextYear = date.getFullYear() + 1;
            const antDate = new Date(nextYear, 0, 0, 24, 59, 59, 999);
            const isoDate = date.toISOString();
            const isoAntDate = antDate.toISOString();
            query['createdAt'] = {
                $gte: isoDate,
                $lte: isoAntDate,
            };
        }
        if (applicationDateFrom && applicationDateTo) {
            const from = new Date(applicationDateFrom).toISOString();
            const to = new Date(applicationDateTo).toISOString();
            query['applicationDate'] = { $gte: from, $lte: to };
        }
        if (paginated && paginated === 'false') {
            const applications = await this.Application.find(query).populate([
                {
                    path: 'opportunity',
                    select: 'versions industry country dateOfExpiration status',
                },
                {
                    path: 'candidate',
                    select: 'user',
                    populate: {
                        path: 'user',
                        select: 'firstName lastName email phone country jobTitle  ',
                    },
                },
            ]);
            return applications;
        }
        const totalApplications = await this.Application.countDocuments(query);
        const totalPages = Math.ceil(totalApplications / pageSize);
        const applications = await this.Application.find(query, {
            status: 1,
            applicationDate: 1,
            createdAt: 1,
        })
            .populate([
            {
                path: 'opportunity',
                select: 'versions industry country dateOfExpiration status',
            },
            {
                path: 'candidate',
                select: 'user',
                populate: {
                    path: 'user',
                    select: 'firstName lastName email phone country jobTitle',
                },
            },
        ])
            .sort({ createdAt: sortOrder === 'asc' ? 1 : -1 })
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize || 10)
            .lean();
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalApplications,
            applications,
            expiredOpportunities,
            activeOpportunities,
        };
    }
    async autoRejectOldApplications() {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 15);
        await this.Application.updateMany({ status: constants_1.ApplicationStatus.PENDING, applicationDate: { $lte: (0, functions_1.formatDate)(cutoffDate) } }, { status: constants_1.ApplicationStatus.REJECTED });
    }
    async updateApplicationStatus(applicationId, applicationData) {
        if (!mongoose_1.default.Types.ObjectId.isValid(applicationId)) {
            throw new http_exception_1.default(400, 'Invalid applicationId format');
        }
        const application = await opportunity_application_model_1.default.findById(applicationId).lean();
        if (!application) {
            console.error('Application not found:', applicationId);
            throw new http_exception_1.default(404, 'Application not found');
        }
        const updatedApplication = await opportunity_application_model_1.default.findByIdAndUpdate(applicationId, { ...applicationData }, { new: true }).lean();
        return updatedApplication;
    }
    async cancelApplication(applicationId, currentUser) {
        const foundApplication = await this.Application.findById(applicationId);
        if (!foundApplication)
            throw new http_exception_1.default(404, messages_1.MESSAGES.APPLICATION.NOT_FOUND);
        const candidate = await this.Candidate.findOne({ user: currentUser._id });
        if (!candidate)
            throw new http_exception_1.default(404, 'Candidate not found');
        if (candidate._id.toString() == foundApplication.candidate.toString()) {
            await this.Opportunity.findByIdAndUpdate(foundApplication.opportunity, { $pull: { applicants: foundApplication.candidate } });
            await this.Application.findByIdAndDelete(applicationId);
        }
        else
            throw new http_exception_1.default(403, 'Permission denied! Only the applicant can cancel their application');
    }
    async correctApplications() {
        const brokenResumes = await opportunity_application_model_1.default.find({ resume: { $nin: [RegExp('.(pdf|doc|docx)$', 'i')] } });
        let correctedApplications = 0;
        brokenResumes.forEach(async (brokenResume) => {
            const filenamesByCandidate = await this.Candidate.find({ 'cv._id': brokenResume.resume });
            if (filenamesByCandidate.length > 0) {
                await opportunity_application_model_1.default.findByIdAndUpdate(brokenResume._id, { $set: { resume: filenamesByCandidate[0]?.cv[0]?.fileName } }, { new: true });
                correctedApplications++;
            }
        });
        return {
            message: `Broken applications corrected`,
        };
    }
}
exports.default = ApplicationService;
//# sourceMappingURL=opportunity.application.service.js.map