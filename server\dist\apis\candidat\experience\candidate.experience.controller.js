"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const candidate_experience_service_1 = __importDefault(require("./candidate.experience.service"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("../../../utils/helpers/constants");
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class CandidateExperienceController {
    constructor() {
        this.path = '/candidates/experiences';
        this.router = (0, express_1.Router)();
        this.candidateExperienceService = new candidate_experience_service_1.default();
        this.update = async (request, response, next) => {
            try {
                const id = request.user._id;
                const experienceId = request.params.experienceId;
                const experience = request.body;
                const result = await this.candidateExperienceService.update(id, experienceId, experience);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAll = async (request, response, next) => {
            try {
                const candidateId = request.user._id;
                const experiences = await this.candidateExperienceService.getAll(candidateId);
                response.json(experiences);
            }
            catch (error) {
                next(error);
            }
        };
        this.add = async (request, response, next) => {
            try {
                const id = request.user._id;
                const experience = request.body;
                const result = await this.candidateExperienceService.add(id, experience);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.delete = async (request, response, next) => {
            try {
                const id = request.user._id;
                const experienceId = request.params.experienceId;
                await this.candidateExperienceService.delete(id, experienceId);
                response.send({ message: "Candidate's experience deleted successfully" });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), this.getAll);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), cache_middleware_1.invalidateCache, this.add);
        this.router.put(`${this.path}/:experienceId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), cache_middleware_1.invalidateCache, this.update);
        this.router.delete(`${this.path}/:experienceId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), cache_middleware_1.invalidateCache, this.delete);
    }
}
exports.default = CandidateExperienceController;
//# sourceMappingURL=candidate.experience.controller.js.map