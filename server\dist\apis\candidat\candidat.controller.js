"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const candidat_service_1 = __importDefault(require("./candidat.service"));
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const multer_1 = __importDefault(require("multer"));
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class CandidatController {
    constructor() {
        this.path = '/candidates';
        this.router = (0, express_1.Router)();
        this.candidatService = new candidat_service_1.default();
        this.upload = (0, multer_1.default)();
        this.getAllCandidatesProfileCompletion = async (req, res) => {
            try {
                const results = await this.candidatService.calculateAllCandidatesProfileCompletion();
                res.status(200).json(results);
            }
            catch (error) {
                res.status(500).json({});
            }
        };
        this.getAll = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.candidatService.getAll(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.get = async (request, response, next) => {
            try {
                const id = request.user._id;
                response.send(await this.candidatService.get(id));
            }
            catch (error) {
                next(error);
            }
        };
        this.getById = async (request, response, next) => {
            try {
                const id = request.params.id;
                response.send(await this.candidatService.getbyid(id));
            }
            catch (error) {
                next(error);
            }
        };
        this.getCandidateBase64 = async (request, response, next) => {
            try {
                const id = request.params.id;
                response.send(await this.candidatService.getCandidateBase64(id));
            }
            catch (error) {
                next(error);
            }
        };
        this.getporcentagecandidateprofile = async (request, response, next) => {
            try {
                const id = request.params.id;
                const result = await this.candidatService.calculateProfileCompletion(id);
                response.send({
                    success: true,
                    completionPercentage: result.completionPercentage,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.delete = async (request, response, next) => {
            try {
                const id = request.user._id;
                await this.candidatService.archive(id);
                response.send({
                    message: messages_1.MESSAGES.CANDIDATE.ARCHIVED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const candidat = request.body;
                const currentUser = request.user._id;
                const data = await this.candidatService.update(currentUser, candidat);
                response.send({
                    data: data,
                    message: 'Information Updated',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteCandidateResume = async (request, response, next) => {
            const candidateId = request.user._id;
            const { fileName } = request.body;
            try {
                await this.candidatService.deleteCandidateResume(candidateId, fileName);
                response.send({ message: 'Resume deleted successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.getAll);
        this.router.get(`${this.path}/currentCandidate`, validateApiKey_middleware_1.default, authentication_middleware_1.default, this.get);
        this.router.get(`${this.path}/byid/:id`, validateApiKey_middleware_1.default, authentication_middleware_1.default, this.getById);
        this.router.put(`${this.path}/calculate`, this.getAllCandidatesProfileCompletion);
        this.router.get(`${this.path}/Base64/:id`, validateApiKey_middleware_1.default, mongoId_validation_middleware_1.default, this.getCandidateBase64);
        this.router.put(`${this.path}`, authentication_middleware_1.default, this.update);
        this.router.delete(`${this.path}`, authentication_middleware_1.default, this.delete);
        this.router.delete(`${this.path}/resume`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), this.deleteCandidateResume);
        this.router.get(`${this.path}/calculate/:id`, this.getporcentagecandidateprofile);
    }
}
exports.default = CandidatController;
//# sourceMappingURL=candidat.controller.js.map