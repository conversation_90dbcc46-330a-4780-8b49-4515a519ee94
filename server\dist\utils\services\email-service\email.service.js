"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendEmail = void 0;
const nodemailer = __importStar(require("nodemailer"));
const expressHandlebars = __importStar(require("express-handlebars"));
const nodemailer_express_handlebars_1 = __importDefault(require("nodemailer-express-handlebars"));
const handlebars_1 = __importDefault(require("handlebars"));
const allow_prototype_access_1 = require("@handlebars/allow-prototype-access");
const path = __importStar(require("path"));
const sendEmail = (data) => {
    const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: Number(process.env.SMTP_PORT),
        secure: true,
        auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASSWORD,
        },
    });
    const handlebars = expressHandlebars.create({
        handlebars: (0, allow_prototype_access_1.allowInsecurePrototypeAccess)(handlebars_1.default),
        extname: '.handlebars',
        defaultLayout: 'main',
        layoutsDir: path.join(__dirname, 'views/layouts'),
        partialsDir: path.join(__dirname, 'views/partials'),
        helpers: {
            formatKey: function (key) {
                if (key.toLowerCase().includes('team')) {
                    return '';
                }
                return key.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());
            },
            eq: function (a, b) {
                return a === b;
            },
        },
    });
    transporter.use('compile', (0, nodemailer_express_handlebars_1.default)({
        viewEngine: handlebars,
        viewPath: path.join(__dirname, 'views/partials'),
    }));
    // const defaultAttachments = [
    //     {
    //         filename: 'energy.png',
    //         path: `${__dirname}/../../../public/api/v1/images/energy.png`,
    //         cid: 'energy',
    //     },
    //     {
    //         filename: 'transport.png',
    //         path: `${__dirname}/../../../public/api/v1/images/transport.png`,
    //         cid: 'transport',
    //     },
    //     {
    //         filename: 'itandtelecom.png',
    //         path: `${__dirname}/../../../public/api/v1/images/itandtelecom.png`,
    //         cid: 'itandtelecom',
    //     },
    //     {
    //         filename: 'oilandgas.png',
    //         path: `${__dirname}/../../../public/api/v1/images/oilandgas.png`,
    //         cid: 'oilandgas',
    //     },
    //     {
    //         filename: 'banking.png',
    //         path: `${__dirname}/../../../public/api/v1/images/banking.png`,
    //         cid: 'banking',
    //     },
    //     {
    //         filename: 'x.png',
    //         path: `${__dirname}/../../../public/api/v1/images/x.png`,
    //         cid: 'x',
    //     },
    //     {
    //         filename: 'linkedin.png',
    //         path: `${__dirname}/../../../public/api/v1/images/linkedin.png`,
    //         cid: 'linkedin',
    //     },
    //     {
    //         filename: 'youtube.png',
    //         path: `${__dirname}/../../../public/api/v1/images/youtube.png`,
    //         cid: 'youtube',
    //     },
    //     {
    //         filename: 'instagram.png',
    //         path: `${__dirname}/../../../public/api/v1/images/instagram.png`,
    //         cid: 'instagram',
    //     },
    //     {
    //         filename: 'facebook.png',
    //         path: `${__dirname}/../../../public/api/v1/images/facebook.png`,
    //         cid: 'facebook',
    //     },
    //     {
    //         filename: 'industriesList.png',
    //         path: `${__dirname}/../../../public/api/v1/images/industriesList.png`,
    //         cid: 'industriesList',
    //     },
    // ];
    const { to, cc, subject, template, context, attachments } = data;
    let mailOptions = {
        from: '"Pentabell" <<EMAIL>>',
        to: to,
        subject: subject,
        template: template,
        cc: cc,
        context: context,
    };
    if (attachments)
        mailOptions = { ...mailOptions, attachments };
    handlebars
        .render(`${handlebars.partialsDir}/${template}.handlebars`, mailOptions.context)
        .then(() => {
        transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
                console.error(error);
                return;
            }
        });
    })
        .catch(error => {
        console.error('Error during rendering:', error);
    });
};
exports.sendEmail = sendEmail;
//# sourceMappingURL=email.service.js.map