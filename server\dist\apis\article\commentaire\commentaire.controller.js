"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const commentaire_service_1 = __importDefault(require("./commentaire.service"));
const messages_1 = require("@/utils/helpers/messages");
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const commentaire_model_1 = require("./commentaire.model");
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
class CommentController {
    constructor() {
        this.path = '/comments';
        this.commentService = new commentaire_service_1.default();
        this.router = (0, express_1.Router)();
        this.getAllComments = async (req, res, next) => {
            try {
                const queries = req.query;
                const comments = await this.commentService.getAllComments(queries);
                res.send(comments);
            }
            catch (error) {
                next(error);
            }
        };
        this.addCommentToArticle = async (req, res, next) => {
            try {
                await this.commentService.addCommentToArticle(req.params.articleId, req.body, req.user);
                res.send({ message: 'Comment added to article successfully.' });
            }
            catch (error) {
                next(error);
            }
        };
        this.approveOrDisapproveComment = async (req, res, next) => {
            try {
                const commentId = req.params.commentId;
                const approved = req.body.approve;
                res.send({ message: await this.commentService.approveOrDisapproveComment(commentId, approved) });
            }
            catch (error) {
                next(error);
            }
        };
        this.getCommentById = async (req, res, next) => {
            try {
                const { commentId } = req.params;
                const comment = await this.commentService.get(commentId);
                res.status(200).json(comment);
            }
            catch (error) {
                next(error);
            }
        };
        this.assignBadges = async () => {
            const usersWithApprovedComments = await commentaire_model_1.CommentModel.aggregate([
                { $match: { approved: true } },
                {
                    $group: {
                        _id: { email: '$email', name: '$name' },
                        approvedCount: { $sum: 1 },
                    },
                },
                { $match: { approvedCount: { $gte: 20 } } },
            ]);
            const badgeUsers = usersWithApprovedComments.map((user) => user._id.email);
            await commentaire_model_1.CommentModel.updateMany({ email: { $in: badgeUsers } }, { $set: { badge: 'top commenter' } });
        };
        this.getAllCommentsByArticleId = async (req, res, next) => {
            try {
                await this.assignBadges();
                const queries = req.query;
                const { articleId } = req.params;
                const comments = await this.commentService.getAllCommentsByArticle(articleId, queries);
                res.send(comments);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllCommentsByArticleIdDashboard = async (req, res, next) => {
            try {
                await this.assignBadges();
                const queries = req.query;
                const { articleId } = req.params;
                const comments = await this.commentService.getAllCommentsByArticleDashboard(articleId, queries);
                res.send(comments);
            }
            catch (error) {
                next(error);
            }
        };
        this.addResponseToComment = async (req, res, next) => {
            try {
                const { commentId } = req.params;
                const responseData = req.body;
                await this.commentService.addResponseToCommentService(commentId, responseData, req.user);
                res.send({ message: 'Response added to comment successfully.' });
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteComment = async (request, response, next) => {
            try {
                const id = request.params.id;
                const currentUser = request.user;
                await this.commentService.deleteComment(id, currentUser);
                response.send({
                    message: messages_1.MESSAGES.Comment.DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), queries_validation_middleware_1.default, this.getAllComments);
        this.router.get(`${this.path}/:articleId`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getAllCommentsByArticleId);
        this.router.get(`${this.path}/detail/:commentId`, this.getCommentById);
        this.router.get(`${this.path}/Dashboard/:articleId`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getAllCommentsByArticleIdDashboard);
        this.router.post(`${this.path}/:articleId`, validation_middleware_1.checkCurrentUserForComment, cache_middleware_1.invalidateCache, this.addCommentToArticle);
        this.router.post(`${this.path}/:commentId/response`, validation_middleware_1.checkCurrentUserForResponse, cache_middleware_1.invalidateCache, this.addResponseToComment);
        this.router.put(`${this.path}/approve/:commentId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.approveOrDisapproveComment);
        this.router.delete(`${this.path}/:id`, authentication_middleware_1.default, mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.deleteComment);
    }
}
exports.default = CommentController;
//# sourceMappingURL=commentaire.controller.js.map