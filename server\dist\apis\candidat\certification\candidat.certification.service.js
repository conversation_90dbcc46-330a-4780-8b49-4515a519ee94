"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const candidat_service_1 = __importDefault(require("../candidat.service"));
const candidat_certification_model_1 = __importDefault(require("./candidat.certification.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const candidat_model_1 = __importDefault(require("../candidat.model"));
class CandidateCertificationService {
    constructor() {
        this.Candidate = candidat_model_1.default;
        this.candidateService = new candidat_service_1.default();
    }
    async update(candidateId, certificationId, certificationData) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const certificationIndex = candidate.certifications.findIndex((cerId) => cerId._id.toString() === certificationId);
        if (certificationIndex !== -1) {
            const existingCertification = await candidat_certification_model_1.default.findById(certificationId);
            if (existingCertification) {
                existingCertification.title = certificationData.title;
                existingCertification.startDate = certificationData.startDate;
                existingCertification.endDate = certificationData.endDate;
                existingCertification.academy = certificationData.academy;
                await existingCertification.save();
            }
        }
        else
            throw new http_exception_1.default(404, 'Certification not found');
    }
    async add(candidateId, certificationData) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const newCertification = new candidat_certification_model_1.default(certificationData);
        await newCertification.save();
        candidate.certifications = candidate.certifications || [];
        candidate.certifications.push(newCertification._id);
        candidate.numberOfCertifications = Math.max((candidate.numberOfCertifications || 0) + 1);
        await this.Candidate.findOneAndUpdate({ user: candidateId }, { $set: candidate });
        const { completionPercentage } = await this.candidateService.calculateProfileCompletion(candidateId);
        await this.Candidate.findOneAndUpdate({ user: candidateId }, { $set: { profilePercentage: completionPercentage } });
    }
    async delete(candidateId, certificationId) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const certificationIndex = candidate.certifications.findIndex((cerId) => cerId._id.toString() === certificationId);
        if (certificationIndex !== -1) {
            candidate.certifications.splice(certificationIndex, 1);
            await candidat_certification_model_1.default.findByIdAndDelete(certificationId);
            await this.Candidate.findByIdAndUpdate(candidate._id, {
                $set: { numberOfCertifications: Math.max((candidate.numberOfCertifications || 0) - 1), certifications: candidate.certifications },
            });
        }
        else
            throw new http_exception_1.default(404, 'Certification not found');
        const { completionPercentage } = await this.candidateService.calculateProfileCompletion(candidateId);
        await this.Candidate.findOneAndUpdate({ user: candidateId }, { $set: { profilePercentage: completionPercentage } });
    }
    async getAll(candidateId) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const certificationsDetails = [];
        for (const certification of candidate.certifications) {
            const certificationDetail = await candidat_certification_model_1.default.findById(certification._id).lean();
            if (certificationDetail)
                certificationsDetails.push(certificationDetail);
        }
        return certificationsDetails;
    }
}
exports.default = CandidateCertificationService;
//# sourceMappingURL=candidat.certification.service.js.map