"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx":
/*!*******************************************************************!*\
  !*** ./src/features/glossary/component/GlossariesListWebsite.jsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryListWebsite; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction GlossaryListWebsite(param) {\n    let { glossaries } = param;\n    _s();\n    const letters = Object.keys(glossaries);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleToggle = ()=>{\n        setExpanded(!expanded);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"custom-max-width\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                container: true,\n                children: (letters === null || letters === void 0 ? void 0 : letters.length) > 0 && (letters === null || letters === void 0 ? void 0 : letters.map((letter, index)=>{\n                    var _glossaries_letter, _glossaries_letter1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        item: true,\n                        lg: 3,\n                        md: 4,\n                        sm: 6,\n                        xs: 12,\n                        className: \"letters\",\n                        id: letter,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"length\",\n                                children: (_glossaries_letter = glossaries[letter]) === null || _glossaries_letter === void 0 ? void 0 : _glossaries_letter.length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 31,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"letter\",\n                                children: letter\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 32,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"words\",\n                                children: (expanded ? glossaries[letter] : glossaries[letter].slice(0, 5)).map((glossary)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"word\",\n                                        href: \"/glossaries/\".concat(glossary.url),\n                                        children: glossary.word\n                                    }, glossary, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 33,\n                                columnNumber: 17\n                            }, this),\n                            ((_glossaries_letter1 = glossaries[letter]) === null || _glossaries_letter1 === void 0 ? void 0 : _glossaries_letter1.length) > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"glossary-button\",\n                                onClick: handleToggle,\n                                children: expanded ? \"Show less\" : \"Show more\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 44,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                        lineNumber: 21,\n                        columnNumber: 15\n                    }, this);\n                }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryListWebsite, \"DuL5jiiQQFgbn7gBKAyxwS/H4Ek=\");\n_c = GlossaryListWebsite;\nvar _c;\n$RefreshReg$(_c, \"GlossaryListWebsite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx\n"));

/***/ })

});