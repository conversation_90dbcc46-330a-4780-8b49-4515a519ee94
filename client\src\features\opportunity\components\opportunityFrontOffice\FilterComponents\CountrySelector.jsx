import { Autocomplete, TextField, InputAdornment } from "@mui/material";
import SvgMapPin from "@/assets/images/icons/MapPin.svg";

const CountrySelector = ({ value, options, onChange }) => (
  <div className="filter-options">
    <Autocomplete
      className="input-pentabell maps"
      id="tags-standard"
      value={value || []}
      options={options}
      getOptionLabel={(option) => option}
      onChange={onChange}
      renderInput={(params) => (
        <TextField
          {...params}
          className="input-pentabell multiple-select"
          variant="standard"
          placeholder="Country"
          slotProps={{
            input: {
              startAdornment: (
                <>
                  <InputAdornment position="start">
                    <SvgMapPin />
                  </InputAdornment>
                  {params.InputProps?.startAdornment}
                </>
              ),
            },
          }}
        />
      )}
    />
  </div>
);

export default CountrySelector;
