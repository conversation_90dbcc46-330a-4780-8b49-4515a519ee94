"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const guide_service_1 = __importDefault(require("./guide.service"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const guide_interface_1 = require("./guide.interface");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
class GuideController {
    constructor() {
        this.path = '/guides';
        this.guideService = new guide_service_1.default();
        this.router = (0, express_1.Router)();
        this.createguide = async (request, response, next) => {
            try {
                const guideData = request.body;
                const newGuide = await this.guideService.createGuide(guideData);
                response.status(201).send({ guide: newGuide });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateMainFields = async (req, res, next) => {
            try {
                const { guideId } = req.params;
                const updateData = req.body;
                res.send(await this.guideService.updateMainfieldsGuide(guideId, updateData));
            }
            catch (error) {
                next(error);
            }
        };
        this.getGuidesByTitle = async (req, res, next) => {
            try {
                const language = req.query.language || guide_interface_1.Language.ENGLISH;
                const guides = await this.guideService.getGuideTitles(language);
                res.send(guides);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllDownloadByGuides = async (req, res, next) => {
            try {
                const queries = req.query;
                const { guideId } = req.params;
                const downloads = await this.guideService.getAllDownloadByGuides(guideId, queries);
                res.send(downloads);
            }
            catch (error) {
                next(error);
            }
        };
        this.dowloadguide = async (req, res, next) => {
            try {
                await this.guideService.downloadGuide(req.params.guideId, req.body);
                res.send({ message: 'Guide download succesfully .' });
            }
            catch (error) {
                next(error);
            }
        };
        this.getGideById = async (req, res, next) => {
            try {
                const { guideId } = req.params;
                const guides = await this.guideService.get(guideId);
                res.send(guides);
            }
            catch (error) {
                next(error);
            }
        };
        this.getGuideByLanguageAndId = async (req, res, next) => {
            try {
                const { id, language } = req.params;
                const guide = await this.guideService.getGuideByLanguageAndId(language, id);
                if (!guide) {
                    if (language === 'fr') {
                        res.status(204).send();
                        return;
                    }
                    else {
                        res.status(404).json({ message: 'guide not found' });
                        return;
                    }
                }
                res.status(200).json(guide);
            }
            catch (error) {
                next(error);
            }
        };
        this.getGuideByUrl = async (req, res, next) => {
            try {
                const { url, language } = req.params;
                const currentUser = req.user;
                const guides = await this.guideService.getGuideByUrl(language, url, currentUser);
                res.send(guides);
            }
            catch (error) {
                next(error);
            }
        };
        this.getSlugBySlug = async (req, res, next) => {
            try {
                const { url, language } = req.params;
                const guides = await this.guideService.getSlugBySlug(language, url);
                res.json(guides);
            }
            catch (error) {
                next(error);
            }
        };
        this.updateGuideVersion = async (req, res, next) => {
            try {
                const { guideId, language } = req.params;
                const versionData = req.body;
                if (!guideId || !language) {
                    res.status(400).send('Missing guide ID or language.');
                }
                const languageEnum = language;
                const updatedGuide = await this.guideService.updateGuideVersion(guideId, languageEnum, versionData);
                res.send(updatedGuide);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAllGuides = async (request, response, next) => {
            const queries = request.query;
            const language = request.query.language || guide_interface_1.Language.ENGLISH;
            try {
                const guide = await this.guideService.getGuides(queries, language);
                response.status(200).send({ guide });
            }
            catch (error) {
                next(error);
            }
        };
        this.getArticleAndGuide = async (request, response, next) => {
            const queries = request.query;
            const language = request.query.language || guide_interface_1.Language.ENGLISH;
            try {
                const List = await this.guideService.getArticlesAndGuides(queries, language);
                response.status(200).send({ List });
            }
            catch (error) {
                next(error);
            }
        };
        this.getOppositeLanguageVersions = async (req, res, next) => {
            try {
                const { language, versionIds } = req.params;
                if (!language || !versionIds)
                    return next(new http_exception_1.default(400, 'Missing language or versionIds parameter.'));
                const versionIdsArray = versionIds.split(',');
                const result = await this.guideService.getOppositeLanguageVersionsGuide(language, versionIdsArray);
                res.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.archiveGuideByLanguageAndId = async (req, res, next) => {
            try {
                const { language, guideId } = req.params;
                if (!language || !guideId) {
                    throw new http_exception_1.default(400, 'Missing language or guideId parameter.');
                }
                const deletedGuide = await this.guideService.archiveGuideByLanguageAndId(language, guideId);
                if (!deletedGuide) {
                    throw new http_exception_1.default(404, `No guide or version found for language ${language} with guideId ${guideId}.`);
                }
                res.send({
                    message: `Version with language ${language} deleted successfully from guide with ID ${guideId}.`,
                    deletedGuide,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.createguide);
        this.router.post(`${this.path}/download/:guideId`, this.dowloadguide);
        this.router.post(`${this.path}/update/:language/:guideId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.updateGuideVersion);
        this.router.get(`${this.path}`, this.getAllGuides);
        this.router.get(`/AriclesAndGuides`, this.getArticleAndGuide);
        this.router.get(`${this.path}/:language/:versionIds/translation`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.validateCache, this.getOppositeLanguageVersions);
        this.router.get(`${this.path}/:language/listguide`, this.getGuidesByTitle);
        this.router.get(`/downloads/:guideId`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getAllDownloadByGuides);
        this.router.get(`${this.path}/:language/:url`, this.getGuideByUrl);
        this.router.get(`${this.path}/guide/:language/:id`, this.getGuideByLanguageAndId);
        this.router.get(`${this.path}/:guideId`, this.getGideById);
        this.router.put(`${this.path}/:guideId`, this.updateMainFields);
        this.router.get(`${this.path}/opposite/:language/:url`, validateApiKey_middleware_1.default, cache_middleware_1.validateCache, this.getSlugBySlug);
        this.router.delete(`${this.path}/:language/:guideId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), cache_middleware_1.invalidateCache, this.archiveGuideByLanguageAndId);
    }
}
exports.default = GuideController;
//# sourceMappingURL=guide.controller.js.map