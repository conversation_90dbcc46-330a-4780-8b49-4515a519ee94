"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const client_validations_1 = require("../client.validations");
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const mongoId_validation_middleware_1 = __importDefault(require("@/middlewares/mongoId-validation.middleware"));
const manager_service_1 = __importDefault(require("../services/manager.service"));
const queries_validation_middleware_1 = __importDefault(require("@/middlewares/queries-validation.middleware"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const messages_1 = require("@/utils/helpers/messages");
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class ManagerController {
    constructor() {
        this.path = '/managers';
        this.router = (0, express_1.Router)();
        this.managerService = new manager_service_1.default();
        this.upload = (0, multer_1.default)();
        this.get = async (request, response, next) => {
            try {
                const managerId = request.params.id;
                const result = await this.managerService.get(managerId);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getAll = async (request, response, next) => {
            try {
                const queries = request.query;
                const result = await this.managerService.getAll(queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.create = async (request, response, next) => {
            try {
                const managerData = request.body;
                const result = await this.managerService.create(managerData);
                response.status(201).send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.createMany = async (request, response, next) => {
            try {
                const file = request.file;
                if (!file) {
                    return response.status(400).send({
                        message: messages_1.MESSAGES.FILE.NO_FILE,
                    });
                }
                const result = await this.managerService.createMany(file);
                response.status(201).send({
                    message: messages_1.MESSAGES.MANAGERS.MANAGERS_IMPORTED,
                    managers: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.delete = async (request, response, next) => {
            try {
                const managerId = request.params.id;
                await this.managerService.delete(managerId);
                response.send({
                    message: messages_1.MESSAGES.MANAGERS.MANAGER_DELETED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const managerId = request.params.id;
                const managerData = request.body;
                const result = await this.managerService.update(managerId, managerData);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), queries_validation_middleware_1.default, cache_middleware_1.validateCache, this.getAll);
        this.router.get(`${this.path}/:id`, validateApiKey_middleware_1.default, authentication_middleware_1.default, mongoId_validation_middleware_1.default, cache_middleware_1.validateCache, this.get);
        this.router.post(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), (0, validation_middleware_1.validationMiddleware)(client_validations_1.createManagerSchema), cache_middleware_1.invalidateCache, this.create);
        this.router.post(`${this.path}/import`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.upload.single('file'), cache_middleware_1.invalidateCache, this.createMany);
        this.router.delete(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.delete);
        this.router.put(`${this.path}/:id`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), mongoId_validation_middleware_1.default, cache_middleware_1.invalidateCache, this.update);
    }
}
exports.default = ManagerController;
//# sourceMappingURL=manager.controller.js.map