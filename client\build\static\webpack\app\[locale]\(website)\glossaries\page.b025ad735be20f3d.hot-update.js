"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx":
/*!*******************************************************************!*\
  !*** ./src/features/glossary/component/GlossariesListWebsite.jsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryListWebsite; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction GlossaryListWebsite(param) {\n    let { glossaries, locale } = param;\n    _s();\n    const letters = Object.keys(glossaries);\n    const [expanded, setExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleToggle = ()=>{\n        setExpanded(!expanded);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"custom-max-width\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                container: true,\n                children: (letters === null || letters === void 0 ? void 0 : letters.length) > 0 && (letters === null || letters === void 0 ? void 0 : letters.map((letter, index)=>{\n                    var _glossaries_letter, _glossaries_letter1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        item: true,\n                        lg: 3,\n                        md: 4,\n                        sm: 6,\n                        xs: 12,\n                        className: \"letters\",\n                        id: letter,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"length\",\n                                children: (_glossaries_letter = glossaries[letter]) === null || _glossaries_letter === void 0 ? void 0 : _glossaries_letter.length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 31,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"letter\",\n                                children: letter\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 32,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"words\",\n                                children: (expanded ? glossaries[letter] : glossaries[letter].slice(0, 5)).map((glossary)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        className: \"word\",\n                                        href: \"\".concat(locale === \"\", \"/glossaries/\").concat(glossary.url),\n                                        children: glossary.word\n                                    }, glossary, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 33,\n                                columnNumber: 17\n                            }, this),\n                            ((_glossaries_letter1 = glossaries[letter]) === null || _glossaries_letter1 === void 0 ? void 0 : _glossaries_letter1.length) > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"glossary-button\",\n                                onClick: handleToggle,\n                                children: expanded ? \"Show less\" : \"Show more\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                                lineNumber: 44,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                        lineNumber: 21,\n                        columnNumber: 15\n                    }, this);\n                }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossariesListWebsite.jsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_s(GlossaryListWebsite, \"DuL5jiiQQFgbn7gBKAyxwS/H4Ek=\");\n_c = GlossaryListWebsite;\nvar _c;\n$RefreshReg$(_c, \"GlossaryListWebsite\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossariesListWebsite.jsx\n"));

/***/ })

});