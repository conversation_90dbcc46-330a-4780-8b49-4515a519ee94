"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/page",{

/***/ "(app-pages-browser)/./src/components/sections/LatestJobOffers.jsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/LatestJobOffers.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Container!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var embla_carousel_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! embla-carousel-react */ \"(app-pages-browser)/./node_modules/embla-carousel-react/esm/embla-carousel-react.esm.js\");\n/* harmony import */ var i18n_iso_countries__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! i18n-iso-countries */ \"(app-pages-browser)/./node_modules/i18n-iso-countries/index.js\");\n/* harmony import */ var i18n_iso_countries_langs_en_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! i18n-iso-countries/langs/en.json */ \"(app-pages-browser)/./node_modules/i18n-iso-countries/langs/en.json\");\n/* harmony import */ var _features_opportunity_components_opportunityFrontOffice_OpportunityComponents_OpportunityItemByGrid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItemByGrid */ \"(app-pages-browser)/./src/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItemByGrid.jsx\");\n/* harmony import */ var _ui_CustomButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LatestJobOffers(param) {\n    let { language } = param;\n    _s();\n    const OPTIONS = {\n        loop: false,\n        align: \"start\"\n    };\n    const [emblaRef] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(OPTIONS);\n    const [jobOffers, setJobOffers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        urgent: undefined\n    });\n    i18n_iso_countries__WEBPACK_IMPORTED_MODULE_3__.registerLocale(i18n_iso_countries_langs_en_json__WEBPACK_IMPORTED_MODULE_4__);\n    const fetchJobOffers = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_7__.axiosGetJsonSSR.get(\"\".concat(_utils_urls__WEBPACK_IMPORTED_MODULE_8__.baseURL).concat(_utils_urls__WEBPACK_IMPORTED_MODULE_8__.API_URLS.opportunity, \"/urgent\"), {\n                params: {\n                    urgent: query.urgent\n                }\n            });\n            if (response.data && response.data.length > 0) {\n                const fetchedOffers = response.data.map((offer)=>{\n                    var _offer_versions_language, _offer_versions_language1;\n                    return {\n                        id: offer._id,\n                        title: ((_offer_versions_language = offer.versions[language]) === null || _offer_versions_language === void 0 ? void 0 : _offer_versions_language.title) || \"Titre non disponible\",\n                        industry: offer.industry,\n                        country: offer.country,\n                        dateOfExpiration: offer.dateOfExpiration,\n                        minExperience: offer.minExperience,\n                        maxExperience: offer.maxExperience,\n                        existingLanguages: offer.existingLanguages,\n                        reference: offer.reference,\n                        urgent: offer.urgent,\n                        url: (_offer_versions_language1 = offer.versions[language]) === null || _offer_versions_language1 === void 0 ? void 0 : _offer_versions_language1.url\n                    };\n                });\n                setJobOffers(fetchedOffers);\n            } else {\n                setJobOffers([]);\n                setError(\"No job offers available at the moment.\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching job offers:\", err);\n            setError(\"Failed to fetch job offers\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchJobOffers();\n    }, [\n        query\n    ]);\n    const onClickFilter = (data)=>{\n        if (data.urgent !== undefined) {\n            setQuery((prev)=>({\n                    ...prev,\n                    urgent: data.urgent\n                }));\n        } else {\n            setQuery((prev)=>({\n                    ...prev,\n                    urgent: undefined\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        id: \"latest-offers\",\n        className: \"custom-max-width\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"heading-h1 text-center\",\n                children: t(\"homePage:s3:title\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"filter-btns\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        onClick: ()=>onClickFilter({\n                                urgent: !query.urgent\n                            }),\n                        text: t(\"homePage:s3:btu\"),\n                        className: \"\".concat(query.urgent ? \"btn btn-filter selected\" : \"btn btn-outlined\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        text: t(\"homePage:s3:btlast\"),\n                        onClick: ()=>onClickFilter({\n                                urgent: undefined\n                            }),\n                        className: \"\".concat(query.urgent === undefined ? \"btn btn-filter selected\" : \"btn btn-outlined\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"embla\",\n                id: \"jobs__slider\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"embla__viewport\",\n                    ref: emblaRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"embla__container\",\n                        children: jobOffers.map((opportunity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_opportunity_components_opportunityFrontOffice_OpportunityComponents_OpportunityItemByGrid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                opportunity: opportunity,\n                                language: \"en\"\n                            }, opportunity === null || opportunity === void 0 ? void 0 : opportunity._id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"center-div\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    text: t(\"homePage:s3:all\"),\n                    link: \"/\".concat(_helpers_routesList__WEBPACK_IMPORTED_MODULE_9__.websiteRoutesList.opportunities.route),\n                    // onClick={() => onClickFilter({ urgent: undefined })}\n                    className: \"btn btn-filled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(LatestJobOffers, \"3pdFVtrTsKhzPDGwe+Ms3jSedJI=\", false, function() {\n    return [\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = LatestJobOffers;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LatestJobOffers);\nvar _c;\n$RefreshReg$(_c, \"LatestJobOffers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/LatestJobOffers.jsx\n"));

/***/ })

});