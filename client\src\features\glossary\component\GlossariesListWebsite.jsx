"use client";

import { But<PERSON>, Container, Grid } from "@mui/material";
import { useState } from "react";

export default function GlossaryListWebsite({ glossaries }) {
  const letters = Object.keys(glossaries);
  const [expanded, setExpanded] = useState(false);

  const handleToggle = () => {
    setExpanded(!expanded);
  };

  return (
    <div id="glossary-page">
      <Container className="custom-max-width">
        <Grid container>
          {letters?.length > 0 &&
            letters?.map((letter, index) => (
              <Grid
                item
                lg={3}
                md={4}
                sm={6}
                xs={12}
                key={index}
                className="letters"
                id={letter}
              >
                <p className="length">{glossaries[letter]?.length}</p>
                <p className="letter">{letter}</p>
                {(expanded
                  ? glossaries[letter]
                  : glossaries[letter].slice(0, 5)
                ).map((glossary) => (
                  <p className="word" key={glossary}>
                    {glossary}
                  </p>
                ))}
                {glossaries[letter]?.length > 5 && (
                  <Button className="glossary-button" onClick={handleToggle}>
                    {expanded ? "Show less" : "Show more"}
                  </Button>
                )}
              </Grid>
            ))}
        </Grid>
      </Container>
    </div>
  );
}
