import { Container } from "@mui/material";

import SvgbarChartTransport from "@/assets/images/services/icons/barChartTransport.svg";
import SvgbarChartTelecom from "@/assets/images/services/icons/barChartTelecom.svg";
import SvgbarChartEnergy from "@/assets/images/services/icons/barChartEnergy.svg";
import SvgbarChartOilGaz from "@/assets/images/services/icons/barChartOilGaz.svg";
import SvgbarChartBanking from "@/assets/images/services/icons/barChartBanking.svg";
import SvgbarChartOther from "@/assets/images/services/icons/barChartOther.svg";

function BusinessInIraq({ t }) {
  return (
    <Container id="business-tunisia" className="custom-max-width">
      <p className="heading-h1 text-center">{t("iraq:BusinessInIraq:title")}</p>
      <p className="sub-heading text-center">
        {t("iraq:BusinessInIraq:description")}
      </p>
      <div className="locations">
        <div className="location-item">
          <p className="label">
            <span>
              <SvgbarChartTransport />
            </span>
          </p>
          <p className="value paragraph">{t("iraq:BusinessInIraq:s1:title")}</p>
        </div>
        <div className="location-item">
          <p className="label">
            <span>
              <SvgbarChartTelecom />
            </span>
          </p>
          <p className="value paragraph">{t("iraq:BusinessInIraq:s2:title")}</p>
        </div>
        <div className="location-item">
          <p className="label">
            <span>
              <SvgbarChartEnergy />
            </span>
          </p>
          <p className="value paragraph">{t("iraq:BusinessInIraq:s3:title")}</p>
        </div>

        <div className="location-item">
          <p className="label">
            <span>
              <SvgbarChartOilGaz />
            </span>
          </p>
          <p className="value paragraph">{t("iraq:BusinessInIraq:s4:title")}</p>
        </div>

        <div className="location-item">
          <p className="label">
            <span>
              <SvgbarChartBanking />
            </span>
          </p>
          <p className="value paragraph">{t("iraq:BusinessInIraq:s5:title")}</p>
        </div>

        <div className="location-item">
          <p className="label">
            <span>
              <SvgbarChartOther />
            </span>
          </p> 
           <p className="value paragraph">{t("iraq:BusinessInIraq:s6:title")}</p>
        </div>
      </div>
    </Container>
  );
}

export default BusinessInIraq;
