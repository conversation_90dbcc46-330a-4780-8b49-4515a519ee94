"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/page",{

/***/ "(app-pages-browser)/./src/components/sections/LatestJobOffers.jsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/LatestJobOffers.jsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Container!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var embla_carousel_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! embla-carousel-react */ \"(app-pages-browser)/./node_modules/embla-carousel-react/esm/embla-carousel-react.esm.js\");\n/* harmony import */ var i18n_iso_countries__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! i18n-iso-countries */ \"(app-pages-browser)/./node_modules/i18n-iso-countries/index.js\");\n/* harmony import */ var i18n_iso_countries_langs_en_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! i18n-iso-countries/langs/en.json */ \"(app-pages-browser)/./node_modules/i18n-iso-countries/langs/en.json\");\n/* harmony import */ var _features_opportunity_components_opportunityFrontOffice_OpportunityComponents_OpportunityItemByGrid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItemByGrid */ \"(app-pages-browser)/./src/features/opportunity/components/opportunityFrontOffice/OpportunityComponents/OpportunityItemByGrid.jsx\");\n/* harmony import */ var _ui_CustomButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/CustomButton */ \"(app-pages-browser)/./src/components/ui/CustomButton.jsx\");\n/* harmony import */ var _config_axios__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/axios */ \"(app-pages-browser)/./src/config/axios.js\");\n/* harmony import */ var _utils_urls__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/urls */ \"(app-pages-browser)/./src/utils/urls.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/helpers/routesList */ \"(app-pages-browser)/./src/helpers/routesList.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LatestJobOffers(param) {\n    let { language } = param;\n    _s();\n    const OPTIONS = {\n        loop: false,\n        align: \"start\"\n    };\n    const [emblaRef] = (0,embla_carousel_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(OPTIONS);\n    const [jobOffers, setJobOffers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        urgent: undefined\n    });\n    i18n_iso_countries__WEBPACK_IMPORTED_MODULE_3__.registerLocale(i18n_iso_countries_langs_en_json__WEBPACK_IMPORTED_MODULE_4__);\n    const fetchJobOffers = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            const response = await _config_axios__WEBPACK_IMPORTED_MODULE_7__.axiosGetJsonSSR.get(\"\".concat(_utils_urls__WEBPACK_IMPORTED_MODULE_8__.baseURL).concat(_utils_urls__WEBPACK_IMPORTED_MODULE_8__.API_URLS.opportunity, \"/urgent\"), {\n                params: {\n                    urgent: query.urgent\n                }\n            });\n            if (response.data && response.data.length > 0) {\n                const fetchedOffers = response.data.map((offer)=>{\n                    var _offer_versions_language, _offer_versions_language1;\n                    return {\n                        id: offer._id,\n                        title: ((_offer_versions_language = offer.versions[language]) === null || _offer_versions_language === void 0 ? void 0 : _offer_versions_language.title) || \"Titre non disponible\",\n                        industry: offer.industry,\n                        country: offer.country,\n                        dateOfExpiration: offer.dateOfExpiration,\n                        minExperience: offer.minExperience,\n                        maxExperience: offer.maxExperience,\n                        existingLanguages: offer.existingLanguages,\n                        reference: offer.reference,\n                        urgent: offer.urgent,\n                        url: (_offer_versions_language1 = offer.versions[language]) === null || _offer_versions_language1 === void 0 ? void 0 : _offer_versions_language1.url\n                    };\n                });\n                setJobOffers(fetchedOffers);\n            } else {\n                setJobOffers([]);\n                setError(\"No job offers available at the moment.\");\n            }\n        } catch (err) {\n            console.error(\"Error fetching job offers:\", err);\n            setError(\"Failed to fetch job offers\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchJobOffers();\n    }, [\n        query\n    ]);\n    const onClickFilter = (data)=>{\n        if (data.urgent !== undefined) {\n            setQuery((prev)=>({\n                    ...prev,\n                    urgent: data.urgent\n                }));\n        } else {\n            setQuery((prev)=>({\n                    ...prev,\n                    urgent: undefined\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n        id: \"latest-offers\",\n        className: \"custom-max-width\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"heading-h1 text-center\",\n                children: t(\"homePage:s3:title\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                id: \"filter-btns\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        onClick: ()=>onClickFilter({\n                                urgent: !query.urgent\n                            }),\n                        text: t(\"homePage:s3:btu\"),\n                        className: \"\".concat(query.urgent ? \"btn btn-filter selected\" : \"btn btn-outlined\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        text: t(\"homePage:s3:btlast\"),\n                        onClick: ()=>onClickFilter({\n                                urgent: undefined\n                            }),\n                        className: \"\".concat(query.urgent === undefined ? \"btn btn-filter selected\" : \"btn btn-outlined\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"embla\",\n                id: \"jobs__slider\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"embla__viewport\",\n                    ref: emblaRef,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"embla__container\",\n                        children: jobOffers.map((opportunity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_opportunity_components_opportunityFrontOffice_OpportunityComponents_OpportunityItemByGrid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                opportunity: opportunity,\n                                language: \"en\"\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                                lineNumber: 114,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"center-div\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_CustomButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    text: t(\"homePage:s3:all\"),\n                    link: \"/\".concat(_helpers_routesList__WEBPACK_IMPORTED_MODULE_9__.websiteRoutesList.opportunities.route),\n                    // onClick={() => onClickFilter({ urgent: undefined })}\n                    className: \"btn btn-filled\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\components\\\\sections\\\\LatestJobOffers.jsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(LatestJobOffers, \"3pdFVtrTsKhzPDGwe+Ms3jSedJI=\", false, function() {\n    return [\n        embla_carousel_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = LatestJobOffers;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LatestJobOffers);\nvar _c;\n$RefreshReg$(_c, \"LatestJobOffers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3NlY3Rpb25zL0xhdGVzdEpvYk9mZmVycy5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUM0QztBQUNGO0FBQ0s7QUFDSztBQUNUO0FBQ2E7QUFFaUY7QUFDM0Y7QUFDRztBQUNBO0FBQ1E7QUFFekQsU0FBU2EsZ0JBQWdCLEtBQVk7UUFBWixFQUFFQyxRQUFRLEVBQUUsR0FBWjs7SUFDdkIsTUFBTUMsVUFBVTtRQUFFQyxNQUFNO1FBQU9DLE9BQU87SUFBUTtJQUM5QyxNQUFNLENBQUNDLFNBQVMsR0FBR2QsaUVBQWdCQSxDQUFDVztJQUNwQyxNQUFNLENBQUNJLFdBQVdDLGFBQWEsR0FBR3BCLCtDQUFRQSxDQUFDLEVBQUU7SUFDN0MsTUFBTSxDQUFDcUIsU0FBU0MsV0FBVyxHQUFHdEIsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDdUIsT0FBT0MsU0FBUyxHQUFHeEIsK0NBQVFBLENBQUM7SUFFbkMsTUFBTSxFQUFFeUIsQ0FBQyxFQUFFLEdBQUd0Qiw2REFBY0E7SUFDNUIsTUFBTSxDQUFDdUIsT0FBT0MsU0FBUyxHQUFHM0IsK0NBQVFBLENBQUM7UUFDakM0QixRQUFRQztJQUNWO0lBRUF4Qiw4REFBd0IsQ0FBQ0MsNkRBQVFBO0lBRWpDLE1BQU15QixpQkFBaUI7UUFDckJULFdBQVc7UUFDWEUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNUSxXQUFXLE1BQU12QiwwREFBZUEsQ0FBQ3dCLEdBQUcsQ0FDeEMsR0FBYXZCLE9BQVZDLGdEQUFPQSxFQUF3QixPQUFyQkQsaURBQVFBLENBQUN3QixXQUFXLEVBQUMsWUFDbEM7Z0JBQ0VDLFFBQVE7b0JBQ05QLFFBQVFGLE1BQU1FLE1BQU07Z0JBQ3RCO1lBQ0Y7WUFHRixJQUFJSSxTQUFTSSxJQUFJLElBQUlKLFNBQVNJLElBQUksQ0FBQ0MsTUFBTSxHQUFHLEdBQUc7Z0JBQzdDLE1BQU1DLGdCQUFnQk4sU0FBU0ksSUFBSSxDQUFDRyxHQUFHLENBQUMsQ0FBQ0M7d0JBRWhDQSwwQkFTRkE7MkJBWDZDO3dCQUNsREMsSUFBSUQsTUFBTUUsR0FBRzt3QkFDYkMsT0FBT0gsRUFBQUEsMkJBQUFBLE1BQU1JLFFBQVEsQ0FBQzlCLFNBQVMsY0FBeEIwQiwrQ0FBQUEseUJBQTBCRyxLQUFLLEtBQUk7d0JBQzFDRSxVQUFVTCxNQUFNSyxRQUFRO3dCQUN4QkMsU0FBU04sTUFBTU0sT0FBTzt3QkFDdEJDLGtCQUFrQlAsTUFBTU8sZ0JBQWdCO3dCQUN4Q0MsZUFBZVIsTUFBTVEsYUFBYTt3QkFDbENDLGVBQWVULE1BQU1TLGFBQWE7d0JBQ2xDQyxtQkFBbUJWLE1BQU1VLGlCQUFpQjt3QkFDMUNDLFdBQVdYLE1BQU1XLFNBQVM7d0JBQzFCdkIsUUFBUVksTUFBTVosTUFBTTt3QkFDcEJ3QixHQUFHLEdBQUVaLDRCQUFBQSxNQUFNSSxRQUFRLENBQUM5QixTQUFTLGNBQXhCMEIsZ0RBQUFBLDBCQUEwQlksR0FBRztvQkFDcEM7O2dCQUNBaEMsYUFBYWtCO1lBQ2YsT0FBTztnQkFDTGxCLGFBQWEsRUFBRTtnQkFDZkksU0FBUztZQUNYO1FBQ0YsRUFBRSxPQUFPNkIsS0FBSztZQUNaQyxRQUFRL0IsS0FBSyxDQUFDLDhCQUE4QjhCO1lBQzVDN0IsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQXJCLGdEQUFTQSxDQUFDO1FBQ1I4QjtJQUNGLEdBQUc7UUFBQ0w7S0FBTTtJQUVWLE1BQU02QixnQkFBZ0IsQ0FBQ25CO1FBQ3JCLElBQUlBLEtBQUtSLE1BQU0sS0FBS0MsV0FBVztZQUM3QkYsU0FBUyxDQUFDNkIsT0FBVTtvQkFDbEIsR0FBR0EsSUFBSTtvQkFDUDVCLFFBQVFRLEtBQUtSLE1BQU07Z0JBQ3JCO1FBQ0YsT0FBTztZQUNMRCxTQUFTLENBQUM2QixPQUFVO29CQUNsQixHQUFHQSxJQUFJO29CQUNQNUIsUUFBUUM7Z0JBQ1Y7UUFDRjtJQUNGO0lBRUEscUJBQ0UsOERBQUMzQixzRkFBU0E7UUFBQ3VDLElBQUc7UUFBZ0JnQixXQUFVOzswQkFDdEMsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQmhDLEVBQUU7Ozs7OzswQkFDMUMsOERBQUNrQztnQkFBSWxCLElBQUc7O2tDQUNOLDhEQUFDakMsd0RBQVlBO3dCQUNYb0QsU0FBUyxJQUFNTCxjQUFjO2dDQUFFM0IsUUFBUSxDQUFDRixNQUFNRSxNQUFNOzRCQUFDO3dCQUNyRGlDLE1BQU1wQyxFQUFFO3dCQUNSZ0MsV0FBVyxHQUVWLE9BREMvQixNQUFNRSxNQUFNLEdBQUcsNEJBQTRCOzs7Ozs7a0NBRy9DLDhEQUFDcEIsd0RBQVlBO3dCQUNYcUQsTUFBTXBDLEVBQUU7d0JBQ1JtQyxTQUFTLElBQU1MLGNBQWM7Z0NBQUUzQixRQUFRQzs0QkFBVTt3QkFDakQ0QixXQUFXLEdBSVYsT0FIQy9CLE1BQU1FLE1BQU0sS0FBS0MsWUFDYiw0QkFDQTs7Ozs7Ozs7Ozs7OzBCQUtWLDhEQUFDaUM7Z0JBQVFMLFdBQVU7Z0JBQVFoQixJQUFJOzBCQUM3Qiw0RUFBQ2tCO29CQUFJRixXQUFVO29CQUFrQk0sS0FBSzdDOzhCQUNwQyw0RUFBQ3lDO3dCQUFJRixXQUFVO2tDQUNadEMsVUFBVW9CLEdBQUcsQ0FBQyxDQUFDTCxhQUFhOEIsc0JBQzNCLDhEQUFDekQsMklBQXFCQTtnQ0FFcEIyQixhQUFhQTtnQ0FDYnBCLFVBQVU7K0JBRkxrRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU2YsOERBQUNMO2dCQUFJRixXQUFVOzBCQUNiLDRFQUFDakQsd0RBQVlBO29CQUNYcUQsTUFBTXBDLEVBQUU7b0JBQ1J3QyxNQUFNLElBQTBDLE9BQXRDckQsa0VBQWlCQSxDQUFDc0QsYUFBYSxDQUFDQyxLQUFLO29CQUMvQyx1REFBdUQ7b0JBQ3ZEVixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtyQjtHQXZIUzVDOztRQUVZVCw2REFBZ0JBO1FBS3JCRCx5REFBY0E7OztLQVByQlU7QUF5SFQsK0RBQWVBLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvc2VjdGlvbnMvTGF0ZXN0Sm9iT2ZmZXJzLmpzeD85MmFjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IENvbnRhaW5lciB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSBcInJlYWN0LWkxOG5leHRcIjtcclxuaW1wb3J0IHVzZUVtYmxhQ2Fyb3VzZWwgZnJvbSBcImVtYmxhLWNhcm91c2VsLXJlYWN0XCI7XHJcbmltcG9ydCBjb3VudHJpZXMgZnJvbSBcImkxOG4taXNvLWNvdW50cmllc1wiO1xyXG5pbXBvcnQgZW5Mb2NhbGUgZnJvbSBcImkxOG4taXNvLWNvdW50cmllcy9sYW5ncy9lbi5qc29uXCI7XHJcblxyXG5pbXBvcnQgT3Bwb3J0dW5pdHlJdGVtQnlHcmlkIGZyb20gXCJAL2ZlYXR1cmVzL29wcG9ydHVuaXR5L2NvbXBvbmVudHMvb3Bwb3J0dW5pdHlGcm9udE9mZmljZS9PcHBvcnR1bml0eUNvbXBvbmVudHMvT3Bwb3J0dW5pdHlJdGVtQnlHcmlkXCI7XHJcbmltcG9ydCBDdXN0b21CdXR0b24gZnJvbSBcIi4uL3VpL0N1c3RvbUJ1dHRvblwiO1xyXG5pbXBvcnQgeyBheGlvc0dldEpzb25TU1IgfSBmcm9tIFwiQC9jb25maWcvYXhpb3NcIjtcclxuaW1wb3J0IHsgQVBJX1VSTFMsIGJhc2VVUkwgfSBmcm9tIFwiQC91dGlscy91cmxzXCI7XHJcbmltcG9ydCB7IHdlYnNpdGVSb3V0ZXNMaXN0IH0gZnJvbSBcIkAvaGVscGVycy9yb3V0ZXNMaXN0XCI7XHJcblxyXG5mdW5jdGlvbiBMYXRlc3RKb2JPZmZlcnMoeyBsYW5ndWFnZSB9KSB7XHJcbiAgY29uc3QgT1BUSU9OUyA9IHsgbG9vcDogZmFsc2UsIGFsaWduOiBcInN0YXJ0XCIgfTtcclxuICBjb25zdCBbZW1ibGFSZWZdID0gdXNlRW1ibGFDYXJvdXNlbChPUFRJT05TKTtcclxuICBjb25zdCBbam9iT2ZmZXJzLCBzZXRKb2JPZmZlcnNdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUobnVsbCk7XHJcblxyXG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKTtcclxuICBjb25zdCBbcXVlcnksIHNldFF1ZXJ5XSA9IHVzZVN0YXRlKHtcclxuICAgIHVyZ2VudDogdW5kZWZpbmVkLFxyXG4gIH0pO1xyXG5cclxuICBjb3VudHJpZXMucmVnaXN0ZXJMb2NhbGUoZW5Mb2NhbGUpO1xyXG5cclxuICBjb25zdCBmZXRjaEpvYk9mZmVycyA9IGFzeW5jICgpID0+IHtcclxuICAgIHNldExvYWRpbmcodHJ1ZSk7XHJcbiAgICBzZXRFcnJvcihudWxsKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zR2V0SnNvblNTUi5nZXQoXHJcbiAgICAgICAgYCR7YmFzZVVSTH0ke0FQSV9VUkxTLm9wcG9ydHVuaXR5fS91cmdlbnRgLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgIHBhcmFtczoge1xyXG4gICAgICAgICAgICB1cmdlbnQ6IHF1ZXJ5LnVyZ2VudCxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfVxyXG4gICAgICApO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEgJiYgcmVzcG9uc2UuZGF0YS5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgY29uc3QgZmV0Y2hlZE9mZmVycyA9IHJlc3BvbnNlLmRhdGEubWFwKChvZmZlcikgPT4gKHtcclxuICAgICAgICAgIGlkOiBvZmZlci5faWQsXHJcbiAgICAgICAgICB0aXRsZTogb2ZmZXIudmVyc2lvbnNbbGFuZ3VhZ2VdPy50aXRsZSB8fCBcIlRpdHJlIG5vbiBkaXNwb25pYmxlXCIsXHJcbiAgICAgICAgICBpbmR1c3RyeTogb2ZmZXIuaW5kdXN0cnksXHJcbiAgICAgICAgICBjb3VudHJ5OiBvZmZlci5jb3VudHJ5LFxyXG4gICAgICAgICAgZGF0ZU9mRXhwaXJhdGlvbjogb2ZmZXIuZGF0ZU9mRXhwaXJhdGlvbixcclxuICAgICAgICAgIG1pbkV4cGVyaWVuY2U6IG9mZmVyLm1pbkV4cGVyaWVuY2UsXHJcbiAgICAgICAgICBtYXhFeHBlcmllbmNlOiBvZmZlci5tYXhFeHBlcmllbmNlLFxyXG4gICAgICAgICAgZXhpc3RpbmdMYW5ndWFnZXM6IG9mZmVyLmV4aXN0aW5nTGFuZ3VhZ2VzLFxyXG4gICAgICAgICAgcmVmZXJlbmNlOiBvZmZlci5yZWZlcmVuY2UsXHJcbiAgICAgICAgICB1cmdlbnQ6IG9mZmVyLnVyZ2VudCxcclxuICAgICAgICAgIHVybDogb2ZmZXIudmVyc2lvbnNbbGFuZ3VhZ2VdPy51cmwsXHJcbiAgICAgICAgfSkpO1xyXG4gICAgICAgIHNldEpvYk9mZmVycyhmZXRjaGVkT2ZmZXJzKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBzZXRKb2JPZmZlcnMoW10pO1xyXG4gICAgICAgIHNldEVycm9yKFwiTm8gam9iIG9mZmVycyBhdmFpbGFibGUgYXQgdGhlIG1vbWVudC5cIik7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgam9iIG9mZmVyczpcIiwgZXJyKTtcclxuICAgICAgc2V0RXJyb3IoXCJGYWlsZWQgdG8gZmV0Y2ggam9iIG9mZmVyc1wiKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBmZXRjaEpvYk9mZmVycygpO1xyXG4gIH0sIFtxdWVyeV0pO1xyXG5cclxuICBjb25zdCBvbkNsaWNrRmlsdGVyID0gKGRhdGEpID0+IHtcclxuICAgIGlmIChkYXRhLnVyZ2VudCAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgIHNldFF1ZXJ5KChwcmV2KSA9PiAoe1xyXG4gICAgICAgIC4uLnByZXYsXHJcbiAgICAgICAgdXJnZW50OiBkYXRhLnVyZ2VudCxcclxuICAgICAgfSkpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0UXVlcnkoKHByZXYpID0+ICh7XHJcbiAgICAgICAgLi4ucHJldixcclxuICAgICAgICB1cmdlbnQ6IHVuZGVmaW5lZCxcclxuICAgICAgfSkpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8Q29udGFpbmVyIGlkPVwibGF0ZXN0LW9mZmVyc1wiIGNsYXNzTmFtZT1cImN1c3RvbS1tYXgtd2lkdGhcIj5cclxuICAgICAgPGgyIGNsYXNzTmFtZT1cImhlYWRpbmctaDEgdGV4dC1jZW50ZXJcIj57dChcImhvbWVQYWdlOnMzOnRpdGxlXCIpfTwvaDI+XHJcbiAgICAgIDxkaXYgaWQ9XCJmaWx0ZXItYnRuc1wiPlxyXG4gICAgICAgIDxDdXN0b21CdXR0b25cclxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uQ2xpY2tGaWx0ZXIoeyB1cmdlbnQ6ICFxdWVyeS51cmdlbnQgfSl9XHJcbiAgICAgICAgICB0ZXh0PXt0KFwiaG9tZVBhZ2U6czM6YnR1XCIpfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPXtgJHtcclxuICAgICAgICAgICAgcXVlcnkudXJnZW50ID8gXCJidG4gYnRuLWZpbHRlciBzZWxlY3RlZFwiIDogXCJidG4gYnRuLW91dGxpbmVkXCJcclxuICAgICAgICAgIH1gfVxyXG4gICAgICAgIC8+XHJcbiAgICAgICAgPEN1c3RvbUJ1dHRvblxyXG4gICAgICAgICAgdGV4dD17dChcImhvbWVQYWdlOnMzOmJ0bGFzdFwiKX1cclxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uQ2xpY2tGaWx0ZXIoeyB1cmdlbnQ6IHVuZGVmaW5lZCB9KX1cclxuICAgICAgICAgIGNsYXNzTmFtZT17YCR7XHJcbiAgICAgICAgICAgIHF1ZXJ5LnVyZ2VudCA9PT0gdW5kZWZpbmVkXHJcbiAgICAgICAgICAgICAgPyBcImJ0biBidG4tZmlsdGVyIHNlbGVjdGVkXCJcclxuICAgICAgICAgICAgICA6IFwiYnRuIGJ0bi1vdXRsaW5lZFwiXHJcbiAgICAgICAgICB9YH1cclxuICAgICAgICAvPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImVtYmxhXCIgaWQ9e1wiam9ic19fc2xpZGVyXCJ9PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZW1ibGFfX3ZpZXdwb3J0XCIgcmVmPXtlbWJsYVJlZn0+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImVtYmxhX19jb250YWluZXJcIj5cclxuICAgICAgICAgICAge2pvYk9mZmVycy5tYXAoKG9wcG9ydHVuaXR5LCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgIDxPcHBvcnR1bml0eUl0ZW1CeUdyaWRcclxuICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XHJcbiAgICAgICAgICAgICAgICBvcHBvcnR1bml0eT17b3Bwb3J0dW5pdHl9XHJcbiAgICAgICAgICAgICAgICBsYW5ndWFnZT17XCJlblwifVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvc2VjdGlvbj5cclxuXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2VudGVyLWRpdlwiPlxyXG4gICAgICAgIDxDdXN0b21CdXR0b25cclxuICAgICAgICAgIHRleHQ9e3QoXCJob21lUGFnZTpzMzphbGxcIil9XHJcbiAgICAgICAgICBsaW5rPXtgLyR7d2Vic2l0ZVJvdXRlc0xpc3Qub3Bwb3J0dW5pdGllcy5yb3V0ZX1gfVxyXG4gICAgICAgICAgLy8gb25DbGljaz17KCkgPT4gb25DbGlja0ZpbHRlcih7IHVyZ2VudDogdW5kZWZpbmVkIH0pfVxyXG4gICAgICAgICAgY2xhc3NOYW1lPXtcImJ0biBidG4tZmlsbGVkXCJ9XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L0NvbnRhaW5lcj5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBMYXRlc3RKb2JPZmZlcnM7XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNvbnRhaW5lciIsInVzZVRyYW5zbGF0aW9uIiwidXNlRW1ibGFDYXJvdXNlbCIsImNvdW50cmllcyIsImVuTG9jYWxlIiwiT3Bwb3J0dW5pdHlJdGVtQnlHcmlkIiwiQ3VzdG9tQnV0dG9uIiwiYXhpb3NHZXRKc29uU1NSIiwiQVBJX1VSTFMiLCJiYXNlVVJMIiwid2Vic2l0ZVJvdXRlc0xpc3QiLCJMYXRlc3RKb2JPZmZlcnMiLCJsYW5ndWFnZSIsIk9QVElPTlMiLCJsb29wIiwiYWxpZ24iLCJlbWJsYVJlZiIsImpvYk9mZmVycyIsInNldEpvYk9mZmVycyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInQiLCJxdWVyeSIsInNldFF1ZXJ5IiwidXJnZW50IiwidW5kZWZpbmVkIiwicmVnaXN0ZXJMb2NhbGUiLCJmZXRjaEpvYk9mZmVycyIsInJlc3BvbnNlIiwiZ2V0Iiwib3Bwb3J0dW5pdHkiLCJwYXJhbXMiLCJkYXRhIiwibGVuZ3RoIiwiZmV0Y2hlZE9mZmVycyIsIm1hcCIsIm9mZmVyIiwiaWQiLCJfaWQiLCJ0aXRsZSIsInZlcnNpb25zIiwiaW5kdXN0cnkiLCJjb3VudHJ5IiwiZGF0ZU9mRXhwaXJhdGlvbiIsIm1pbkV4cGVyaWVuY2UiLCJtYXhFeHBlcmllbmNlIiwiZXhpc3RpbmdMYW5ndWFnZXMiLCJyZWZlcmVuY2UiLCJ1cmwiLCJlcnIiLCJjb25zb2xlIiwib25DbGlja0ZpbHRlciIsInByZXYiLCJjbGFzc05hbWUiLCJoMiIsImRpdiIsIm9uQ2xpY2siLCJ0ZXh0Iiwic2VjdGlvbiIsInJlZiIsImluZGV4IiwibGluayIsIm9wcG9ydHVuaXRpZXMiLCJyb3V0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/LatestJobOffers.jsx\n"));

/***/ })

});