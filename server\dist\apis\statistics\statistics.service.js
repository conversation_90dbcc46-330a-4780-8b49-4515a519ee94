"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatisticsService = void 0;
const constants_1 = require("@/utils/helpers/constants");
const candidat_model_1 = __importDefault(require("../candidat/candidat.model"));
const opportunity_application_model_1 = __importDefault(require("../opportunity/model/opportunity.application.model"));
const opportunity_model_1 = __importDefault(require("../opportunity/model/opportunity.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const article_model_1 = __importDefault(require("../article/article.model"));
const files_model_1 = __importDefault(require("../storage/files.model"));
const commentaire_model_1 = require("../article/commentaire/commentaire.model");
const contact_model_1 = __importDefault(require("../contact/contact.model"));
const login_records_model_1 = __importDefault(require("../auth/login-records/login-records.model"));
const newsletter_model_1 = __importDefault(require("../newsletter/newsletter.model"));
const messages_1 = require("@/utils/helpers/messages");
class StatisticsService {
    constructor() {
        this.Application = opportunity_application_model_1.default;
        this.Candidate = candidat_model_1.default;
        this.Opportunity = opportunity_model_1.default;
        this.Article = article_model_1.default;
        this.File = files_model_1.default;
        this.Comment = commentaire_model_1.CommentModel;
        this.Contact = contact_model_1.default;
        this.LoginRecord = login_records_model_1.default;
        this.NewsLetter = newsletter_model_1.default;
    }
    async getTotalStats() {
        const totalCandidates = await this.Candidate.countDocuments();
        const totalArticles = await this.Article.countDocuments();
        const totalApplications = await this.Application.countDocuments();
        const totalComments = await this.Comment.countDocuments();
        const totalContacts = await this.Contact.countDocuments();
        const totalOpportunity = await this.Opportunity.countDocuments();
        const totalLogins = await this.LoginRecord.countDocuments();
        const totalFilesUploaded = await this.File.countDocuments({ resource: 'candidates' });
        return {
            totalCandidates,
            totalArticles,
            totalApplications,
            totalComments,
            totalContacts,
            totalOpportunity,
            totalLogins,
            totalFilesUploaded,
        };
    }
    async getCommentsStatistics(queries) {
        const { dateFrom, dateTo, approve, categories } = queries;
        const matchConditions = {};
        let to = new Date(dateTo);
        if (dateTo) {
            to.setHours(23, 59, 59, 999);
        }
        if (dateFrom || dateTo) {
            const from = new Date(dateFrom);
            matchConditions['createdAt'] = { $gte: from, $lte: to };
        }
        if (approve) {
            matchConditions['approved'] = approve === 'true' ? true : false;
        }
        let parsedCategories = [];
        if (categories) {
            parsedCategories = categories.split(',').map((category) => category.trim());
        }
        const pipeline = [
            { $match: matchConditions },
            {
                $lookup: {
                    from: 'articles',
                    localField: 'article',
                    foreignField: '_id',
                    as: 'articleDetails',
                },
            },
            { $unwind: '$articleDetails' },
            { $unwind: '$articleDetails.versions' },
            { $unwind: '$articleDetails.versions.category' },
            {
                $lookup: {
                    from: 'categories',
                    localField: 'articleDetails.versions.category',
                    foreignField: 'versionscategory._id',
                    as: 'categoryDetails',
                },
            },
            { $unwind: '$categoryDetails' },
            { $unwind: '$categoryDetails.versionscategory' },
            {
                $match: {
                    'categoryDetails.versionscategory.language': 'en',
                },
            },
        ];
        if (parsedCategories.length > 0) {
            pipeline.push({
                $match: {
                    'categoryDetails.versionscategory.name': { $in: parsedCategories },
                },
            });
        }
        pipeline.push({
            $group: {
                _id: {
                    commentId: '$_id',
                    category: '$categoryDetails.versionscategory.name',
                },
            },
        }, {
            $group: {
                _id: '$_id.category',
                total: { $sum: 1 },
            },
        }, {
            $project: {
                _id: 0,
                category: '$_id',
                total: 1,
            },
        }, { $sort: { total: -1 } });
        const result = await this.Comment.aggregate(pipeline);
        return result;
    }
    async getApplicationsStats(queries) {
        const { status, startDate, endDate, opportunity, barChart } = queries;
        const matchConditions = {};
        const statuses = Object.values(constants_1.ApplicationStatus);
        if (barChart) {
            if (startDate || endDate) {
                const from = new Date(startDate);
                const to = new Date(endDate);
                matchConditions['createdAt'] = { $gte: from, $lte: to };
            }
            return await this.Application.aggregate([
                { $match: matchConditions },
                {
                    $group: {
                        _id: {
                            year: { $year: '$createdAt' },
                            month: { $month: '$createdAt' },
                        },
                        applications: { $sum: 1 },
                    },
                },
                {
                    $sort: { '_id.year': 1, '_id.month': 1 },
                },
                {
                    $project: {
                        _id: 0,
                        year: '$_id.year',
                        month: '$_id.month',
                        applications: 1,
                    },
                },
            ]);
        }
        if (status) {
            if (!statuses.includes(status))
                throw new http_exception_1.default(400, messages_1.MESSAGES.STATISTICS.INVALID_STATUS + status);
            matchConditions['status'] = status;
        }
        if (opportunity) {
            const foundOpening = await this.Opportunity.findById(opportunity);
            if (!foundOpening)
                throw new http_exception_1.default(404, messages_1.MESSAGES.OPPORTUNITY.NOT_FOUND);
            matchConditions['opportunity'] = foundOpening._id;
        }
        if (startDate || endDate) {
            matchConditions['createdAt'] = {};
            if (startDate)
                matchConditions['createdAt'].$gte = typeof startDate === 'string' ? new Date(startDate) : startDate;
            if (endDate)
                matchConditions['createdAt'].$lte = typeof endDate === 'string' ? new Date(endDate) : endDate;
        }
        const applicationsStats = await this.Application.aggregate([
            {
                $match: matchConditions,
            },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                },
            },
            {
                $project: {
                    status: '$_id',
                    totalApplications: '$count',
                    _id: 0,
                },
            },
            {
                $sort: { totalApplications: -1 },
            },
        ]);
        const statusMap = applicationsStats.reduce((acc, stat) => {
            acc[stat.status] = stat.totalApplications;
            return acc;
        }, {});
        return statuses.map(status => ({
            status,
            totalApplications: statusMap[status] || 0,
        }));
    }
    async getArticleStats(queries) {
        const { visibility, startDate, endDate, archived, article, language, barChart } = queries;
        const matchConditions = {
            'versions.isArchived': false,
        };
        const visibilities = Object.values(constants_1.Visibility).map(visibility => visibility.toLowerCase());
        if (barChart) {
            if (startDate || endDate) {
                const from = new Date(startDate);
                const to = new Date(endDate);
                matchConditions['versions.createdAt'] = { $gte: from, $lte: to };
            }
            return await this.Article.aggregate([
                { $match: matchConditions },
                { $unwind: '$versions' },
                {
                    $match: { 'versions.language': language === 'fr' ? 'fr' : 'en' },
                },
                {
                    $group: {
                        _id: {
                            year: { $year: '$versions.createdAt' },
                            month: { $month: '$versions.createdAt' },
                        },
                        articles: { $sum: 1 },
                    },
                },
                {
                    $sort: { '_id.year': 1, '_id.month': 1 },
                },
                {
                    $project: {
                        _id: 0,
                        year: '$_id.year',
                        month: '$_id.month',
                        articles: 1,
                    },
                },
            ]);
        }
        if (visibility) {
            if (!visibilities.includes(visibility.toLowerCase()))
                throw new http_exception_1.default(400, `Invalid visibility type '${visibility}'`);
            matchConditions['versions.visibility'] = RegExp(visibility, 'i');
        }
        if (startDate || endDate) {
            matchConditions['versions.publishDate'] = {};
            if (startDate)
                matchConditions['versions.publishDate'].$gte = typeof startDate === 'string' ? new Date(startDate) : startDate;
            if (endDate)
                matchConditions['versions.publishDate'].$lte = typeof endDate === 'string' ? new Date(endDate) : endDate;
        }
        if (archived === 'true')
            matchConditions['versions.isArchived'] = true;
        const articlesStats = await this.Article.aggregate([
            {
                $match: matchConditions,
            },
            {
                $unwind: '$versions',
            },
            {
                $group: {
                    _id: '$versions.visibility',
                    totalArticles: { $sum: 1 },
                    totalComments: { $sum: '$totalCommentaires' },
                },
            },
            {
                $project: {
                    visibility: '$_id',
                    totalArticles: 1,
                    totalComments: 1,
                    _id: 0,
                },
            },
            {
                $sort: { totalArticles: -1 },
            },
        ]);
        const statusMap = articlesStats.reduce((acc, stat) => {
            acc[stat.visibility] = {
                totalArticles: stat.totalArticles,
                totalComments: stat.totalComments,
            };
            return acc;
        }, {});
        return Object.values(constants_1.Visibility).map(visibility => ({
            visibility,
            totalArticles: Math.ceil((statusMap[visibility]?.totalArticles || 0) / 2),
            totalComments: statusMap[visibility]?.totalComments || 0,
        }));
    }
    async getOpportunitiesStats(queries) {
        const { startDate, endDate, visibility, archived, opportunityType, industry, urgent, country, barChart, language } = queries;
        const visibilities = Object.values(constants_1.Visibility).map(visibility => visibility.toLowerCase());
        const opportunityTypes = Object.values(constants_1.OpportunityType).map(opportunityType => opportunityType.toLowerCase());
        const industries = Object.values(constants_1.Industry).map(industry => industry.toLowerCase());
        const matchConditions = {
            'OpportunityVersions.isArchived': false,
        };
        if (barChart) {
            if (startDate || endDate) {
                const from = new Date(startDate);
                const to = new Date(endDate);
                matchConditions['OpportunityVersions.createdAt'] = { $gte: from, $lte: to };
            }
            return await this.Opportunity.aggregate([
                { $match: matchConditions },
                { $unwind: '$OpportunityVersions' },
                {
                    $match: { 'OpportunityVersions.language': language === 'fr' ? 'fr' : 'en' },
                },
                {
                    $group: {
                        _id: {
                            year: { $year: '$OpportunityVersions.createdAt' },
                            month: { $month: '$OpportunityVersions.createdAt' },
                        },
                        opportunities: { $sum: 1 },
                    },
                },
                {
                    $sort: { '_id.year': 1, '_id.month': 1 },
                },
                {
                    $project: {
                        _id: 0,
                        year: '$_id.year',
                        month: '$_id.month',
                        opportunities: 1,
                    },
                },
            ]);
        }
        if (visibility) {
            if (!visibilities.includes(visibility.toLowerCase()))
                throw new http_exception_1.default(400, messages_1.MESSAGES.STATISTICS.INVALID_VISIBILITY);
            matchConditions['OpportunityVersions.visibility'] = RegExp(visibility, 'i');
        }
        if (archived)
            matchConditions['OpportunityVersions.isArchived'] = true;
        if (opportunityType) {
            if (!opportunityTypes.includes(opportunityType.toLowerCase()))
                throw new http_exception_1.default(400, messages_1.MESSAGES.STATISTICS.INVALID_OPPORTUNITY_TYPE);
            matchConditions['opportunityType'] = RegExp(opportunityType, 'i');
        }
        if (industry) {
            if (!industries.includes(industry.toLowerCase()))
                throw new http_exception_1.default(400, messages_1.MESSAGES.STATISTICS.INVALID_INDUSTRY);
            matchConditions['industry'] = RegExp(industry, 'i');
        }
        if (urgent)
            urgent === 'true' ? (matchConditions['urgent'] = true) : (matchConditions['urgent'] = false);
        if (country)
            matchConditions['country'] = RegExp(country, 'i');
        if (startDate || endDate) {
            matchConditions['OpportunityVersions.createdAt'] = {};
            if (startDate)
                matchConditions['OpportunityVersions.createdAt'].$gte = typeof startDate === 'string' ? new Date(startDate) : startDate;
            if (endDate)
                matchConditions['OpportunityVersions.createdAt'].$lte = typeof endDate === 'string' ? new Date(endDate) : endDate;
        }
        const opportunityStats = await this.Opportunity.aggregate([
            {
                $match: matchConditions,
            },
            {
                $unwind: '$OpportunityVersions',
            },
            {
                $group: {
                    _id: '$OpportunityVersions.visibility',
                    totalOpportunities: { $sum: 1 },
                },
            },
            {
                $project: {
                    visibility: '$_id',
                    totalOpportunities: 1,
                    _id: 0,
                },
            },
            {
                $sort: { totalOpportunities: -1 },
            },
        ]);
        const statusMap = opportunityStats.reduce((acc, stat) => {
            acc[stat.visibility] = {
                totalOpportunities: stat.totalOpportunities,
            };
            return acc;
        }, {});
        return Object.values(constants_1.Visibility).map(visibility => ({
            visibility,
            totalOpportunities: Math.ceil((statusMap[visibility]?.totalOpportunities || 0) / 2),
        }));
    }
    async getResourcesStats(resource, queries) {
        const { dateFrom, dateTo } = queries;
        const matchConditions = { resource };
        if (dateFrom && dateTo) {
            const from = new Date(dateFrom);
            let to = new Date(dateTo);
            to.setHours(23, 59, 59, 999);
            matchConditions['createdAt'] = { $gte: from, $lte: to };
        }
        return await this.File.aggregate([
            { $match: matchConditions },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                    },
                    resumes: { $sum: 1 },
                },
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 },
            },
            {
                $project: {
                    _id: 0,
                    year: '$_id.year',
                    month: '$_id.month',
                    resumes: 1,
                },
            },
        ]);
    }
    async getUserActivityStats(queries) {
        const { dateFrom, dateTo } = queries;
        const matchConditions = {};
        if (dateFrom || dateTo) {
            const from = new Date(dateFrom);
            let to = new Date(dateTo);
            to.setHours(23, 59, 59, 999);
            matchConditions['createdAt'] = { $gte: from, $lte: to };
        }
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        const loginStats = await this.LoginRecord.aggregate([
            { $match: matchConditions },
            {
                $group: {
                    _id: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' }, type: '$type' },
                    count: { $sum: 1 },
                },
            },
        ]);
        const fileStats = await this.File.aggregate([
            { $match: { ...matchConditions, resource: 'candidates' } },
            {
                $group: {
                    _id: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' } },
                    resumes: { $sum: 1 },
                },
            },
        ]);
        const applicationStats = await this.Application.aggregate([
            { $match: matchConditions },
            {
                $group: {
                    _id: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' } },
                    applications: { $sum: 1 },
                },
            },
        ]);
        const combinedStats = {};
        loginStats.forEach(({ _id, count }) => {
            const key = `${_id.year}-${_id.month}`;
            if (!combinedStats[key])
                combinedStats[key] = { year: _id.year, month: _id.month, login: 0, register: 0 };
            if (_id.type === 'login')
                combinedStats[key].login += count;
            if (_id.type === 'register')
                combinedStats[key].register += count;
        });
        fileStats.forEach(({ _id, resumes }) => {
            const key = `${_id.year}-${_id.month}`;
            if (!combinedStats[key])
                combinedStats[key] = { year: _id.year, month: _id.month, login: 0, register: 0 };
            combinedStats[key].resumes = resumes;
        });
        applicationStats.forEach(({ _id, applications }) => {
            const key = `${_id.year}-${_id.month}`;
            if (!combinedStats[key])
                combinedStats[key] = { year: _id.year, month: _id.month, login: 0, register: 0 };
            combinedStats[key].applications = applications;
        });
        const result = Object.values(combinedStats)
            .map((stat) => ({
            login: stat.login || 0,
            register: stat.register || 0,
            resumes: stat.resumes || 0,
            applications: stat.applications || 0,
            month: stat.month,
            monthNumber: stat.month,
            monthName: monthNames[stat.month - 1],
            year: stat.year,
        }))
            .sort((a, b) => a.year - b.year || a.monthNumber - b.monthNumber)
            .map((stat) => ({
            login: stat.login,
            register: stat.register,
            resumes: stat.resumes,
            applications: stat.applications,
            month: stat.monthName + '\n' + stat.year,
            year: stat.year,
        }));
        return result;
    }
    async getLoginsRecordStats(queries) {
        const { dateFrom, dateTo, user, type } = queries;
        const matchConditions = {};
        if (dateFrom || dateTo) {
            const from = new Date(dateFrom);
            let to = new Date(dateTo);
            to.setHours(23, 59, 59, 999);
            matchConditions['createdAt'] = { $gte: from, $lte: to };
        }
        if (type) {
            if (!['register', 'login'].includes(type.toLowerCase()))
                throw new http_exception_1.default(400, messages_1.MESSAGES.STATISTICS.INVALID_TYPE + type);
            matchConditions['type'] = RegExp(type, 'i');
        }
        if (user)
            matchConditions['user'] = user;
        return await this.LoginRecord.aggregate([
            { $match: matchConditions },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                    },
                    logins: { $sum: 1 },
                },
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 },
            },
            {
                $project: {
                    _id: 0,
                    year: '$_id.year',
                    month: '$_id.month',
                    logins: 1,
                },
            },
        ]);
    }
    async getContactRecordStats(queries) {
        const { dateFrom, dateTo, type } = queries;
        const matchConditions = {};
        if (dateFrom || dateTo) {
            const from = new Date(dateFrom);
            let to = new Date(dateTo);
            to.setHours(23, 59, 59, 999);
            matchConditions['createdAt'] = { $gte: from, $lte: to };
        }
        if (type) {
            if (![
                'mainservice',
                'payrollservice',
                'consultingservice',
                'technicalassistanceservice',
                'aisourcingservice',
                'directhiringservice',
                'joinus',
                'countrycontact',
            ].includes(type.toLowerCase()))
                throw new http_exception_1.default(400, messages_1.MESSAGES.STATISTICS.INVALID_TYPE);
            matchConditions['type'] = RegExp(type, 'i');
        }
        return await this.Contact.aggregate([
            { $match: matchConditions },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                    },
                    contacts: { $sum: 1 },
                },
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 },
            },
            {
                $project: {
                    _id: 0,
                    year: '$_id.year',
                    month: '$_id.month',
                    contacts: 1,
                },
            },
        ]);
    }
    async getNewsLettersRecordStats(queries) {
        const { dateFrom, dateTo } = queries;
        const matchConditions = {};
        if (dateFrom || dateTo) {
            const from = new Date(dateFrom);
            let to = new Date(dateTo);
            to.setHours(23, 59, 59, 999);
            matchConditions['createdAt'] = { $gte: from, $lte: to };
        }
        return await this.NewsLetter.aggregate([
            { $match: matchConditions },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                    },
                    newsletters: { $sum: 1 },
                },
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 },
            },
            {
                $project: {
                    _id: 0,
                    year: '$_id.year',
                    month: '$_id.month',
                    newsletters: 1,
                },
            },
        ]);
    }
    async getPlatformActivityStats(queries) {
        const { dateFrom, dateTo, language } = queries;
        const from = dateFrom ? new Date(dateFrom) : null;
        let to = new Date(dateTo);
        to.setHours(23, 59, 59, 999);
        const dateMatch = from && to ? { $gte: from, $lte: to } : null;
        const baseMatch = (createdAtField) => (dateMatch ? { [createdAtField]: dateMatch } : {});
        const [newsletterStats, contactStats, articleStats, opportunityStats] = await Promise.all([
            this.NewsLetter.aggregate([
                { $match: baseMatch('createdAt') },
                {
                    $group: {
                        _id: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' } },
                        newsletters: { $sum: 1 },
                    },
                },
            ]),
            this.Contact.aggregate([
                { $match: baseMatch('createdAt') },
                {
                    $group: {
                        _id: { year: { $year: '$createdAt' }, month: { $month: '$createdAt' } },
                        contacts: { $sum: 1 },
                    },
                },
            ]),
            this.Article.aggregate([
                { $unwind: '$versions' },
                {
                    $match: {
                        'versions.language': language === 'fr' ? 'fr' : 'en',
                        'versions.visibility': 'Public',
                        ...baseMatch('versions.createdAt'),
                    },
                },
                {
                    $group: {
                        _id: { year: { $year: '$versions.createdAt' }, month: { $month: '$versions.createdAt' } },
                        articles: { $sum: 1 },
                    },
                },
            ]),
            this.Opportunity.aggregate([
                { $unwind: '$OpportunityVersions' },
                { $match: { 'OpportunityVersions.language': language === 'fr' ? 'fr' : 'en', ...baseMatch('OpportunityVersions.createdAt') } },
                {
                    $group: {
                        _id: { year: { $year: '$OpportunityVersions.createdAt' }, month: { $month: '$OpportunityVersions.createdAt' } },
                        opportunities: { $sum: 1 },
                    },
                },
            ]),
        ]);
        const normalizeData = (stats, key) => stats.map(item => ({
            year: item._id.year,
            month: item._id.month,
            [key]: item[key],
        }));
        const newsletters = normalizeData(newsletterStats, 'newsletters');
        const contacts = normalizeData(contactStats, 'contacts');
        const articles = normalizeData(articleStats, 'articles');
        const opportunities = normalizeData(opportunityStats, 'opportunities');
        const mergedStats = {};
        const mergeIntoStats = (data, key) => {
            data.forEach(({ year, month, ...counts }) => {
                const keyId = `${year}-${month}`;
                if (!mergedStats[keyId]) {
                    mergedStats[keyId] = { year, month, newsletters: 0, contacts: 0, articles: 0, opportunities: 0 };
                }
                mergedStats[keyId][key] = counts[key] || 0;
            });
        };
        mergeIntoStats(newsletters, 'newsletters');
        mergeIntoStats(contacts, 'contacts');
        mergeIntoStats(articles, 'articles');
        mergeIntoStats(opportunities, 'opportunities');
        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        const result = Object.values(mergedStats)
            .sort((a, b) => a.year - b.year || a.month - b.month)
            .map(stat => ({
            ...stat,
            month: monthNames[stat.month - 1] + '\n' + stat.year,
        }));
        return result;
    }
}
exports.StatisticsService = StatisticsService;
//# sourceMappingURL=statistics.service.js.map