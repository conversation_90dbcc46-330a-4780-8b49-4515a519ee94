"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_service_1 = __importDefault(require("./auth.service"));
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const auth_validations_1 = require("./auth.validations");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const upload_middleware_1 = require("../../middlewares/upload.middleware");
const messages_1 = require("@/utils/helpers/messages");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const passport_1 = __importDefault(require("./../../utils/config/passport"));
const services_1 = require("@/utils/services");
const user_service_1 = __importDefault(require("../user/services/user.service"));
const login_records_model_1 = __importDefault(require("./login-records/login-records.model"));
class AuthController {
    constructor() {
        this.path = '/auth';
        this.router = (0, express_1.Router)();
        this.AuthService = new auth_service_1.default();
        this.userService = new user_service_1.default();
        this.LoginRecord = login_records_model_1.default;
        this.signUp = async (request, response, next) => {
            try {
                const signUpData = request.body;
                await this.AuthService.signUp(signUpData);
                response.status(201).send({
                    message: messages_1.MESSAGES.USER.USER_CREATED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.checkUserExistence = async (request, response, next) => {
            const { token } = request.query;
            if (!token) {
                return response.status(400).json({ error: 'Token est requis' });
            }
            try {
                const { exist } = await this.AuthService.checkUserExistence(String(token));
                return response.status(200).json({ exist });
            }
            catch (error) {
                return response.status(500).json({});
            }
        };
        this.activateAccount = async (request, response, next) => {
            const { token } = request.params;
            try {
                if (!token)
                    return next(new http_exception_1.default(400, 'Token is missing in the request.'));
                await this.AuthService.activateAccount(token);
                response.send({ message: 'Account activated successfully.' });
            }
            catch (error) {
                next(error);
            }
        };
        this.Update = async (request, response, next) => {
            try {
                const userId = request.params.userId;
                const userData = request.body;
                await this.AuthService.updateUser(userId, userData);
                response.status(200).send({
                    message: messages_1.MESSAGES.USER.USER_UPDATED,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.signIn = async (request, response, next) => {
            try {
                const signInData = request.body;
                const { user, accessToken, refreshToken } = await this.AuthService.signIn(signInData);
                response.cookie('accessToken', accessToken, {
                    maxAge: Number(process.env.COOKIE_MAX_AGE),
                    httpOnly: true,
                    secure: process.env.NODE_ENV !== 'prod' ? false : true,
                });
                response.cookie('refreshToken', refreshToken, {
                    maxAge: Number(process.env.COOKIE_MAX_AGE),
                    httpOnly: true,
                    secure: process.env.NODE_ENV !== 'prod' ? false : true,
                });
                request.user = user;
                response.send({ user, accessToken, refreshToken });
            }
            catch (error) {
                next(error);
            }
        };
        this.logout = async (request, response, next) => {
            try {
                response.clearCookie('refreshToken');
                response.clearCookie('accessToken');
                response.send({
                    message: messages_1.MESSAGES.AUTH.LOGOUT,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.forgotPassword = async (request, response, next) => {
            try {
                const email = request.body.email;
                await this.AuthService.forgotPassword(email);
                response.status(200).send({
                    message: messages_1.MESSAGES.AUTH.RESET + email,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.resetPassword = async (request, response, next) => {
            try {
                const { token } = request.params;
                const userInfo = request.body;
                await this.AuthService.resetPassword(userInfo, token);
                response.status(200).send({
                    message: messages_1.MESSAGES.AUTH.RESET_SUCCESSFUL,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.googleAuth = async (request, response, next) => {
            try {
                passport_1.default.authenticate('google', {
                    scope: ['email', 'profile'],
                    state: request.query.state,
                })(request, response, next);
            }
            catch (error) {
                next(error);
            }
        };
        this.googleAuthCallback = async (req, response, next) => {
            try {
                passport_1.default.authenticate('google', async (err, user) => {
                    if (err)
                        return response.redirect(process.env.ERROR_REDIRECT + encodeURIComponent(err.message));
                    if (!user)
                        return response.redirect(process.env.ERROR_REDIRECT + 'Unauthorized');
                    const state = req.query.state ? JSON.parse(req.query.state) : {};
                    const { isSignup, redirection } = state;
                    req.logIn(user, async (err) => {
                        if (err)
                            return next(err);
                        if (isSignup === true) {
                            return response.redirect(`${process.env.MESSAGE_REDIRECT}?success=${encodeURIComponent('Account created. Please login!')}`);
                        }
                        const payload = { _id: user._id, roles: user.roles, profilePicture: user.profilePicture, candidate: user?.candidate };
                        const accessToken = await services_1.auth.generateToken(payload, String(process.env.ACCESS_TOKEN_PRIVATE_KEY), String(process.env.ACCESS_TOKEN_TIME));
                        const refreshToken = await services_1.auth.generateToken(payload, String(process.env.REFRESH_TOKEN_PRIVATE_KEY), String(process.env.REFRESH_TOKEN_TIME));
                        response.cookie('accessToken', accessToken, {
                            maxAge: Number(process.env.COOKIE_MAX_AGE),
                            httpOnly: true,
                            secure: process.env.NODE_ENV === 'prod',
                            sameSite: process.env.NODE_ENV === 'prod' ? 'none' : 'lax',
                        });
                        response.cookie('refreshToken', refreshToken, {
                            maxAge: Number(process.env.COOKIE_MAX_AGE),
                            httpOnly: true,
                            secure: process.env.NODE_ENV === 'prod',
                            sameSite: process.env.NODE_ENV === 'prod' ? 'none' : 'lax',
                        });
                        await this.LoginRecord.create({ user: user._id, type: 'login' });
                        if (redirection)
                            return response.redirect(redirection);
                        return response.redirect(process.env.MESSAGE_REDIRECT);
                    });
                })(req, response, next);
            }
            catch (error) {
                next(error);
            }
        };
        this.microsoftAuth = async (request, response, next) => {
            try {
                passport_1.default.authenticate('microsoft', {
                    prompt: 'select_account',
                    state: request.query.state,
                })(request, response, next);
            }
            catch (error) {
                next(error);
            }
        };
        this.microsoftAuthCallback = async (req, response, next) => {
            try {
                passport_1.default.authenticate('microsoft', async (err, user) => {
                    if (err)
                        return response.redirect(process.env.ERROR_REDIRECT + encodeURIComponent(err.message));
                    if (!user)
                        return response.redirect(process.env.ERROR_REDIRECT + 'Unauthorized');
                    const state = req.query.state ? JSON.parse(req.query.state) : {};
                    const { isSignup, redirection } = state;
                    req.logIn(user, async (err) => {
                        if (err)
                            return next(err);
                        if (isSignup === true) {
                            return response.redirect(`${process.env.MESSAGE_REDIRECT}?success=${encodeURIComponent('Account created. Please login!')}`);
                        }
                        const payload = { _id: user._id, roles: user.roles, profilePicture: user.profilePicture, candidate: user?.candidate };
                        const accessToken = await services_1.auth.generateToken(payload, String(process.env.ACCESS_TOKEN_PRIVATE_KEY), String(process.env.ACCESS_TOKEN_TIME));
                        const refreshToken = await services_1.auth.generateToken(payload, String(process.env.REFRESH_TOKEN_PRIVATE_KEY), String(process.env.REFRESH_TOKEN_TIME));
                        response.cookie('accessToken', accessToken, {
                            maxAge: Number(process.env.COOKIE_MAX_AGE),
                            httpOnly: true,
                            secure: process.env.NODE_ENV === 'prod',
                            sameSite: process.env.NODE_ENV === 'prod' ? 'none' : 'lax',
                        });
                        response.cookie('refreshToken', refreshToken, {
                            maxAge: Number(process.env.COOKIE_MAX_AGE),
                            httpOnly: true,
                            secure: process.env.NODE_ENV === 'prod',
                            sameSite: process.env.NODE_ENV === 'prod' ? 'none' : 'lax',
                        });
                        await this.LoginRecord.create({ user: user._id, type: 'login' });
                        if (redirection)
                            return response.redirect(redirection);
                        return response.redirect(process.env.MESSAGE_REDIRECT);
                    });
                })(req, response, next);
            }
            catch (error) {
                next(error);
            }
        };
        this.linkedinAuth = async (request, response, next) => {
            try {
                passport_1.default.authenticate('linkedin', {
                    state: request.query.state,
                })(request, response, next);
            }
            catch (error) {
                next(error);
            }
        };
        this.linkedinAuthCallback = async (req, response, next) => {
            try {
                passport_1.default.authenticate('linkedin', async (err, user) => {
                    if (err)
                        return response.redirect(process.env.ERROR_REDIRECT + encodeURIComponent(err.message));
                    if (!user)
                        return response.redirect(process.env.ERROR_REDIRECT + 'Unauthorized');
                    const state = req.query.state ? JSON.parse(req.query.state) : {};
                    const { isSignup, redirection } = state;
                    req.logIn(user, async (err) => {
                        if (err)
                            return next(err);
                        if (isSignup === true)
                            return response.redirect(`${process.env.MESSAGE_REDIRECT}?success=${encodeURIComponent('Account created. Please login!')}`);
                        const foundUser = await this.userService.getByEmail(user.email);
                        const payload = {
                            _id: foundUser._id,
                            roles: foundUser.roles,
                            profilePicture: foundUser.profilePicture,
                            candidate: foundUser?.candidate,
                        };
                        const accessToken = await services_1.auth.generateToken(payload, String(process.env.ACCESS_TOKEN_PRIVATE_KEY), String(process.env.ACCESS_TOKEN_TIME));
                        const refreshToken = await services_1.auth.generateToken(payload, String(process.env.REFRESH_TOKEN_PRIVATE_KEY), String(process.env.REFRESH_TOKEN_TIME));
                        response.cookie('accessToken', accessToken, {
                            maxAge: Number(process.env.COOKIE_MAX_AGE),
                            httpOnly: true,
                            secure: process.env.NODE_ENV === 'prod',
                            sameSite: process.env.NODE_ENV === 'prod' ? 'none' : 'lax',
                        });
                        response.cookie('refreshToken', refreshToken, {
                            maxAge: Number(process.env.COOKIE_MAX_AGE),
                            httpOnly: true,
                            secure: process.env.NODE_ENV === 'prod',
                            sameSite: process.env.NODE_ENV === 'prod' ? 'none' : 'lax',
                        });
                        if (redirection)
                            return response.redirect(redirection);
                        return response.redirect(process.env.MESSAGE_REDIRECT);
                    });
                })(req, response, next);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}/google`, this.googleAuth);
        this.router.get(`${this.path}/microsoft`, this.microsoftAuth);
        this.router.get(`${this.path}/linkedin`, this.linkedinAuth);
        this.router.get(`${this.path}/google/callback`, this.googleAuthCallback);
        this.router.get(`${this.path}/microsoft/callback`, this.microsoftAuthCallback);
        this.router.get(`${this.path}/linkedin/callback`, this.linkedinAuthCallback);
        this.router.get(`/checkUserExistence`, this.checkUserExistence);
        this.router.post(`${this.path}/signup`, (0, validation_middleware_1.validationMiddleware)(auth_validations_1.signUpSchema), this.signUp);
        this.router.put(`${this.path}/update/:userId`, authentication_middleware_1.default, upload_middleware_1.uploadMiddleware, (0, validation_middleware_1.validationMiddleware)(auth_validations_1.UpdateSchema), this.Update);
        this.router.post(`${this.path}/signin`, (0, validation_middleware_1.validationMiddleware)(auth_validations_1.signInSchema), this.signIn);
        this.router.post(`${this.path}/logout`, authentication_middleware_1.default, this.logout);
        this.router.post(`${this.path}/forgot-password`, (0, validation_middleware_1.validationMiddleware)(auth_validations_1.forgotPasswordSchema), this.forgotPassword);
        this.router.get('/activation/:token', this.activateAccount);
        this.router.post(`${this.path}/reset-password/:token`, this.resetPassword);
    }
}
exports.default = AuthController;
//# sourceMappingURL=auth.controller.js.map