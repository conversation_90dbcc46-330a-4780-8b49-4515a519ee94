"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const client_model_1 = __importDefault(require("../models/client.model"));
const manager_model_1 = __importDefault(require("../models/manager.model"));
const messages_1 = require("@/utils/helpers/messages");
class ClientService {
    constructor() {
        this.Client = client_model_1.default;
        this.Manager = manager_model_1.default;
    }
    async get(id) {
        try {
            const client = await this.Client.findById(id);
            if (!client)
                throw new http_exception_1.default(404, messages_1.MESSAGES.CLIENTS.CLIENT_NOT_FOUND);
            return client;
        }
        catch (error) {
            throw error;
        }
    }
    async getAll(queries) {
        try {
            let totalClients;
            let clients;
            const pageNumber = Number(queries.pageNumber) || 1;
            const pageSize = Number(queries.pageSize) || 10;
            const { name, hidden, paginated } = queries;
            const query = {};
            if (hidden) {
                query['hidden'] = hidden;
            }
            if (name) {
                query['name'] = RegExp(`.*${name}.*`, 'i');
            }
            if (paginated && paginated === 'false') {
                clients = await this.Client.find(query).sort({ name: 1, createdAt: 'desc' });
                totalClients = await this.Client.countDocuments(query);
                return {
                    totalClients,
                    clients,
                };
            }
            else {
                clients = await this.Client.find(query)
                    .sort({ name: 1, createdAt: 'desc' })
                    .skip((pageNumber - 1) * pageSize)
                    .limit(pageSize);
                totalClients = await this.Client.countDocuments(query);
            }
            const totalPages = Math.ceil(totalClients / pageSize);
            return {
                pageNumber,
                pageSize,
                totalPages,
                totalClients,
                clients,
            };
        }
        catch (error) {
            console.error(error);
            throw error;
        }
    }
    async create(clientData, file) {
        try {
            const oldClient = await this.Client.findOne({ name: { $regex: new RegExp(`^${clientData.name}$`, 'i') } });
            if (oldClient)
                throw new http_exception_1.default(409, messages_1.MESSAGES.CLIENTS.ALREADY_EXIST);
            clientData.name = clientData.name.toUpperCase();
            const client = await this.Client.create({ ...clientData, logo: file === undefined ? null : file });
            return client;
        }
        catch (error) {
            console.error(error);
            throw error;
        }
    }
    async createMany(file) {
        const clientsData = JSON.parse(file.buffer.toString());
        for (const client of clientsData) {
            client.name = client.name.toUpperCase();
            const oldClient = await this.Client.findOne({ name: { $regex: new RegExp(`^${client.name}$`, 'i') } });
            if (oldClient) {
                throw new http_exception_1.default(409, messages_1.MESSAGES.CLIENTS.ALREADY_EXIST);
            }
        }
        return await this.Client.create(clientsData);
    }
    async update(id, clientData) {
        try {
            await this.get(id);
            const oldClient = await this.Client.findOne({
                _id: { $ne: id },
                name: { $regex: new RegExp(`^${clientData.name}$`, 'i') },
            });
            if (oldClient) {
                throw new http_exception_1.default(409, messages_1.MESSAGES.CLIENTS.ALREADY_EXIST);
            }
            clientData.name = clientData.name.toUpperCase();
            const client = await this.Client.findByIdAndUpdate(id, clientData, { new: true });
            return client;
        }
        catch (error) {
            console.error(error);
            throw error;
        }
    }
    async delete(id) {
        try {
            await this.get(id);
            await this.Client.findByIdAndDelete(id);
        }
        catch (error) {
            console.error(error);
            throw error;
        }
    }
    async getManagers(clientId) {
        try {
            const managers = await this.Manager.find({ client: clientId }).select('firstName lastName email address phone');
            if (!managers)
                throw new http_exception_1.default(404, 'No Managers Found');
            return managers;
        }
        catch (error) {
            console.error(error);
            throw error;
        }
    }
}
exports.default = ClientService;
//# sourceMappingURL=client.service.js.map