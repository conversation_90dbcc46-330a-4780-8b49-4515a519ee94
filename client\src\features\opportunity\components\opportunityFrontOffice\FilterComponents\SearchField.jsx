import { TextField, InputAdornment } from "@mui/material";
import SvgSearchIcon from "@/assets/images/icons/searchIcon.svg";

const SearchField = ({ value, onChange, placeholder }) => (
  <TextField
    className="input-pentabell"
    autoComplete="off"
    slotProps={{
      input: {
        startAdornment: (
          <InputAdornment position="start">
            <SvgSearchIcon />
          </InputAdornment>
        ),
      },
    }}
    variant="standard"
    type="text"
    value={value || []}
    onChange={onChange}
    placeholder={placeholder}
  />
);

export default SearchField;
