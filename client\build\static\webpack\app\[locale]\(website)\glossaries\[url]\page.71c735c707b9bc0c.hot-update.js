"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _assets_images_icons_instagram_svg__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/assets/images/icons/instagram.svg */ \"(app-pages-browser)/./src/assets/images/icons/instagram.svg\");\n/* harmony import */ var _assets_images_icons_linkedin_svg__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/assets/images/icons/linkedin.svg */ \"(app-pages-browser)/./src/assets/images/icons/linkedin.svg\");\n/* harmony import */ var _assets_images_icons_facebook_svg__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/assets/images/icons/facebook.svg */ \"(app-pages-browser)/./src/assets/images/icons/facebook.svg\");\n/* harmony import */ var _assets_images_icons_x_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/x.svg */ \"(app-pages-browser)/./src/assets/images/icons/x.svg\");\n/* harmony import */ var _PentabellCompanySection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PentabellCompanySection */ \"(app-pages-browser)/./src/features/glossary/component/PentabellCompanySection.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst GlossaryDetails = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function GlossaryDetails(param) {\n    let { article, language, isMobileSSR } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(theme.breakpoints.down(\"sm\")) || isMobileSSR;\n    const isTablet = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(theme.breakpoints.down(\"md\")) || isMobileSSR;\n    const breadcrumbSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"BreadcrumbList\",\n            itemListElement: [\n                {\n                    \"@type\": \"ListItem\",\n                    position: 1,\n                    item: {\n                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                        name: \"Glossary\"\n                    }\n                }\n            ]\n        }), [\n        language\n    ]);\n    const glossaryPath = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>language === \"en\" ? \"/glossaries\" : \"/\".concat(language, \"/glossaries\"), [\n        language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page-details\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    type: \"application/ld+json\",\n                    dangerouslySetInnerHTML: {\n                        __html: JSON.stringify(breadcrumbSchema)\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"container\",\n                    container: true,\n                    columnSpacing: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            item: true,\n                            sm: 12,\n                            md: 12,\n                            lg: 9,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"glossary-header\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"custom-max-width\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"glossary-path\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        locale: language === \"en\" ? \"en\" : \"fr\",\n                                                        href: \"\".concat(glossaryPath, \"/\"),\n                                                        className: \"link\",\n                                                        children: \"Glossary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    (article === null || article === void 0 ? void 0 : article.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SvgArrowRight, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                                lineNumber: 72,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"word\",\n                                                                children: article.word\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                                lineNumber: 73,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"letter\",\n                                                children: article === null || article === void 0 ? void 0 : article.letter\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"custom-max-width\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            htmlContent: article === null || article === void 0 ? void 0 : article.content\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PentabellCompanySection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        isMobile && isTablet && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"glossary-social-media-icons\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_facebook_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_linkedin_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_x_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_instagram_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        !(isMobile && isTablet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            item: true,\n                            sm: 12,\n                            md: 12,\n                            lg: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"custom-max-width\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glossary-social-media-icons\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_facebook_svg__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_linkedin_svg__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_x_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_instagram_svg__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}, \"b0xrQkBwMRtDSXZDGNM7QmBe+QA=\", false, function() {\n    return [\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    ];\n})), \"b0xrQkBwMRtDSXZDGNM7QmBe+QA=\", false, function() {\n    return [\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    ];\n});\n_c1 = GlossaryDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryDetails);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlossaryDetails$memo\");\n$RefreshReg$(_c1, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});