"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _PentabellCompanySection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PentabellCompanySection */ \"(app-pages-browser)/./src/features/glossary/component/PentabellCompanySection.jsx\");\n/* harmony import */ var _GlossaryHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./GlossaryHeader */ \"(app-pages-browser)/./src/features/glossary/component/GlossaryHeader.jsx\");\n/* harmony import */ var _GlossarySocialMediaIcon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./GlossarySocialMediaIcon */ \"(app-pages-browser)/./src/features/glossary/component/GlossarySocialMediaIcon.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst GlossaryDetails = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c = _s(function GlossaryDetails(param) {\n    let { glossary, language, isMobileSSR } = param;\n    _s();\n    const theme = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(theme.breakpoints.down(\"sm\")) || isMobileSSR;\n    const isTablet = (0,_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(theme.breakpoints.down(\"md\")) || isMobileSSR;\n    const breadcrumbSchema = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"BreadcrumbList\",\n            itemListElement: [\n                {\n                    \"@type\": \"ListItem\",\n                    position: 1,\n                    item: {\n                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                        name: \"Glossary\"\n                    }\n                }\n            ]\n        }), [\n        language\n    ]);\n    const glossaryPath = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>language === \"en\" ? \"/glossaries\" : \"/\".concat(language, \"/glossaries\"), [\n        language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"glossary-page-details\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(breadcrumbSchema)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"container\",\n                container: true,\n                columnSpacing: 2,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        sm: 12,\n                        md: 12,\n                        lg: 9,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlossaryHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                language: language,\n                                glossaryPath: glossaryPath,\n                                glossary: glossary\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"custom-max-width\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        htmlContent: glossary === null || glossary === void 0 ? void 0 : glossary.content\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PentabellCompanySection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        language: language\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this),\n                                    isMobile && isTablet && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlossarySocialMediaIcon__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 38\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    !(isMobile && isTablet) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        item: true,\n                        sm: 12,\n                        md: 12,\n                        lg: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"custom-max-width\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_GlossarySocialMediaIcon__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}, \"b0xrQkBwMRtDSXZDGNM7QmBe+QA=\", false, function() {\n    return [\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n})), \"b0xrQkBwMRtDSXZDGNM7QmBe+QA=\", false, function() {\n    return [\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        _barrel_optimize_names_Container_Grid_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    ];\n});\n_c1 = GlossaryDetails;\n/* harmony default export */ __webpack_exports__[\"default\"] = (GlossaryDetails);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlossaryDetails$memo\");\n$RefreshReg$(_c1, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});