"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_locales_en_validations_json",{

/***/ "(app-pages-browser)/./src/locales/en/validations.json":
/*!*****************************************!*\
  !*** ./src/locales/en/validations.json ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"invalidEmail":"Invalid email","emptyField":"Please fill in this required field !","invalidDate":"Invalid date","endDate":"End date must be after start date","minDate":"Date of birth must be after 1950","maxDate":"Date must be before 2005","minLength":"Field must be at least 3 characters","maxLength":"Field must be at most 20 characters","required":"This field is required!","invalidPassword":"Password requires at least one uppercase, one lowercase letter, one digit, and one special character","passwordMatch":"password must match","minOne":"At least one skill is required","minNationality":"At least one nationality is required","minRoles":"At least one role is required","phoneFormat":"Invalid phone number format","companyEmailRequired":"Please use your company email address"}');

/***/ })

});