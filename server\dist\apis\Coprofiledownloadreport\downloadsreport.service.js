"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const downloadsreport_model_1 = require("./downloadsreport.model");
const path_1 = __importDefault(require("path"));
const services_1 = require("@/utils/services");
class DownloadReportService {
    constructor() {
        this.DownloadReport = downloadsreport_model_1.DownloadReportModel;
    }
    async handleReportDownload(fullName, email) {
        if (!fullName || !email) {
            throw new Error('FullName and Email are required');
        }
        const filePath = path_1.default.join(__dirname, '/../../../src/public/static/Pentabell Corporate Profile.pdf');
        const downloadUrl = 'https://www.pentabell.com/Pentabell Corporate Profile.pdf';
        const existingUser = await this.DownloadReport.findOne({ email: email.toLowerCase() });
        await (0, services_1.sendEmail)({
            to: email,
            subject: 'Your Corporate Profile is Ready to Download',
            template: 'downloadReport',
            context: {
                fullName,
                email,
                downloadUrl,
            },
            attachments: [
                {
                    filename: 'Pentabell Corporate Profile.pdf',
                    path: filePath,
                    contentType: 'application/pdf',
                },
            ],
        });
        if (!existingUser) {
            await this.DownloadReport.create({ fullName, email });
            return 'Email sent with report (new record)';
        }
        return 'Email sent with report (existing user)';
    }
}
exports.default = DownloadReportService;
//# sourceMappingURL=downloadsreport.service.js.map