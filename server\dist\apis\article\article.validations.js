"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.articleSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const articleSchema = joi_1.default.object({
    versions: joi_1.default.array().items(joi_1.default.object({
        language: joi_1.default.string().required(),
        title: joi_1.default.string().required(),
        metaTitle: joi_1.default.string().required(),
        metaDescription: joi_1.default.string().required(),
        content: joi_1.default.string().required(),
    })).required(),
});
exports.articleSchema = articleSchema;
//# sourceMappingURL=article.validations.js.map