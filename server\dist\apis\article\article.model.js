"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const articleVersionSchema = new mongoose_1.Schema({
    language: { type: String, enum: constants_1.Language },
    title: { type: String },
    keywords: { type: [String] },
    metaTitle: { type: String, default: '' },
    metaDescription: { type: String, default: '' },
    url: { type: String },
    alt: { type: String, default: '' },
    image: { type: String },
    category: [{ type: mongoose_1.Types.ObjectId, ref: 'Category' }],
    shareOnSocialMedia: { type: Boolean, default: false },
    content: { type: String },
    canonical: { type: String },
    visibility: {
        type: String,
        enum: constants_1.Visibility,
        default: constants_1.Visibility.Draft,
    },
    publishDate: { type: Date },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    isArchived: { type: Boolean, default: false },
    highlights: { type: [String] },
    description: { type: String }
});
const articleSchema = new mongoose_1.Schema({
    versions: { type: [articleVersionSchema] },
    tags: { type: [String] },
    totalCommentaires: { type: Number, default: 0 },
    robotsMeta: {
        type: String,
        enum: constants_1.robotsMeta,
        default: constants_1.robotsMeta.index,
    },
    createdBy: { type: mongoose_1.Types.ObjectId, ref: 'User' },
}, {
    timestamps: true,
    toJSON: {
        transform: function (doc, ret) {
            delete ret.__v;
        },
    },
});
articleSchema.index({
    'versions.title': 'text',
    url: 'text',
}, {
    weights: {
        'versions.title': 50,
        url: 30,
    },
    name: 'ArticleTextIndex',
});
exports.default = (0, mongoose_1.model)('Article', articleSchema);
//# sourceMappingURL=article.model.js.map