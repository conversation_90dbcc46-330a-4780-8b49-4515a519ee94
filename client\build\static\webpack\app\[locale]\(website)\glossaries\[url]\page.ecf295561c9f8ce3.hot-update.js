"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(website)/glossaries/[url]/page",{

/***/ "(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx":
/*!*************************************************************!*\
  !*** ./src/features/glossary/component/GlossaryDetails.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GlossaryDetails; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Container/Container.js\");\n/* harmony import */ var _barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Container,Grid!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var moment_locale_fr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! moment/locale/fr */ \"(app-pages-browser)/./node_modules/moment/locale/fr.js\");\n/* harmony import */ var moment_locale_fr__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(moment_locale_fr__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../blog/components/ArticleContent */ \"(app-pages-browser)/./src/features/blog/components/ArticleContent.jsx\");\n/* harmony import */ var _assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/assets/images/icons/arrowRight.svg */ \"(app-pages-browser)/./src/assets/images/icons/arrowRight.svg\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction GlossaryDetails(param) {\n    let { id, article, language, url } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const theme = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const isMobile = (0,_barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(theme.breakpoints.down(\"sm\"));\n    moment__WEBPACK_IMPORTED_MODULE_2___default().locale(i18n.language || \"en\");\n    const [modifiedHtmlContent, setModifiedHtmlContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(article === null || article === void 0 ? void 0 : article.content);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const parser = new DOMParser();\n        const doc = parser.parseFromString(article === null || article === void 0 ? void 0 : article.content, \"text/html\");\n        const extractedHeadings = [];\n        Array.from(doc.querySelectorAll(\"h2, h3\")).forEach((heading)=>{\n            const id = heading.innerText.toLowerCase().replace(/\\s+/g, \"-\").replace(/[^a-z0-9\\-]/g, \"\");\n            heading.id = id;\n            extractedHeadings.push({\n                tagName: heading.tagName.toLowerCase(),\n                content: heading.innerText,\n                id\n            });\n        });\n        setModifiedHtmlContent(doc.body.innerHTML);\n    }, [\n        article === null || article === void 0 ? void 0 : article.content\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            id: \"glossary-page-details\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"glossary-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        className: \"custom-max-width\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"categories-path\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    locale: language === \"en\" ? \"en\" : \"fr\",\n                                    href: \"\".concat(language === \"en\" ? \"/glossary\" : \"/\".concat(language, \"/glossary\"), \"/\"),\n                                    className: \"link\",\n                                    children: \"Glossary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                (article === null || article === void 0 ? void 0 : article.word) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_assets_images_icons_arrowRight_svg__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: article === null || article === void 0 ? void 0 : article.word\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    type: \"application/ld+json\",\n                    dangerouslySetInnerHTML: {\n                        __html: JSON.stringify({\n                            \"@context\": \"https://schema.org\",\n                            \"@type\": \"BreadcrumbList\",\n                            itemListElement: [\n                                {\n                                    \"@type\": \"ListItem\",\n                                    position: 1,\n                                    item: {\n                                        \"@id\": language === \"en\" ? \"https://www.pentabell.com/glossaries/\" : \"https://www.pentabell.com/\".concat(language, \"/glossaries/\"),\n                                        name: \"Glossary\"\n                                    }\n                                }\n                            ]\n                        })\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"custom-max-width\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"container\",\n                        container: true,\n                        columnSpacing: 2,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Container_Grid_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 8,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"glossary-content\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_components_ArticleContent__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    htmlContent: modifiedHtmlContent\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\GlossaryDetails.jsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(GlossaryDetails, \"uSZFHA8dnwIqCnuai4cJTqtx7/0=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        _barrel_optimize_names_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    ];\n});\n_c = GlossaryDetails;\nvar _c;\n$RefreshReg$(_c, \"GlossaryDetails\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9nbG9zc2FyeS9jb21wb25lbnQvR2xvc3NhcnlEZXRhaWxzLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM0QztBQUNoQjtBQUNvQjtBQUNRO0FBQzlCO0FBQ3FCO0FBRW1CO0FBQ0Q7QUFFbEQsU0FBU1UsZ0JBQWdCLEtBQThCO1FBQTlCLEVBQUVDLEVBQUUsRUFBRUMsT0FBTyxFQUFFQyxRQUFRLEVBQUVDLEdBQUcsRUFBRSxHQUE5Qjs7SUFDdEMsTUFBTSxFQUFFQyxDQUFDLEVBQUUsR0FBR1IsNkRBQWNBO0lBRTVCLE1BQU1TLFFBQVFYLHNHQUFRQTtJQUN0QixNQUFNLEVBQUVZLElBQUksRUFBRSxHQUFHViw2REFBY0E7SUFDL0IsTUFBTVcsV0FBV1osc0dBQWFBLENBQUNVLE1BQU1HLFdBQVcsQ0FBQ0MsSUFBSSxDQUFDO0lBRXREbEIsb0RBQWEsQ0FBQ2UsS0FBS0osUUFBUSxJQUFJO0lBRS9CLE1BQU0sQ0FBQ1MscUJBQXFCQyx1QkFBdUIsR0FBR3RCLCtDQUFRQSxDQUM1RFcsb0JBQUFBLDhCQUFBQSxRQUFTWSxPQUFPO0lBR2xCeEIsZ0RBQVNBLENBQUM7UUFDUixNQUFNeUIsU0FBUyxJQUFJQztRQUNuQixNQUFNQyxNQUFNRixPQUFPRyxlQUFlLENBQUNoQixvQkFBQUEsOEJBQUFBLFFBQVNZLE9BQU8sRUFBRTtRQUNyRCxNQUFNSyxvQkFBb0IsRUFBRTtRQUU1QkMsTUFBTUMsSUFBSSxDQUFDSixJQUFJSyxnQkFBZ0IsQ0FBQyxXQUFXQyxPQUFPLENBQUMsQ0FBQ0M7WUFDbEQsTUFBTXZCLEtBQUt1QixRQUFRQyxTQUFTLENBQ3pCQyxXQUFXLEdBQ1hDLE9BQU8sQ0FBQyxRQUFRLEtBQ2hCQSxPQUFPLENBQUMsZ0JBQWdCO1lBQzNCSCxRQUFRdkIsRUFBRSxHQUFHQTtZQUNia0Isa0JBQWtCUyxJQUFJLENBQUM7Z0JBQ3JCQyxTQUFTTCxRQUFRSyxPQUFPLENBQUNILFdBQVc7Z0JBQ3BDWixTQUFTVSxRQUFRQyxTQUFTO2dCQUMxQnhCO1lBQ0Y7UUFDRjtRQUVBWSx1QkFBdUJJLElBQUlhLElBQUksQ0FBQ0MsU0FBUztJQUMzQyxHQUFHO1FBQUM3QixvQkFBQUEsOEJBQUFBLFFBQVNZLE9BQU87S0FBQztJQUVyQixxQkFDRTtrQkFDRSw0RUFBQ2tCO1lBQUkvQixJQUFHOzs4QkFDTiw4REFBQytCO29CQUFJL0IsSUFBRzs4QkFDTiw0RUFBQ1IsMEZBQVNBO3dCQUFDd0MsV0FBVTtrQ0FDbkIsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQ0N2QixRQUFRUixhQUFhLE9BQU8sT0FBTztvQ0FDbkNnQyxNQUFNLEdBRUwsT0FEQ2hDLGFBQWEsT0FBUSxjQUFhLElBQWEsT0FBVEEsVUFBUyxjQUNoRDtvQ0FDRDhCLFdBQVU7OENBQ1g7Ozs7OztnQ0FHQS9CLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU2tDLElBQUksbUJBQ1o7O3NEQUNFLDhEQUFDckMsMkVBQWFBOzs7OztzREFDZCw4REFBQ3NDO3NEQUFHbkMsb0JBQUFBLDhCQUFBQSxRQUFTa0MsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU0zQiw4REFBQ0U7b0JBQ0NDLE1BQUs7b0JBQ0xDLHlCQUF5Qjt3QkFDdkJDLFFBQVFDLEtBQUtDLFNBQVMsQ0FBQzs0QkFDckIsWUFBWTs0QkFDWixTQUFTOzRCQUNUQyxpQkFBaUI7Z0NBQ2Y7b0NBQ0UsU0FBUztvQ0FDVEMsVUFBVTtvQ0FDVkMsTUFBTTt3Q0FDSixPQUNFM0MsYUFBYSxPQUNSLDBDQUNELDZCQUFzQyxPQUFUQSxVQUFTO3dDQUM1QzRDLE1BQU07b0NBQ1I7Z0NBQ0Y7NkJBQ0Q7d0JBQ0g7b0JBQ0Y7Ozs7Ozs4QkFFRiw4REFBQ3RELDBGQUFTQTtvQkFBQ3dDLFdBQVU7OEJBQ25CLDRFQUFDdkMsMkZBQUlBO3dCQUFDdUMsV0FBVTt3QkFBWWUsU0FBUzt3QkFBQ0MsZUFBZTtrQ0FDbkQsNEVBQUN2RCwyRkFBSUE7NEJBQUNvRCxJQUFJOzRCQUFDSSxJQUFJOzRCQUFJQyxJQUFJO3NDQUNyQiw0RUFBQ25CO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDbkMsdUVBQWNBO29DQUFDc0QsYUFBYXhDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVN0M7R0E5RndCWjs7UUFDUkgseURBQWNBO1FBRWRGLGtHQUFRQTtRQUNMRSx5REFBY0E7UUFDZEQsa0dBQWFBOzs7S0FMUkkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2ZlYXR1cmVzL2dsb3NzYXJ5L2NvbXBvbmVudC9HbG9zc2FyeURldGFpbHMuanN4PzEzMzYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IG1vbWVudCBmcm9tIFwibW9tZW50XCI7XHJcbmltcG9ydCB7IENvbnRhaW5lciwgR3JpZCB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IHVzZVRoZW1lLCB1c2VNZWRpYVF1ZXJ5IH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcclxuaW1wb3J0IFwibW9tZW50L2xvY2FsZS9mclwiO1xyXG5pbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gXCJyZWFjdC1pMThuZXh0XCI7XHJcblxyXG5pbXBvcnQgQXJ0aWNsZUNvbnRlbnQgZnJvbSBcIi4uLy4uL2Jsb2cvY29tcG9uZW50cy9BcnRpY2xlQ29udGVudFwiO1xyXG5pbXBvcnQgU3ZnQXJyb3dSaWdodCBmcm9tIFwiQC9hc3NldHMvaW1hZ2VzL2ljb25zL2Fycm93UmlnaHQuc3ZnXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBHbG9zc2FyeURldGFpbHMoeyBpZCwgYXJ0aWNsZSwgbGFuZ3VhZ2UsIHVybCB9KSB7XHJcbiAgY29uc3QgeyB0IH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG5cclxuICBjb25zdCB0aGVtZSA9IHVzZVRoZW1lKCk7XHJcbiAgY29uc3QgeyBpMThuIH0gPSB1c2VUcmFuc2xhdGlvbigpO1xyXG4gIGNvbnN0IGlzTW9iaWxlID0gdXNlTWVkaWFRdWVyeSh0aGVtZS5icmVha3BvaW50cy5kb3duKFwic21cIikpO1xyXG5cclxuICBtb21lbnQubG9jYWxlKGkxOG4ubGFuZ3VhZ2UgfHwgXCJlblwiKTtcclxuXHJcbiAgY29uc3QgW21vZGlmaWVkSHRtbENvbnRlbnQsIHNldE1vZGlmaWVkSHRtbENvbnRlbnRdID0gdXNlU3RhdGUoXHJcbiAgICBhcnRpY2xlPy5jb250ZW50XHJcbiAgKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHBhcnNlciA9IG5ldyBET01QYXJzZXIoKTtcclxuICAgIGNvbnN0IGRvYyA9IHBhcnNlci5wYXJzZUZyb21TdHJpbmcoYXJ0aWNsZT8uY29udGVudCwgXCJ0ZXh0L2h0bWxcIik7XHJcbiAgICBjb25zdCBleHRyYWN0ZWRIZWFkaW5ncyA9IFtdO1xyXG5cclxuICAgIEFycmF5LmZyb20oZG9jLnF1ZXJ5U2VsZWN0b3JBbGwoXCJoMiwgaDNcIikpLmZvckVhY2goKGhlYWRpbmcpID0+IHtcclxuICAgICAgY29uc3QgaWQgPSBoZWFkaW5nLmlubmVyVGV4dFxyXG4gICAgICAgIC50b0xvd2VyQ2FzZSgpXHJcbiAgICAgICAgLnJlcGxhY2UoL1xccysvZywgXCItXCIpXHJcbiAgICAgICAgLnJlcGxhY2UoL1teYS16MC05XFwtXS9nLCBcIlwiKTtcclxuICAgICAgaGVhZGluZy5pZCA9IGlkO1xyXG4gICAgICBleHRyYWN0ZWRIZWFkaW5ncy5wdXNoKHtcclxuICAgICAgICB0YWdOYW1lOiBoZWFkaW5nLnRhZ05hbWUudG9Mb3dlckNhc2UoKSxcclxuICAgICAgICBjb250ZW50OiBoZWFkaW5nLmlubmVyVGV4dCxcclxuICAgICAgICBpZCxcclxuICAgICAgfSk7XHJcbiAgICB9KTtcclxuXHJcbiAgICBzZXRNb2RpZmllZEh0bWxDb250ZW50KGRvYy5ib2R5LmlubmVySFRNTCk7XHJcbiAgfSwgW2FydGljbGU/LmNvbnRlbnRdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxkaXYgaWQ9XCJnbG9zc2FyeS1wYWdlLWRldGFpbHNcIj5cclxuICAgICAgICA8ZGl2IGlkPVwiZ2xvc3NhcnktaGVhZGVyXCI+XHJcbiAgICAgICAgICA8Q29udGFpbmVyIGNsYXNzTmFtZT1cImN1c3RvbS1tYXgtd2lkdGhcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXRlZ29yaWVzLXBhdGhcIj5cclxuICAgICAgICAgICAgICA8YVxyXG4gICAgICAgICAgICAgICAgbG9jYWxlPXtsYW5ndWFnZSA9PT0gXCJlblwiID8gXCJlblwiIDogXCJmclwifVxyXG4gICAgICAgICAgICAgICAgaHJlZj17YCR7XHJcbiAgICAgICAgICAgICAgICAgIGxhbmd1YWdlID09PSBcImVuXCIgPyBgL2dsb3NzYXJ5YCA6IGAvJHtsYW5ndWFnZX0vZ2xvc3NhcnlgXHJcbiAgICAgICAgICAgICAgICB9L2B9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJsaW5rXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICBHbG9zc2FyeVxyXG4gICAgICAgICAgICAgIDwvYT5cclxuICAgICAgICAgICAgICB7YXJ0aWNsZT8ud29yZCAmJiAoXHJcbiAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICA8U3ZnQXJyb3dSaWdodCAvPlxyXG4gICAgICAgICAgICAgICAgICA8cD57YXJ0aWNsZT8ud29yZH08L3A+XHJcbiAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvQ29udGFpbmVyPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxzY3JpcHRcclxuICAgICAgICAgIHR5cGU9XCJhcHBsaWNhdGlvbi9sZCtqc29uXCJcclxuICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7XHJcbiAgICAgICAgICAgIF9faHRtbDogSlNPTi5zdHJpbmdpZnkoe1xyXG4gICAgICAgICAgICAgIFwiQGNvbnRleHRcIjogXCJodHRwczovL3NjaGVtYS5vcmdcIixcclxuICAgICAgICAgICAgICBcIkB0eXBlXCI6IFwiQnJlYWRjcnVtYkxpc3RcIixcclxuICAgICAgICAgICAgICBpdGVtTGlzdEVsZW1lbnQ6IFtcclxuICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgXCJAdHlwZVwiOiBcIkxpc3RJdGVtXCIsXHJcbiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAxLFxyXG4gICAgICAgICAgICAgICAgICBpdGVtOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgXCJAaWRcIjpcclxuICAgICAgICAgICAgICAgICAgICAgIGxhbmd1YWdlID09PSBcImVuXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPyBgaHR0cHM6Ly93d3cucGVudGFiZWxsLmNvbS9nbG9zc2FyaWVzL2BcclxuICAgICAgICAgICAgICAgICAgICAgICAgOiBgaHR0cHM6Ly93d3cucGVudGFiZWxsLmNvbS8ke2xhbmd1YWdlfS9nbG9zc2FyaWVzL2AsXHJcbiAgICAgICAgICAgICAgICAgICAgbmFtZTogXCJHbG9zc2FyeVwiLFxyXG4gICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICBdLFxyXG4gICAgICAgICAgICB9KSxcclxuICAgICAgICAgIH19XHJcbiAgICAgICAgLz5cclxuICAgICAgICA8Q29udGFpbmVyIGNsYXNzTmFtZT1cImN1c3RvbS1tYXgtd2lkdGhcIj5cclxuICAgICAgICAgIDxHcmlkIGNsYXNzTmFtZT1cImNvbnRhaW5lclwiIGNvbnRhaW5lciBjb2x1bW5TcGFjaW5nPXsyfT5cclxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs4fT5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdsb3NzYXJ5LWNvbnRlbnRcIj5cclxuICAgICAgICAgICAgICAgIDxBcnRpY2xlQ29udGVudCBodG1sQ29udGVudD17bW9kaWZpZWRIdG1sQ29udGVudH0gLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgey8qIDxHcmlkIGNsYXNzTmFtZT1cImNvbnRhaW5lclwiIGNvbnRhaW5lciBjb2x1bW5TcGFjaW5nPXsyfT5cclxuICAgICAgICAgIDwvR3JpZD4gKi99XHJcbiAgICAgICAgPC9Db250YWluZXI+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJtb21lbnQiLCJDb250YWluZXIiLCJHcmlkIiwidXNlVGhlbWUiLCJ1c2VNZWRpYVF1ZXJ5IiwidXNlVHJhbnNsYXRpb24iLCJBcnRpY2xlQ29udGVudCIsIlN2Z0Fycm93UmlnaHQiLCJHbG9zc2FyeURldGFpbHMiLCJpZCIsImFydGljbGUiLCJsYW5ndWFnZSIsInVybCIsInQiLCJ0aGVtZSIsImkxOG4iLCJpc01vYmlsZSIsImJyZWFrcG9pbnRzIiwiZG93biIsImxvY2FsZSIsIm1vZGlmaWVkSHRtbENvbnRlbnQiLCJzZXRNb2RpZmllZEh0bWxDb250ZW50IiwiY29udGVudCIsInBhcnNlciIsIkRPTVBhcnNlciIsImRvYyIsInBhcnNlRnJvbVN0cmluZyIsImV4dHJhY3RlZEhlYWRpbmdzIiwiQXJyYXkiLCJmcm9tIiwicXVlcnlTZWxlY3RvckFsbCIsImZvckVhY2giLCJoZWFkaW5nIiwiaW5uZXJUZXh0IiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIiwicHVzaCIsInRhZ05hbWUiLCJib2R5IiwiaW5uZXJIVE1MIiwiZGl2IiwiY2xhc3NOYW1lIiwiYSIsImhyZWYiLCJ3b3JkIiwicCIsInNjcmlwdCIsInR5cGUiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsIkpTT04iLCJzdHJpbmdpZnkiLCJpdGVtTGlzdEVsZW1lbnQiLCJwb3NpdGlvbiIsIml0ZW0iLCJuYW1lIiwiY29udGFpbmVyIiwiY29sdW1uU3BhY2luZyIsInhzIiwic20iLCJodG1sQ29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/GlossaryDetails.jsx\n"));

/***/ })

});