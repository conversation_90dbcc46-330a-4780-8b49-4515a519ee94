"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommentModel = void 0;
const mongoose_1 = require("mongoose");
const commentSchema = new mongoose_1.Schema({
    article: {
        type: mongoose_1.Types.ObjectId,
        ref: 'Article',
    },
    user: {
        type: mongoose_1.Types.ObjectId,
        ref: 'User',
    },
    responses: [
        {
            type: mongoose_1.Types.ObjectId,
            ref: 'Comment',
        },
    ],
    email: { type: String },
    firstName: { type: String },
    comment: { type: String },
    approved: { type: Boolean, default: false },
    badge: { type: String, default: null },
}, {
    timestamps: true,
});
exports.CommentModel = (0, mongoose_1.model)('Comment', commentSchema);
//# sourceMappingURL=commentaire.model.js.map