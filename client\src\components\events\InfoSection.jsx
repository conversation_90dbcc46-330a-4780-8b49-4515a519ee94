import { Box, Grid, Typography } from "@mui/material";
import { Business, Public, Place, Event } from "@mui/icons-material";
import SectorIcon from "@/assets/images/icons/sector.svg";
import LocationIcon from "@/assets/images/icons/location.svg";
import CountryIcon from "@/assets/images/icons/country.svg";
import OrganiserIcon from "@/assets/images/icons/organiser.svg";

const InfoSection = ({ t, infos }) => {
  return (
    <Box className="infoSection">
      <Grid container spacing={2}>
        {/* Left Side */}
        <Grid item xs={12} sm={6} md={6} className="leftSide">
          <Box className="item">
            <SectorIcon className="icon" />
            <Box>
              <Typography className="label">
                {t("eventDetails:sector")}
              </Typography>
              <Typography className="value">{infos.sector}</Typography>
            </Box>
          </Box>

          <Box className="item">
            <CountryIcon className="icon" />
            <Box>
              <Typography className="label">
                {t("eventDetails:countryConcerned")}
              </Typography>
              <Typography className="value">
                {" "}
                {infos.countryConcerned}
              </Typography>
            </Box>
          </Box>
        </Grid>

        {/* Divider */}
        <Grid item xs={0} sm={1} md={1} className="divider"></Grid>

        {/* Right Side */}
        <Grid item xs={12} sm={6} md={5} className="right-side">
          <Box className="item">
            <LocationIcon className="icon" />
            <Box>
              <Typography className="label">
                {t("eventDetails:location")}
              </Typography>
              <Typography className="value">{infos.country}</Typography>
            </Box>
          </Box>

          <Box className="item">
            <OrganiserIcon className="icon" />
            <Box>
              <Typography className="label">
                {t("eventDetails:organiser")}
              </Typography>
              <Typography className="value">{infos.organiser}</Typography>
            </Box>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default InfoSection;
