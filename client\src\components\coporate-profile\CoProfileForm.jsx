"use client";
import {
  Container,
  FormGroup,
  Grid,
  TextField,
  FormLabel,
  FormControlLabel,
  Checkbox,
  Alert,
  Box,
  useMediaQuery, useTheme
} from "@mui/material";
import { ErrorMessage, Form, Formik } from "formik";
import * as Yup from "yup";
import { useState } from "react";
import { axiosGetJsonSSR } from "@/config/axios";
import { useTranslation } from "react-i18next";
import CustomButton from "../ui/CustomButton";
import ImgCoporateFormSection from "../../assets/images/website/coporate-profile/ImgCoporateFormSection.png";
import ImgCoporateFormSectionmobile from "../../assets/images/website/coporate-profile/ImgCoporateFormSectionmobile.png";
import coporateProfileFormimage from "../../assets/images/website/coporate-profile/coporateProfileFormimage.png";
import CompanyEmailValidator from "company-email-validator";
import { toast } from "react-toastify";
import { API_URLS } from "@/utils/urls";
function CoProfileForm() {
  const { t } = useTranslation();
  const [success, setSuccess] = useState(false);
  const [errMsg, setErrMsg] = useState("");
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const initialValues = {
    fullName: "",
    email: "",
    acceptTerms: false,
  };
  const validationSchema = () =>
    Yup.object().shape({
      fullName: Yup.string().required(t("validations:emptyField")),
      email: Yup.string()
        .email(t("validations:invalidEmail"))
        .required(t("validations:required"))
        .test(
          "is-company-email",
          t("validations:companyEmailRequired"),
          (value) => CompanyEmailValidator.isCompanyEmail(value)
        ),
      acceptTerms: Yup.boolean().oneOf([true], t("validations:required")),
    });
  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    try {
      await axiosGetJsonSSR.post(`${API_URLS.report}/download`, values);
      toast.success("Your Corporate Profile has been successfully delivered to your email inbox.");
      resetForm();
    } catch (error) {
      console.error("Download error:", error);
      toast.error("An error occurred while downloading the report.");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box id="coporateProfileForm">
      <Box
        className="coporate-form"
        sx={{
          backgroundImage: `url(${isMobile ? ImgCoporateFormSectionmobile.src : ImgCoporateFormSection.src})`,
        }}
      />
      <Container className="relative-section custom-max-width">
        <Grid container className="coporate-grid" spacing={2}>
          <Grid item xs={12} md={8} className="left-section">
            <Box display="flex" flexDirection="column" gap="1rem">
              <h2 className="heading-h1 text-white">Download the Report</h2>

              <Formik
                initialValues={initialValues}
                validationSchema={() => validationSchema()}
                onSubmit={handleSubmit}
              >
                {({ values, handleChange, touched, errors }) => (
                  <Form className="pentabell-form">
                    <Grid item md={10} xs={12}>
                      <FormGroup className="form-group light">
                        <FormLabel className="label-pentabell light">
                          Full Name
                        </FormLabel>
                        <TextField
                          variant="standard"
                          name="fullName"
                          placeholder={t("joinUs:form:fullName")}
                          fullWidth
                          className="input-pentabell"
                          value={values.fullName}
                          onChange={handleChange}
                          error={!!(errors.fullName && touched.fullName)}
                        />
                      </FormGroup>
                      <ErrorMessage name="fullName">
                        {(msg) => (
                          <Alert variant="filled" severity="error">
                            {msg}
                          </Alert>
                        )}
                      </ErrorMessage>
                    </Grid>
                    <Grid item md={10} xs={12}>
                      <FormGroup className="form-group light">
                        <FormLabel className="label-pentabell light">
                          Work Email
                        </FormLabel>
                        <TextField
                          placeholder={t("Work Email")}
                          variant="standard"
                          name="email"
                          fullWidth
                          className="input-pentabell"
                          value={values.email}
                          onChange={handleChange}
                          error={!!(errors.email && touched.email)}

                        /> </FormGroup>
                      <ErrorMessage name="email">
                        {(msg) => (
                          <Alert variant="filled" severity="error">
                            {msg}
                          </Alert>
                        )}
                      </ErrorMessage>
                    </Grid>
                    <Grid item xs={12} sm={10}>
                      <FormGroup className="form-group light">
                        <FormControlLabel
                          className="checkbox-pentabell"
                          control={
                            <Checkbox
                              name="acceptTerms"
                              checked={values.acceptTerms}
                              onChange={handleChange}
                              error={!!(errors.acceptTerms && touched.acceptTerms)}
                            />
                          }
                          label={
                            <span className="accept-terms">
                              By submitting this form, you agree to be contacted about Pentabvell’s services, as outlined in our Privacy Policy.
                            </span>
                          }
                        />
                      </FormGroup>
                      <ErrorMessage name="acceptTerms">
                        {(msg) => (
                          <Alert variant="filled" severity="error">
                            {msg}
                          </Alert>
                        )}
                      </ErrorMessage>
                    </Grid>
                    <Box mt={5} className="btn-section">
                      <CustomButton
                        style={{ textDecoration: "none", width: "fit-content" }}
                        className={`btn btn-filled ${isMobile ? "full-width" : ""}`}
                        text="Download Our Report"
                        type="submit"
                      />
                    </Box>
                  </Form>
                )}
              </Formik>
            </Box>
          </Grid>
          <Grid item xs={12} md={4} className="right-section">
            <img src={coporateProfileFormimage.src} alt="Report" className="coporate-image" />
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

export default CoProfileForm;
