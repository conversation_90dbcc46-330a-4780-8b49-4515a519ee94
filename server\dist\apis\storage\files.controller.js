"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const pdf_parse_1 = __importDefault(require("pdf-parse"));
const word_extractor_1 = __importDefault(require("word-extractor"));
const crypto = __importStar(require("crypto"));
const files_service_1 = __importDefault(require("./files.service"));
const fileExist_middleware_1 = require("@/middlewares/fileExist.middleware");
const upload_middleware_1 = require("@/middlewares/upload.middleware");
const validateFormat_middleware_1 = require("@/middlewares/validateFormat.middleware");
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const validation_middleware_1 = require("@/middlewares/validation.middleware");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const user_service_1 = __importDefault(require("../user/services/user.service"));
const functions_1 = require("@/utils/helpers/functions");
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const sharp_1 = __importDefault(require("sharp"));
class FilesController {
    constructor() {
        this.path = '/files';
        this.router = (0, express_1.Router)();
        this.userService = new user_service_1.default();
        this.filesService = new files_service_1.default();
        this.findFileCandidateBase64 = async (request, response, next) => {
            try {
                const fileName = request.params.filename;
                const fileBase64 = await this.filesService.findFileCandidateBase64(fileName);
                response.status(200).json({
                    fileName,
                    fileContent: fileBase64,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.createFile = async (request, response, next) => {
            try {
                const { filename, resource, folder } = request.params;
                const file = request.file;
                let resumeText = '';
                if (!file) {
                    return next(new http_exception_1.default(400, 'Please provide a file!'));
                }
                const ipAddress = request.socket.remoteAddress || request.ip;
                const fileNameParts = file.originalname.split('.');
                const dataBuffer = fs.readFileSync(file.path);
                const checksum = crypto.createHash('sha256').update(dataBuffer).digest('hex');
                const existingFile = await this.filesService.getFileByChecksum(checksum);
                if (existingFile) {
                    throw new http_exception_1.default(409, 'A file with the same checksum already exists.');
                }
                const fileData = {
                    resource: resource,
                    folder: folder,
                    ipSender: ipAddress,
                    originalName: file.originalname,
                    uuid: filename,
                    fileName: filename + '.' + fileNameParts[fileNameParts.length - 1].toLowerCase(),
                    fileType: file.mimetype,
                    fileSize: file.size,
                    checksum: checksum,
                };
                if (resource === 'candidates') {
                    if (file.mimetype === 'application/pdf') {
                        const data = await (0, pdf_parse_1.default)(dataBuffer);
                        resumeText = data.text;
                        resumeText = resumeText.replace(/\s+/g, ' ').trim();
                        if (!(0, functions_1.validate)(resumeText)) {
                            resumeText = await (0, functions_1.extractTextFromScannedPDF)(dataBuffer);
                            resumeText = resumeText.replace(/\s+/g, ' ').trim();
                        }
                    }
                    else if (file.mimetype === 'application/msword' ||
                        file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                        const extractor = new word_extractor_1.default();
                        const doc = await extractor.extract(dataBuffer);
                        resumeText = doc.getBody();
                    }
                    else {
                        throw new http_exception_1.default(400, 'File type not allowed');
                    }
                    const validateContent = (0, functions_1.validate)(resumeText);
                    // const validateMails = extractEmails(resumeText);
                    // if (!validateContent || validateMails.length === 0) {
                    if (!validateContent) {
                        throw new http_exception_1.default(400, 'The resume lacks essential information (education, experience, or contact details)');
                    }
                }
                await this.filesService.createFile(fileData);
                if (resource === 'candidates') {
                    response.status(201).send({ message: 'File created successfully', data: resumeText });
                }
                else {
                    response.status(201).send({ message: 'File created successfully' });
                }
            }
            catch (error) {
                next(error);
            }
        };
        this.uploadResume = async (request, response, next) => {
            try {
                const { filename, resource, folder } = request.params;
                const file = request.file;
                let resumeText = '';
                if (!file) {
                    return next(new http_exception_1.default(400, 'Please provide a file!'));
                }
                const ipAddress = request.socket.remoteAddress || request.ip;
                const originalExt = path.extname(file.originalname).toLowerCase();
                const allowedImageTypes = ['.png', '.jpg', '.jpeg'];
                let finalPath = file.path;
                let finalExt = originalExt;
                let finalMimeType = file.mimetype;
                let originalName = file.originalname;
                const dataBuffer = fs.readFileSync(file.path);
                const checksum = crypto.createHash('sha256').update(dataBuffer).digest('hex');
                const existingFiles = await this.filesService.getFilesByChecksum(checksum);
                const hasWebp = existingFiles.some(f => path.extname(f.fileName).toLowerCase() === '.webp');
                const hasPngOrJpg = existingFiles.some(f => allowedImageTypes.includes(path.extname(f.fileName).toLowerCase()));
                if (hasWebp) {
                    const webpFile = existingFiles.find(f => path.extname(f.fileName).toLowerCase() === '.webp');
                    return response.status(201).json({
                        message: 'uuid exist',
                        uuid: webpFile?.fileName
                    });
                }
                if (hasPngOrJpg || allowedImageTypes.includes(originalExt)) {
                    try {
                        const buffer = fs.readFileSync(file.path);
                        const webpPath = file.path.replace(/\.[^/.]+$/, '.webp');
                        await (0, sharp_1.default)(buffer)
                            .rotate()
                            .webp({ quality: 80 })
                            .toFile(webpPath);
                        fs.unlinkSync(file.path);
                        finalPath = webpPath;
                        finalExt = '.webp';
                        finalMimeType = 'image/webp';
                        originalName = path.basename(webpPath);
                    }
                    catch (err) {
                        console.error('Erreur lors de la conversion en WebP :', err);
                        return next(new http_exception_1.default(500, 'Failed to convert image to webp'));
                    }
                }
                const fileData = {
                    resource,
                    folder,
                    ipSender: ipAddress,
                    originalName,
                    uuid: filename,
                    fileName: filename + finalExt,
                    fileType: finalMimeType,
                    fileSize: fs.statSync(finalPath).size,
                    checksum,
                };
                if (resource === 'candidates') {
                    if (finalMimeType === 'application/pdf') {
                        const data = await (0, pdf_parse_1.default)(dataBuffer);
                        resumeText = data.text.replace(/\s+/g, ' ').trim();
                        if (!(0, functions_1.validate)(resumeText)) {
                            resumeText = await (0, functions_1.extractTextFromScannedPDF)(dataBuffer);
                            resumeText = resumeText.replace(/\s+/g, ' ').trim();
                        }
                    }
                    else if (finalMimeType === 'application/msword' ||
                        finalMimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                        const extractor = new word_extractor_1.default();
                        const doc = await extractor.extract(dataBuffer);
                        resumeText = doc.getBody().replace(/\s+/g, ' ').trim();
                    }
                    else if (!finalMimeType.startsWith('image/')) {
                        return next(new http_exception_1.default(400, 'File type not allowed'));
                    }
                    if (!(0, functions_1.validate)(resumeText)) {
                        return next(new http_exception_1.default(400, 'The resume lacks essential information (education, experience, or contact details)'));
                    }
                }
                await this.filesService.createFile(fileData);
                return response.status(201).json(resource === 'candidates'
                    ? { message: 'File created successfully', data: resumeText }
                    : { message: 'File created successfully', uuid: filename + finalExt });
            }
            catch (error) {
                next(error);
            }
        };
        this.getFileByResource = async (request, response, next) => {
            try {
                const ressource = request.params.resource;
                const queries = request.query;
                const result = await this.filesService.findByresource(ressource, queries);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.getFileByUuid = async (request, response, next) => {
            try {
                const fileName = request.params.filename;
                const findOnePath = await this.filesService.findFile(fileName);
                response.status(200).sendFile(path.resolve(findOnePath), err => {
                    if (err) {
                        return next(new http_exception_1.default(404, 'Invalid ressource'));
                    }
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getFileByName = async (request, response, next) => {
            try {
                const originalName = request.params.originalName;
                const findOnePath = await this.filesService.findFileByName(originalName);
                response.status(200).sendFile(path.resolve(findOnePath), err => {
                    if (err) {
                        return next(new http_exception_1.default(404, 'Invalid ressource'));
                    }
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getFileByNameeventmaps = async (request, response, next) => {
            try {
                const originalName = request.params.originalName;
                const findOnePath = await this.filesService.findFileeventMaps(originalName);
                response.status(200).sendFile(path.resolve(findOnePath), err => {
                    if (err) {
                        return next(new http_exception_1.default(404, 'Invalid ressource'));
                    }
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getUserFileByUuid = async (request, response, next) => {
            try {
                const userId = request.user._id;
                const userdata = await this.userService.get(userId);
                const findOnePath = await this.filesService.findFile(userdata?.profilePicture);
                response.status(200).sendFile(path.resolve(findOnePath), err => {
                    if (err) {
                        return next(new http_exception_1.default(404, 'Invalid ressource'));
                    }
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteFile = async (request, response, next) => {
            try {
                const filename = request.params.filename;
                await this.filesService.deleteFile(filename);
                response.status(200).json({ message: 'File deleted successfully' });
            }
            catch (error) {
                next(error);
            }
        };
        this.countFiles = async (request, response, next) => {
            try {
                const query = request.query;
                const result = await this.filesService.countFiles('./uploads', query);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.compressImage = async (request, response, next) => {
            try {
                const { resource, folder, quality } = request.body;
                const result = await this.filesService.compressImage(resource, folder, quality);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.convertImagesToWebp = async (request, response, next) => {
            try {
                const { imagesPath } = request.body;
                const result = await this.filesService.convertImagesToWebp(imagesPath);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.get(`${this.path}/:filename`, validation_middleware_1.validateUUID, validateFormat_middleware_1.validateFormatMiddleware, this.getFileByUuid);
        this.router.get(`${this.path}/byresource/:resource`, this.getFileByResource);
        this.router.get(`${this.path}`, authentication_middleware_1.default, this.getUserFileByUuid);
        this.router.get(`${this.path}/candidate/:filename`, this.findFileCandidateBase64);
        this.router.post(`${this.path}/:resource/:folder/:filename`, validation_middleware_1.validateParams, validation_middleware_1.validateUUID, fileExist_middleware_1.fileExistMiddleware, upload_middleware_1.uploadMiddleware, this.createFile);
        this.router.post(`${this.path}/uploadResume/:resource/:folder/:filename`, validation_middleware_1.validateParams, validation_middleware_1.validateUUID, fileExist_middleware_1.fileExistMiddleware, upload_middleware_1.uploadMiddleware, this.uploadResume);
        this.router.get(`/file/:originalName`, this.getFileByName);
        this.router.get(`/eventMaps/:originalName`, this.getFileByNameeventmaps);
        this.router.put(`${this.path}/compress`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN]), this.compressImage);
        this.router.put(`${this.path}/convert-image-to-webp`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.ADMIN, constants_1.Role.EDITEUR]), this.convertImagesToWebp);
        this.router.delete(`${this.path}/:filename`, authentication_middleware_1.default, validation_middleware_1.validateUUID, this.deleteFile);
    }
}
exports.default = FilesController;
//# sourceMappingURL=files.controller.js.map