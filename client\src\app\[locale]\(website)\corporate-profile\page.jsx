import CoProfileBanner from "@/components/coporate-profile/CoProfileBanner";
import WhoWeAre from "@/components/coporate-profile/WhoWeAre";
import CoProfileOurServices from "@/components/coporate-profile/CoProfileOurServices";
import Testimonials from "@/components/coporate-profile/Testimonials";
import CoProfileOurIndustries from "../../../../components/coporate-profile/CoProfileOurIndustries";
import ExpertCareSection from "../../../../components/coporate-profile/ExpertCareSection";
import SustinabilitySection from "../../../../components/coporate-profile/SustinabilitySection";
import CoProfileCSR from "../../../../components/coporate-profile/CoProfileCSR";
import HunterSection from "../../../../components/coporate-profile/HunterSection";
import CoProfileForm from "../../../../components/coporate-profile/CoProfileForm";
import { headers } from "next/headers";

export async function generateMetadata() {
    return {
      title: "International Recruitment, Staffing & Payroll Agency | Pentabell",
      description:
        "Seeking the best recruitment agency ? Pentabell is an award-winning provider of international recruitment, staffing, consulting and HR services.",
      robots: "nofollow, noindex",
    };
  }

export default function page() {
/*  const userAgent = headers().get("user-agent") || "";
  const isMobile = /mobile/i.test(userAgent);
  const deviceType = isMobile ? "mobile" : "desktop";
  const isMobileSSR = deviceType === "mobile"; */
  return (
    <div style={{backgroundColor:"rgba(35, 71, 145, 1)"}}>
        <CoProfileBanner />
        <WhoWeAre />
        <CoProfileOurServices />
        <Testimonials />
        <CoProfileOurIndustries />
        <HunterSection />
        <ExpertCareSection />
        <SustinabilitySection />
        <CoProfileCSR />
        <CoProfileForm/>
    </div>
  )
}
