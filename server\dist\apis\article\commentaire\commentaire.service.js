"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const article_model_1 = __importDefault(require("../article.model"));
const commentaire_model_1 = require("./commentaire.model");
const socket_1 = require("@/utils/config/socket");
const constants_1 = require("@/utils/helpers/constants");
class CommentService {
    constructor() {
        this.Comment = commentaire_model_1.CommentModel;
        this.Article = article_model_1.default;
    }
    async addCommentToArticle(articleId, commentData, currentUser) {
        const article = await this.Article.findById(articleId);
        if (!article)
            throw new http_exception_1.default(404, 'Article not found');
        const commentPayload = {
            comment: commentData.comment,
            article: articleId,
        };
        if (currentUser) {
            commentPayload.user = currentUser._id;
        }
        else {
            if (!commentData.email || !commentData.firstName) {
                throw new http_exception_1.default(400, 'Email & First Name must be provided!');
            }
            commentPayload.email = commentData.email;
            commentPayload.firstName = commentData.firstName;
            commentPayload.user = null;
        }
        await this.Comment.create(commentPayload);
        if (currentUser) {
            const notificationMessage = commentData?.language === 'en'
                ? `Your comment has been successfully added and is awaiting administrator approval`
                : `Votre commentaire a été ajouté avec succès et est en attente de l'approbation de l'administrateur.`;
            await (0, socket_1.sendNotification)({
                receiver: currentUser?._id,
                sender: null,
                type: constants_1.Type.COMMENT,
                message: notificationMessage,
            });
        }
        await this.Article.findByIdAndUpdate(article._id, { $set: { totalCommentaires: ++article.totalCommentaires } }, { new: true });
    }
    async approveOrDisapproveComment(commentId, approved) {
        const comment = await this.get(commentId);
        const article = await this.Article.findById(comment?.article);
        if (!article)
            throw new http_exception_1.default(404, 'Article not found');
        await this.Comment.findByIdAndUpdate(commentId, { $set: { approved } });
        if (approved === true && comment?.user) {
            const notificationMessage = `Your comment has been approved by the administrator`;
            await (0, socket_1.sendNotification)({
                receiver: comment?.user._id,
                sender: null,
                message: notificationMessage,
                type: constants_1.Type.COMMENT,
                link: article?.versions[0]?.language === 'en'
                    ? `${process.env.LOGIN_LINK}blog/${article?.versions[0]?.url}`
                    : `${process.env.LOGIN_LINK}${article?.versions[0]?.language}/blog/${article?.versions[0]?.url}`,
            });
        }
        return approved ? `Comment ${commentId} approved successfully` : `Comment ${commentId} disapproved successfully`;
    }
    async getAllCommentsByArticle(articleId, queries) {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, firstName, createdAt, sortOrder = 'desc', approved } = queries;
        const queryConditions = { article: articleId, approved: true };
        const article = await this.Article.findById(articleId);
        if (!article)
            throw new http_exception_1.default(404, 'Article not found');
        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }
        if (firstName)
            queryConditions['firstName'] = new RegExp(`.*${firstName}.*`, 'i');
        if (approved !== undefined)
            queryConditions['approved'] = approved;
        const sortCriteria = {};
        if (sortOrder)
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        let commentsPaginated = await this.Comment.find(queryConditions)
            .lean()
            .populate([
            { path: 'user', select: 'firstName lastName email profilePicture' },
            {
                path: 'responses',
                match: { approved: true },
                populate: { path: 'user', select: 'firstName lastName email profilePicture' },
            },
        ])
            .sort(sortCriteria);
        // avoid repetition of responses appearing in the list of comments
        const responseIds = new Set(commentsPaginated.flatMap((comment) => comment.responses.filter((response) => response.approved === true).map((response) => response._id.toString())));
        const filteredComments = commentsPaginated.filter((comment) => !responseIds.has(comment._id.toString()));
        const totalComments = await this.Comment.countDocuments(queryConditions);
        if (paginated !== 'true') {
            const comments = filteredComments;
            return { totalComments, comments };
        }
        // pagination
        const paginatedComments = filteredComments.slice((pageNumber - 1) * pageSize, pageNumber * pageSize);
        const comments = paginatedComments;
        const totalPages = Math.ceil(Number(totalComments) / pageSize);
        return { totalComments, pageNumber, pageSize, totalPages, comments };
    }
    async getAllCommentsByArticleDashboard(articleId, queries) {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, firstName, createdAt, sortOrder = 'desc', approved } = queries;
        const queryConditions = { article: articleId };
        const article = await this.Article.findById(articleId);
        if (!article)
            throw new http_exception_1.default(404, 'Article not found');
        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }
        if (firstName)
            queryConditions['firstName'] = new RegExp(`.*${firstName}.*`, 'i');
        if (approved !== undefined)
            queryConditions['approved'] = approved;
        const sortCriteria = {};
        if (sortOrder)
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        let commentsPaginated = await this.Comment.find(queryConditions)
            .lean()
            .populate([
            { path: 'user', select: 'firstName lastName email profilePicture' },
            {
                path: 'responses',
                match: { approved: true },
                populate: { path: 'user', select: 'firstName lastName email profilePicture' },
            },
        ])
            .sort(sortCriteria);
        // avoid repetition of responses appearing in the list of comments
        const responseIds = new Set(commentsPaginated.flatMap((comment) => comment.responses.filter((response) => response.approved === true).map((response) => response._id.toString())));
        const filteredComments = commentsPaginated.filter((comment) => !responseIds.has(comment._id.toString()));
        const totalComments = await this.Comment.countDocuments(queryConditions);
        if (paginated !== 'true') {
            const comments = filteredComments;
            return { totalComments, comments };
        }
        // pagination
        const paginatedComments = filteredComments.slice((pageNumber - 1) * pageSize, pageNumber * pageSize);
        const comments = paginatedComments;
        const totalPages = Math.ceil(Number(totalComments) / pageSize);
        return { totalComments, pageNumber, pageSize, totalPages, comments };
    }
    async getAllComments(queries) {
        const { paginated = 'true', pageNumber = 1, pageSize = 10, keyWord, createdAt, sortOrder = 'desc', approved } = queries;
        const queryConditions = {};
        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }
        if (keyWord) {
            queryConditions['$or'] = [
                { 'user.firstName': new RegExp(`.*${keyWord}.*`, 'i') },
                { firstName: new RegExp(`.*${keyWord}.*`, 'i') },
                { email: new RegExp(`.*${keyWord}.*`, 'i') },
                { 'user.lastName': new RegExp(`.*${keyWord}.*`, 'i') },
                { note: new RegExp(`.*${keyWord}.*`, 'i') },
            ];
        }
        // if (firstName) queryConditions['firstName'] = new RegExp(`.*${firstName}.*`, 'i');
        if (approved !== undefined)
            queryConditions['approved'] = approved;
        const sortCriteria = {};
        if (sortOrder)
            sortCriteria['createdAt'] = sortOrder === 'asc' ? 1 : -1;
        let commentsPaginated = await this.Comment.find(queryConditions)
            .lean()
            .populate([
            { path: 'user', select: 'firstName lastName email profilePicture' },
            {
                path: 'responses',
                populate: { path: 'user', select: 'firstName lastName email profilePicture' },
            },
            { path: 'article', select: 'versions.url versions.title versions.language _id ' },
        ])
            .sort(sortCriteria);
        const totalComments = await this.Comment.countDocuments(queryConditions);
        if (paginated !== 'true') {
            return { totalComments, comments: commentsPaginated };
        }
        // pagination
        const paginatedComments = commentsPaginated.slice((pageNumber - 1) * pageSize, pageNumber * pageSize);
        const comments = paginatedComments;
        const totalPages = Math.ceil(Number(totalComments) / pageSize);
        return { totalComments, pageNumber, pageSize, totalPages, comments };
    }
    async addResponseToCommentService(commentId, responseData, currentUser) {
        let comment = await this.get(commentId);
        if (!comment)
            throw new http_exception_1.default(404, 'Comment not found');
        let articleComment = await this.Article.findById(comment?.article);
        if (!articleComment)
            throw new http_exception_1.default(404, 'Comment not found in this article');
        const responsePayload = {
            article: articleComment,
            comment: responseData.comment,
        };
        if (currentUser) {
            responsePayload.user = currentUser._id;
        }
        else {
            if (!responseData.email || !responseData.firstName) {
                throw new http_exception_1.default(400, 'Email & First Name must be provided!');
            }
            responsePayload.email = responseData.email;
            responsePayload.firstName = responseData.firstName;
        }
        const response = await this.Comment.create(responsePayload);
        comment.responses.push(response);
        articleComment.totalCommentaires = articleComment.totalCommentaires + 1;
        articleComment.save();
        await this.Comment.findByIdAndUpdate(commentId, comment);
        if (currentUser) {
            const notificationMessage = responseData?.language === 'en'
                ? `Your comment has been successfully added and is awaiting administrator approval`
                : `Votre commentaire a été ajouté avec succès et est en attente de l'approbation de l'administrateur.`;
            await (0, socket_1.sendNotification)({
                receiver: currentUser?._id,
                sender: null,
                type: constants_1.Type.COMMENT,
                message: notificationMessage,
            });
        }
    }
    async get(commentId) {
        const comment = await this.Comment.findById(commentId)
            .populate('article')
            .populate('user')
            .populate({
            path: 'responses',
            populate: [
                {
                    path: 'user',
                },
                {
                    path: 'article',
                },
            ],
        })
            .populate({
            path: 'responses.responses',
            populate: [
                {
                    path: 'user',
                },
                {
                    path: 'article',
                },
            ],
        });
        if (!comment)
            throw new http_exception_1.default(404, 'Comment not found');
        return comment;
    }
    async deleteComment(commentId, currentUser) {
        const comment = await this.get(commentId);
        if (comment.user?.toString() !== currentUser._id.toString()) {
            throw new http_exception_1.default(403, 'Unauthorized to delete this comment');
        }
        const collectAllSubCommentIds = async (commentId) => {
            const subCommentIds = [];
            const comment = await this.Comment.findById(commentId).lean();
            if (!comment || !comment.responses)
                return subCommentIds;
            for (const subComment of comment.responses) {
                const subCommentId = typeof subComment === 'string' ? subComment : subComment._id;
                if (subCommentId) {
                    subCommentIds.push(subCommentId);
                    const nestedIds = await collectAllSubCommentIds(subCommentId);
                    subCommentIds.push(...nestedIds);
                }
            }
            return subCommentIds;
        };
        const allSubCommentIds = await collectAllSubCommentIds(commentId);
        allSubCommentIds.push(commentId);
        await this.Comment.updateMany({ responses: commentId }, { $pull: { responses: commentId } });
        await this.Comment.deleteMany({ _id: { $in: allSubCommentIds } });
        await this.Article.findByIdAndUpdate(comment.article, {
            $inc: { totalCommentaires: -allSubCommentIds.length },
        });
    }
}
exports.default = CommentService;
//# sourceMappingURL=commentaire.service.js.map