import ViewPDF from "../../../../components/ViewPDF";
import initTranslations from "@/app/i18n";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }`;
  const languages = {
    fr: `https://www.pentabell.com/fr/`,
    en: `https://www.pentabell.com/`,
    "x-default": `https://www.pentabell.com/`,
  };

  const { t } = await initTranslations(locale, [
    "homePage",
    "global",
    "contactUs",
  ]);

  return {
    title:
      "Explore Our PFE Book 2024/2025 - Projects and Case Studies for Aspiring Engineers",
    description:
      "Discover inspiring Pentabell PFE projects. Dive into real-world applications, innovative solutions, and engineering excellence crafted by students and professionals.",
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}

export default function page() {
  return (
    <html lang="en">
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer', 'GTM-NXLL5DG')`,
          }}
        ></script>
      </head>
      <body>
        <noscript
          dangerouslySetInnerHTML={{
            __html: `<iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NXLL5DG" height="0" width="0" style="display:none;visibility:hidden"></iframe>`,
          }}
        ></noscript>
        <ViewPDF />
      </body>
    </html>
  );
}
