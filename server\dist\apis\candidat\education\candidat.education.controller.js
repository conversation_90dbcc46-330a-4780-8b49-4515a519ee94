"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_middleware_1 = __importDefault(require("@/middlewares/authentication.middleware"));
const candidat_education_service_1 = __importDefault(require("./candidat.education.service"));
const authorization_middleware_1 = require("@/middlewares/authorization.middleware");
const constants_1 = require("@/utils/helpers/constants");
const cache_middleware_1 = require("@/middlewares/cache.middleware");
const validateApiKey_middleware_1 = __importDefault(require("@/middlewares/validateApiKey.middleware"));
class CandidateEducationController {
    constructor() {
        this.path = '/candidates/educations';
        this.router = (0, express_1.Router)();
        this.candidateEducationService = new candidat_education_service_1.default();
        this.add = async (request, response, next) => {
            try {
                const id = request.user._id;
                const education = request.body;
                const result = await this.candidateEducationService.add(id, education);
                response.send(result);
            }
            catch (error) {
                next(error);
            }
        };
        this.update = async (request, response, next) => {
            try {
                const id = request.user._id;
                const educationId = request.params.educationId;
                const education = request.body;
                await this.candidateEducationService.update(id, educationId, education);
                response.send({ message: "Candidate's education updated successfully" });
            }
            catch (error) {
                next(error);
            }
        };
        this.getAll = async (request, response, next) => {
            try {
                const candidateId = request.user._id;
                const educations = await this.candidateEducationService.getAll(candidateId);
                response.json(educations);
            }
            catch (error) {
                next(error);
            }
        };
        this.delete = async (request, response, next) => {
            try {
                const id = request.user._id;
                const educationId = request.params.educationId;
                await this.candidateEducationService.delete(id, educationId);
                response.send({ message: "Candidate's education deleted successfully" });
            }
            catch (error) {
                next(error);
            }
        };
        this.initialiseRoutes();
    }
    initialiseRoutes() {
        this.router.post(`${this.path}`, validateApiKey_middleware_1.default, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), cache_middleware_1.invalidateCache, this.add);
        this.router.get(`${this.path}`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), this.getAll);
        this.router.put(`${this.path}/:educationId`, authentication_middleware_1.default, (0, authorization_middleware_1.hasRoles)([constants_1.Role.CANDIDATE]), cache_middleware_1.invalidateCache, this.update);
        this.router.delete(`${this.path}/:educationId`, authentication_middleware_1.default, cache_middleware_1.invalidateCache, this.delete);
    }
}
exports.default = CandidateEducationController;
//# sourceMappingURL=candidat.education.controller.js.map