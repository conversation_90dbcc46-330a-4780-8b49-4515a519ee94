"use client";
import { useState, useEffect, useRef } from "react";
import { Container, Box, Grid } from "@mui/material";

import s1 from "@/assets/images/website/coporate-profile/slides/s1.png";
import s2 from "@/assets/images/website/coporate-profile/slides/s2.png";
import s3 from "@/assets/images/website/coporate-profile/slides/s3.png";
import s4 from "@/assets/images/website/coporate-profile/slides/s4.png";
import s5 from "@/assets/images/website/coporate-profile/slides/s5.png";
import CustomButton from "../ui/CustomButton";
import SDGs from "@/assets/images/website/coporate-profile/SDGs.png";
import iso from "@/assets/images/website/coporate-profile/iso.png";

import ArrowLeft from "@/assets/images/icons/arrow-left.svg";
import ArrowRight from "@/assets/images/icons/arrow-right.svg";

const images = [s1.src, s2.src, s3.src, s4.src, s5.src];

function SustinabilitySection() {
  const [current, setCurrent] = useState(0);
  const intervalRef = useRef(null);

  const startAutoplay = () => {
    stopAutoplay();
    intervalRef.current = setInterval(() => {
      setCurrent((prev) => (prev + 1) % images.length);
    }, 5000);
  };

  const stopAutoplay = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
  };

  useEffect(() => {
    startAutoplay();
    return () => stopAutoplay();
  }, []);

  const handlePrev = () => {
    setCurrent((prev) => (prev - 1 + images.length) % images.length);
    startAutoplay();
  };

  const handleNext = () => {
    setCurrent((prev) => (prev + 1) % images.length);
    startAutoplay();
  };

  return (
    <div>
      <Box id="sustinability-section">
        <Box
          className="sustinability-slides"
          sx={{
            backgroundImage: `url(${images[current]})`,
          }}
        />

        <Container className="relative-section custom-max-width">
          <Grid
            container
            spacing={4}
            alignItems="flex-end"
            height={"100%"}
            className="m-auto"
            justifyContent={"space-between"}
          >
            <Grid item xs={12} md={6} sx={{ zIndex: 1 }}>
              <Box className="relative-div">
                <p className="heading-h3 text-white text-Heading semi-bold">
                  Pentabell Commitment to People, Partners & Sustainability{" "}
                </p>
                <Box
                  sx={{
                    display: "flex",
                    gap: 2,
                    mt: 4,
                  }}
                >
                  <CustomButton
                    onClick={handlePrev}
                    icon={<ArrowLeft />}
                    className={`slider-control`}
                  />
                  <CustomButton
                    onClick={handleNext}
                    icon={<ArrowRight />}
                    className={`slider-control`}
                  />
                </Box>
              </Box>
            </Grid>

            <Grid item xs={0} md={4} className="why-matters-section d-none-sm">
              <p className="sub-heading text-white semi-bold">
                The Bigger Picture
              </p>
              <p className="paragraph text-white">
                Global workforce solutions are fundamentally interrelated with
                human dignity & economic equity.
              </p>

              <p className="paragraph text-white">
                Pentabell operations impact hundreds of lives every day. We are
                investing in social sustainability, not only to reduce risk
                categories like discrimination and unsafe work conditions, but
                to unfold potential-- to bring workforce systems to life as
                singular pathways of opportunity.
              </p>
              <p className="paragraph text-white">
                Any action we take is a pebble in a pond, and it actively shapes
                how an entire industry hires, leads, and takes care of its
                workers in an ever-changing ecosystem.
              </p>

              <p className="sub-heading text-white semi-bold">SDGS</p>
              <img src={SDGs.src} alt="sdg5" height={50} />

              <p className="sub-heading text-white semi-bold">ISO</p>
              <img src={iso.src} alt="iso1" height={50} />
            </Grid>
          </Grid>
        </Container>
      </Box>
      <Container>
        <Grid container className="why-matters-section d-block-sm">
          <p className="sub-heading text-white semi-bold">The Bigger Picture</p>
          <p className="paragraph text-white">
            Global workforce solutions are fundamentally interrelated with human
            dignity & economic equity.
          </p>

          <p className="paragraph text-white">
            Pentabell operations impact hundreds of lives every day. We are
            investing in social sustainability, not only to reduce risk
            categories like discrimination and unsafe work conditions, but to
            unfold potential-- to bring workforce systems to life as singular
            pathways of opportunity.
          </p>
          <p className="paragraph text-white">
            Any action we take is a pebble in a pond, and it actively shapes how
            an entire industry hires, leads, and takes care of its workers in an
            ever-changing ecosystem.
          </p>

          <p className="sub-heading text-white semi-bold">SDGS</p>
          <img src={SDGs.src} alt="sdg5" height={50} />

          <p className="sub-heading text-white semi-bold">ISO</p>
          <img src={iso.src} alt="iso1" height={50} />
        </Grid>
      </Container>
    </div>
  );
}

export default SustinabilitySection;
