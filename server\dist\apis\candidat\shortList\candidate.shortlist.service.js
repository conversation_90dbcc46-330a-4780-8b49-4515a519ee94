"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const opportunity_model_1 = __importDefault(require("../../opportunity/model/opportunity.model"));
const candidat_model_1 = __importDefault(require("../candidat.model"));
const user_model_1 = __importDefault(require("../../user/user.model"));
const messages_1 = require("@/utils/helpers/messages");
const article_model_1 = __importDefault(require("@/apis/article/article.model"));
class ShortlistService {
    constructor() {
        this.Opportunity = opportunity_model_1.default;
        this.Article = article_model_1.default;
        this.Candidate = candidat_model_1.default;
        this.User = user_model_1.default;
    }
    async addOpportunityToShortlist(opportunityId, currentUser) {
        const candidate = await this.Candidate.findOne({ user: currentUser._id });
        const opportunity = await this.Opportunity.findById(opportunityId);
        if (!opportunity) {
            throw new http_exception_1.default(404, messages_1.MESSAGES.CANDIDATE_SHORTLIST.OPPORTUNITY_NOT_FOUND);
        }
        const existingOpportunity = await this.Candidate.findOne({ _id: candidate?._id, shortList: opportunityId });
        if (existingOpportunity) {
            throw new http_exception_1.default(409, messages_1.MESSAGES.CANDIDATE_SHORTLIST.ALREADY_APPLIED);
        }
        return await this.Candidate.findByIdAndUpdate(candidate?._id, { $push: { shortList: opportunity?._id } });
    }
    async getAllOpportunityinShortList(queries, currentUserId) {
        const candidate = await this.Candidate.findOne({ user: currentUserId });
        const shortlist = candidate?.shortList;
        const { paginated, keyWord, industry, genre, createdAt, country, sortOrder, isPublished, } = queries;
        const pageNumber = Number(queries.pageNumber) || 1;
        const pageSize = Number(queries.pageSize) || 8;
        const queryConditions = {};
        if (isPublished !== undefined)
            queryConditions['OpportunityVersions.isPublished'] = true;
        if (createdAt) {
            const date = new Date(createdAt);
            const nextYear = date.getFullYear() + 1;
            const antDate = new Date(nextYear, 0, 0, 24, 59, 59, 999);
            const isoDate = date.toISOString();
            const isoAntDate = antDate.toISOString();
            queryConditions['createdAt'] = {
                $gte: isoDate,
                $lte: isoAntDate,
            };
        }
        if (genre)
            queryConditions['genre'] = genre;
        if (industry) {
            queryConditions['industry'] = industry;
        }
        if (country) {
            queryConditions['country'] = country;
        }
        const shortlistCondition = shortlist ? { _id: { $in: shortlist } } : {};
        const opportunitySearchRequest = keyWord
            ? {
                $text: { $search: keyWord },
                ...shortlistCondition,
                ...queryConditions,
            }
            : { ...shortlistCondition, ...queryConditions };
        const opportunityMetadata = keyWord
            ? {
                score: { $meta: 'textScore' },
            }
            : '';
        const sortCriteria = {
            ...opportunityMetadata,
            createdAt: sortOrder === 'asc' ? 1 : -1,
        };
        if (paginated && paginated === 'false') {
            return await this.Opportunity.find(opportunitySearchRequest, opportunityMetadata)
                .sort(sortCriteria)
                .select('versions jobDescription industry country status genre createdAt dateOfExpiration');
        }
        const totalOpportunities = await this.Opportunity.countDocuments(opportunitySearchRequest);
        const totalPages = Math.ceil(totalOpportunities / pageSize);
        const Opportunities = await this.Opportunity.find(opportunitySearchRequest, opportunityMetadata)
            .sort(sortCriteria)
            .select('versions jobDescription industry country status genre createdAt dateOfExpiration')
            .skip((pageNumber - 1) * pageSize)
            .limit(pageSize || 20);
        return {
            pageNumber,
            pageSize,
            totalPages,
            totalOpportunities,
            Opportunities,
        };
    }
    async deleteFromShortlist(currentUserId, opportunityId) {
        const opportunityInShortList = await this.Candidate.findOne({ user: currentUserId, shortList: opportunityId });
        if (!opportunityInShortList)
            throw new http_exception_1.default(404, messages_1.MESSAGES.CANDIDATE_SHORTLIST.OPPORTUNITY_NOT_FOUND);
        await this.Candidate.findByIdAndUpdate(opportunityInShortList._id, { $pull: { shortList: opportunityId } });
    }
}
exports.default = ShortlistService;
//# sourceMappingURL=candidate.shortlist.service.js.map