"use client";

import { Container, Grid, useMediaQuery, useTheme } from "@mui/material";
import Link from "next/link";
import bannerImg from "@/assets/images/website/coporate-profile/CoProfileBanner.png";
import earth from "@/assets/images/website/coporate-profile/earth.png";

import SvgUploadPic from "@/assets/images/icons/arrowLeft2.svg";

function CoProfileBanner() {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
    return (
        <div
            id="banner-component"
            className="coporate-profile-banner"
            style={{
                backgroundImage: `url(${!isMobile ? bannerImg.src : bannerImg.src})`,
            }}
        >
            <Container
                className="custom-max-width"
                sx={{
                    height: "100%",
                    display: "flex",
                    alignItems: "flex-end",
                }}
            >
                <Grid
                    container
                    sx={{ height: "100%", display: "flex", alignItems: "flex-end", color: "white" }}
                >
                    <Grid id="success-nbrs" container >
                        <Grid item xs={12} sm={4} order={{ xs: 1, sm: 3 }} container justifyContent="center">
                            <img
                                width={"100%"}
                                height={"auto"}
                                alt={"Pentabell"}
                                src={earth.src}
                                loading="lazy"
                                className="earth-img"
                            />
                        </Grid>
                        <Grid item xs={0} sm={1} order={{ xs: 3, sm: 2 }}></Grid>
                        <Grid item xs={4} sm={1} order={{ xs: 2, sm: 1 }} className="nbr-element align-end"
                        >
                            <p className="nbr heading-h2 text-white text-center bold mb-0 mt-0">
                                15K+
                            </p>
                            <p className="nbr-txt paragraph text-center text-white semi-bold">
                                Hired Experts
                            </p>
                            <div className="line" />
                        </Grid>
                        <Grid item xs={4} sm={1} order={{ xs: 3, sm: 2 }} className="nbr-element align-end-mobile">
                            <p className="nbr heading-h2 text-white text-center bold mb-0 mt-0 text-center">
                                61
                            </p>
                            <p className="nbr-txt paragraph text-center text-white semi-bold text-center">
                                Nationalities
                            </p>
                            <div className="line" />
                        </Grid>
                        <Grid item xs={4} sm={1} order={{ xs: 4, sm: 4 }} className="nbr-element">
                            <p className="nbr heading-h2 text-white text-center bold mb-0 mt-0">
                                17K+
                            </p>
                            <p className="nbr-txt paragraph text-center text-white semi-bold">
                                Missions Completed
                            </p>
                            <div className="line" />
                        </Grid>
                        <Grid item xs={4} sm={1} order={{ xs: 5, sm: 5 }} className="nbr-element align-end">
                            <p className="nbr heading-h2 text-white text-center bold mb-0 mt-0 text-center">
                                265K+
                            </p>
                            <p className="nbr-txt paragraph text-center text-white semi-bold text-center">
                                CVs
                            </p>
                            <div className="line" />
                        </Grid>
                        <Grid item xs={2} sm={0} order={{ xs: 6, sm: 6 }} className="d-block d-sm-none"></Grid>

                        <Grid item xs={4} sm={1} order={{ xs: 6, sm: 6 }} className="nbr-element">
                            <p className="nbr heading-h2 text-white text-center bold mb-0 mt-0 text-center">
                                187
                            </p>
                            <p className="nbr-txt paragraph text-center text-white semi-bold text-center">
                                Satisfied Clients
                            </p>
                            <div className="line" />
                        </Grid>
                    </Grid>
                    <Grid container id="overview-section">
                        <Grid item sm={6} md={7}>
                            <p className="paragraph text-white text-banner mt-0">
                            Celebrating 20 Years of Building Trust, Sustaining Growth, and Shaping Futures
                            </p>
                        </Grid>
                        <Grid item sm={3} md={3} className="center-btn">
                            <Link
                                href={`#coporateProfileForm`}
                                style={{ textDecoration: "none", width: "fit-content" }}
                                className={"btn btn-filled bold left-icon full-width"}
                            >
                                <SvgUploadPic /> Corporate profile 2025
                            </Link>
                        </Grid>
                    </Grid>
                </Grid>
            </Container>
        </div>
    );
}

export default CoProfileBanner;
