"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const candidat_service_1 = __importDefault(require("../candidat.service"));
const candidat_education_model_1 = __importDefault(require("./candidat.education.model"));
const http_exception_1 = __importDefault(require("@/utils/exceptions/http.exception"));
const candidat_model_1 = __importDefault(require("../candidat.model"));
class CandidateEducationService {
    constructor() {
        this.Candidate = candidat_model_1.default;
        this.candidateService = new candidat_service_1.default();
    }
    async update(candidateId, educationId, eduactionData) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const educationIndex = candidate.educations.findIndex((eduId) => eduId._id.toString() === educationId);
        if (educationIndex !== -1) {
            const educationExisting = await candidat_education_model_1.default.findById(educationId);
            if (educationExisting) {
                educationExisting.degree = eduactionData.degree;
                educationExisting.startDate = eduactionData.startDate;
                educationExisting.endDate = eduactionData.endDate;
                educationExisting.university = eduactionData.university;
                educationExisting.fieldOfStudy = eduactionData.fieldOfStudy;
                await educationExisting.save();
            }
        }
        else
            throw new http_exception_1.default(404, 'Education not found');
    }
    async add(candidateId, educationData) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const newEducation = new candidat_education_model_1.default(educationData);
        await newEducation.save();
        candidate.educations = candidate.educations || [];
        candidate.educations.push(newEducation._id);
        candidate.numberOfEducations = Math.max((candidate.numberOfEducations || 0) + 1);
        await this.Candidate.findByIdAndUpdate(candidate._id, { $set: candidate });
        const { completionPercentage } = await this.candidateService.calculateProfileCompletion(candidateId);
        await this.Candidate.findOneAndUpdate({ user: candidateId }, { $set: { profilePercentage: completionPercentage } });
    }
    async delete(candidateId, educationId) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const educationIndex = candidate.educations.findIndex((eduId) => eduId._id.toString() === educationId);
        if (educationIndex !== -1) {
            candidate.educations.splice(educationIndex, 1);
            await candidat_education_model_1.default.findByIdAndDelete(educationId);
            candidate.numberOfEducations = Math.max((candidate.numberOfEducations || 0) - 1);
            await this.Candidate.findByIdAndUpdate(candidate._id, { $set: candidate });
        }
        const { completionPercentage } = await this.candidateService.calculateProfileCompletion(candidateId);
        await this.Candidate.findOneAndUpdate({ user: candidateId }, { $set: { profilePercentage: completionPercentage } });
    }
    async getAll(candidateId) {
        const candidate = await this.candidateService.getByUser(candidateId);
        const educationsDetails = [];
        for (const education of candidate.educations) {
            const educationDetail = await candidat_education_model_1.default.findById(education._id);
            if (educationDetail) {
                educationsDetails.push(educationDetail);
            }
        }
        return educationsDetails;
    }
}
exports.default = CandidateEducationService;
//# sourceMappingURL=candidat.education.service.js.map