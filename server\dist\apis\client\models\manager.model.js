"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("@/utils/helpers/constants");
const managerSchema = new mongoose_1.Schema({
    firstName: {
        type: String,
    },
    lastName: {
        type: String,
    },
    email: {
        type: String,
        unique: true,
    },
    phone: {
        type: String,
        minlength: 8,
    },
    country: {
        type: String,
        enum: constants_1.Countries,
    },
    jobPosition: {
        type: String,
    },
    client: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Client', required: true },
}, {
    timestamps: true,
});
exports.default = (0, mongoose_1.model)('Manager', managerSchema);
//# sourceMappingURL=manager.model.js.map