"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MESSAGES = void 0;
exports.MESSAGES = {
    SEOTAGS: {
        SEO_NOT_FOUND: 'This slug does not exist: ',
        SEO_ID_NOT_FOUND: 'This Seo tags does not exist: ',
    },
    EVENT: {
        EVENT_Not_FOUND: 'This Event does not exist: '
    },
    CANDIDATE_SHORTLIST: {
        OPPORTUNITY_DELETED: 'Opportunity deleted from shortList successfully',
        OPPORTUNITY_NOT_FOUND: 'This opportunity does not exist',
        ALREADY_APPLIED: 'You have already added this opportunity to your shortList.',
    },
    RECRUITER_SHORTLIST: {
        CANDIDATE_DELETED: 'Candidate deleted from shortList successfully',
        CANDIDATE_NOT_FOUND: 'Candidate not found',
        ALREADY_APPLIED: 'You have already added this candidate to your shortList.',
    },
    CLIENTS: {
        CLIENTS_IMPORTED: 'Clients have been imported successfully',
        CLIENT_DELETED: 'Client deleted successfully',
        CLIENT_NOT_FOUND: 'Client not found',
        ALREADY_EXIST: 'Client already exists !',
    },
    MANAGERS: {
        MANAGERS_IMPORTED: 'Managers have been imported successfully',
        MANAGER_DELETED: 'Manager deleted successfully',
        MANAGER_NOT_FOUND: 'Manager not found',
        ALREADY_EXIST: 'Manager already exists !',
    },
    OPPORTUNITY: {
        OPPORTUNITIES_IMPORTED: 'Opportunities have been imported successfully',
        DELETED: 'Opportunity deleted successfully',
        NOT_FOUND: 'Opportunity not found',
        NO_RECRUITER: "There's no recruiter with this id",
    },
    APPLICATION: {
        DELETED: 'Application deleted successfully',
        NOT_FOUND: 'Application not found',
    },
    Comment: {
        DELETED: 'Comment deleted successfully',
        NOT_FOUND: 'Comment not found',
    },
    AUTH: {
        LOGOUT: 'User logged out successfully',
        RESET: ' Password reset link sent to ',
        RESET_SUCCESSFUL: 'Password reset was successful',
        EMAIL_ALREADY_EXIST: 'Email already exist',
        PASSWORD_INCORRECT: 'Incorrect password',
        ARCHIVED: "You can't sign in because your account is archived",
        INVALID_TOKEN: 'Invalid token',
        INVALID_Email: 'Invalid email',
        ACCOUNT_NOT_ACTIVATED: 'Your account is not activated yet. Please activate your account before signing in.',
        Account_activated: 'Account already activated.',
    },
    FILE: {
        UPLOADED: 'File uploaded successfully',
        ERROR_UPLOAD: 'Error uploading file',
        NO_FILE: 'No file uploaded',
    },
    USER: {
        EMAIL_UNIQUE: 'Email must be unique',
        USER_NOT_FOUND: 'User not found',
        USER_CREATED: 'User created successfully',
        USER_UPDATED: 'User updated successfully',
        USER_DELETED: 'User deleted successfully',
    },
    CANDIDATE: {
        NOT_FOUND: 'Candidate not found',
        EMAILS: 'Emails must be as an array.',
        SKILLS: 'Skills must be as an array.',
        PHONES: 'phones must be  as an array.',
        ARCHIVED: 'Candidate successfully archived',
    },
    ALERT: {
        ADDED: 'Alert added successfully',
        DISABLED: 'Alert disabled successfully',
        ENABLED: 'Alert enabled successfully',
        NOT_FOUND: 'Alert not found',
        ALREADY_EXIST: 'Alert already exist !',
    },
    Category: {
        Not_found: 'Category not found',
        Already_exist: 'Category name already exist',
    },
};
//# sourceMappingURL=messages.js.map